"use client";

import { LockKeyhole, MoreVertical, Archive, Trash2 } from "lucide-react";
import { ConversationData } from "@/lib/types";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ConversationActionDialog } from "@/components/ui/conversation-action-dialog";
import { useState } from "react";

interface ConversationItemProps {
  conversation: ConversationData;
  isActive: boolean;
  onClick: () => void;
  onDelete?: (conversationId: string) => Promise<void>;
  onArchive?: (conversationId: string) => Promise<void>;
}

export default function ConversationItem({ conversation, isActive, onClick, onDelete, onArchive }: ConversationItemProps) {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogAction, setDialogAction] = useState<'delete' | 'archive'>('delete');
  const [isLoading, setIsLoading] = useState(false);
  
  const baseClasses = "flex flex-col justify-between group cursor-pointer transition-all duration-200 p-3 border-b border-gray-100 hover:bg-gray-50";
  const activeClasses = isActive ? "bg-[#ECF4E9] border-l-4 border-l-[#1E4841]" : "bg-white";
  const unreadClasses = conversation.isUnread ? "font-medium" : "";
  const classNames = `${baseClasses} ${activeClasses} ${unreadClasses}`.trim();
  const avatarBg = conversation.avatarBg || "bg-[#BBF49C]";
  
  const handleAction = async () => {
    setIsLoading(true);
    try {
      if (dialogAction === 'delete' && onDelete) {
        await onDelete(conversation.id);
      } else if (dialogAction === 'archive' && onArchive) {
        await onArchive(conversation.id);
      }
    } finally {
      setIsLoading(false);
      setDialogOpen(false);
    }
  };
  
  const openDialog = (action: 'delete' | 'archive', e: React.MouseEvent) => {
    e.stopPropagation();
    setDialogAction(action);
    setDialogOpen(true);
  };

  return (
    <div
      className={classNames}
      onClick={onClick}
      role="button"
      tabIndex={0}
      aria-label={`Conversation with ${conversation.name} about ${conversation.caseId}`}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onClick();
        }
      }}
    >
      <div className="flex items-center gap-3 mb-2">
        <div className="relative">
          <div className={`h-10 w-10 rounded-full flex items-center justify-center ${avatarBg}`}>
            <span className="text-sm font-medium text-[#1E4841]">
              {conversation.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
            </span>
          </div>
          {conversation.isOnline && (
            <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white rounded-full animate-pulse"></div>
          )}
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <p className="text-sm font-medium text-[#111827] truncate">
                {conversation.name}
              </p>
              {conversation.isOnline ? (
                <span className="text-xs text-green-600 font-medium flex items-center gap-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  Active
                </span>
              ) : (
                <span className="text-xs text-gray-400 font-medium">Offline</span>
              )}
            </div>
            <div className="flex items-center gap-2">
              <p className="text-xs text-[#6B7280]">{conversation.time}</p>
              {conversation.isUnread && (
                <div className="w-2.5 h-2.5 bg-[#EF4444] rounded-full"></div>
              )}
              {(onDelete || onArchive) && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {onArchive && (
                      <DropdownMenuItem onClick={(e) => openDialog('archive', e)}>
                        <Archive className="mr-2 h-4 w-4" />
                        Archive
                      </DropdownMenuItem>
                    )}
                    {onDelete && (
                      <DropdownMenuItem 
                        onClick={(e) => openDialog('delete', e)}
                        className="text-red-600 focus:text-red-600"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </div>
          </div>
          <p className="text-xs text-[#6B7280] mb-1">{conversation.caseId}</p>
        </div>
      </div>
      <div className="flex items-center justify-between">
        <p className="text-sm text-[#4B5563] truncate flex-1 mr-2">
          {conversation.isTyping ? (
            <span className="text-green-600 italic">Typing...</span>
          ) : (
            conversation.lastMessage
          )}
        </p>
        <div className="flex items-center gap-1">
          <LockKeyhole className="w-3 h-3 text-[#1E4841]" />
        </div>
      </div>
      
      <ConversationActionDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        action={dialogAction}
        conversationName={conversation.name}
        onConfirm={handleAction}
        isLoading={isLoading}
      />
    </div>
  );
}