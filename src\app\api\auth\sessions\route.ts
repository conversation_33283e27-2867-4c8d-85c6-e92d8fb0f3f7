import { NextRequest, NextResponse } from 'next/server';
import { validateSessionToken, getUserActiveSessions, revokeSessionToken, revokeAllUserSessions } from '@/lib/auth/session-manager';
import { AuthAuditLogger } from '@/lib/security/audit-logger';

// Get user's active sessions
export async function GET(request: NextRequest) {
  try {
    // Get token from Authorization header
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : null;

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Validate the session token
    const sessionValidation = await validateSessionToken(token);
    
    if (!sessionValidation.valid || !sessionValidation.decoded) {
      return NextResponse.json(
        { success: false, error: 'Invalid session' },
        { status: 401 }
      );
    }

    const decoded = sessionValidation.decoded as { userId: string };
    const { userId } = decoded;
    
    // Get all active sessions for the user
    const sessions = await getUserActiveSessions(userId);
    
    // Format sessions for response (hide sensitive data)
    const formattedSessions = sessions.map(session => ({
      tokenId: session.tokenId,
      userAgent: session.userAgent || 'Unknown',
      ipAddress: session.ipAddress ? 
        session.ipAddress.replace(/\.\d+$/, '.***') : // Mask last octet for privacy
        'Unknown',
      createdAt: session.createdAt,
      lastActivity: session.lastActivity,
      isCurrent: session.tokenId === (sessionValidation.decoded as { tokenId: string }).tokenId
    }));

    return NextResponse.json({
      success: true,
      sessions: formattedSessions,
      totalSessions: formattedSessions.length
    });

  } catch {
    return NextResponse.json(
      { success: false, error: 'Failed to retrieve sessions' },
      { status: 500 }
    );
  }
}

// Revoke specific session or all sessions
export async function DELETE(request: NextRequest) {
  try {
    // Get token from Authorization header
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : null;

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Validate the session token
    const sessionValidation = await validateSessionToken(token);
    
    if (!sessionValidation.valid || !sessionValidation.decoded) {
      return NextResponse.json(
        { success: false, error: 'Invalid session' },
        { status: 401 }
      );
    }

    const decoded2 = sessionValidation.decoded as { userId: string; email: string; tokenId: string };
    const { userId, email, tokenId: currentTokenId } = decoded2;
    
    // Parse request body
    const body = await request.json();
    const { action, tokenId } = body;

    if (action === 'revoke_all') {
      // Revoke all sessions for the user
      const revokedCount = await revokeAllUserSessions(userId, 'user_revoke_all');
      
      // Log the action
      await AuthAuditLogger.logLogout(request, userId, email);
      
      return NextResponse.json({
        success: true,
        message: `Revoked ${revokedCount} sessions`,
        revokedCount
      });
      
    } else if (action === 'revoke_session' && tokenId) {
      // Prevent user from revoking their current session via this endpoint
      if (tokenId === currentTokenId) {
        return NextResponse.json(
          { success: false, error: 'Cannot revoke current session. Use logout endpoint.' },
          { status: 400 }
        );
      }
      
      // Revoke specific session
      const revoked = await revokeSessionToken(tokenId, 'user_revoke_session');
      
      if (revoked) {
        return NextResponse.json({
          success: true,
          message: 'Session revoked successfully'
        });
      } else {
        return NextResponse.json(
          { success: false, error: 'Failed to revoke session' },
          { status: 500 }
        );
      }
      
    } else {
      return NextResponse.json(
        { success: false, error: 'Invalid action or missing tokenId' },
        { status: 400 }
      );
    }

  } catch {
    return NextResponse.json(
      { success: false, error: 'Failed to revoke session' },
      { status: 500 }
    );
  }
}
