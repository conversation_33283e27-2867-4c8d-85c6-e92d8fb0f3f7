import { NextResponse } from 'next/server';
import { DataRetentionService } from '@/lib/compliance/dataRetention';

export async function POST() {
  try {
    await DataRetentionService.cleanupExpiredData();
    
    return NextResponse.json({
      success: true,
      message: 'Data retention cleanup completed successfully'
    });
  } catch (error) {
    console.error('Data cleanup error:', error);
    return NextResponse.json({ error: 'Cleanup failed' }, { status: 500 });
  }
}

export async function GET() {
  try {
    const policies = ['reports', 'messages', 'audit_logs', 'user_data'].map(type => 
      DataRetentionService.getRetentionPolicy(type)
    );
    
    return NextResponse.json({
      success: true,
      policies: policies.filter(Boolean)
    });
  } catch {
    return NextResponse.json({ error: 'Failed to get policies' }, { status: 500 });
  }
}