/**
 * Testimonials Mock Data
 * Used for seeding the database and as fallback data
 */

export interface TestimonialData {
  id: string;
  name: string;
  role: string;
  company: string;
  content: string;
  rating: number;
  category: string;
  isActive: boolean;
  createdAt: Date;
  featured?: boolean;
  avatar?: string;
}

export const TESTIMONIALS_DATA: TestimonialData[] = [
  {
    id: "testimonial-001",
    name: "<PERSON>",
    role: "Chief Compliance Officer",
    company: "TechCorp Industries",
    content: "The 7IRIS platform has transformed how we handle whistleblower reports. The anonymous communication feature gives our employees confidence to speak up, and the investigation tools help us respond quickly and effectively.",
    rating: 5,
    category: "compliance",
    isActive: true,
    createdAt: new Date('2024-01-15'),
    featured: true,
    avatar: "/testimonials/sarah-johnson.jpg"
  },
  {
    id: "testimonial-002",
    name: "<PERSON>",
    role: "Internal Audit Director",
    company: "Global Manufacturing Ltd",
    content: "What impressed us most is the platform's security and ease of use. Our employees can report concerns without fear, and we can track everything through a comprehensive dashboard. It's been a game-changer for our ethics program.",
    rating: 5,
    category: "security",
    isActive: true,
    createdAt: new Date('2024-02-20'),
    featured: true,
    avatar: "/testimonials/michael-chen.jpg"
  },
  {
    id: "testimonial-003",
    name: "Emma Wilson",
    role: "HR Director",
    company: "Financial Services Inc",
    content: "The two-way anonymous communication feature is brilliant. It allows us to gather additional information while maintaining the reporter's anonymity. This has significantly improved our investigation quality.",
    rating: 5,
    category: "communication",
    isActive: true,
    createdAt: new Date('2024-03-10'),
    featured: false,
    avatar: "/testimonials/emma-wilson.jpg"
  },
  {
    id: "testimonial-004",
    name: "David Rodriguez",
    role: "Legal Counsel",
    company: "Healthcare Solutions Corp",
    content: "The compliance reporting and audit trail features are exceptional. We can demonstrate to regulators that we have robust processes in place, and the automated workflows save us countless hours.",
    rating: 4,
    category: "legal",
    isActive: true,
    createdAt: new Date('2024-03-25'),
    featured: false,
    avatar: "/testimonials/david-rodriguez.jpg"
  },
  {
    id: "testimonial-005",
    name: "Lisa Thompson",
    role: "Ethics & Compliance Manager",
    company: "Retail Chain Group",
    content: "Implementation was smooth, and the support team was fantastic. The platform integrates well with our existing systems, and the reporting analytics help us identify trends and improve our processes.",
    rating: 5,
    category: "implementation",
    isActive: true,
    createdAt: new Date('2024-04-05'),
    featured: true,
    avatar: "/testimonials/lisa-thompson.jpg"
  },
  {
    id: "testimonial-006",
    name: "James Park",
    role: "Risk Management Director",
    company: "Energy Solutions Ltd",
    content: "The real-time notifications and escalation features ensure nothing falls through the cracks. We can respond to critical issues immediately, which has significantly improved our risk management capabilities.",
    rating: 5,
    category: "risk-management",
    isActive: true,
    createdAt: new Date('2024-04-18'),
    featured: false,
    avatar: "/testimonials/james-park.jpg"
  },
  {
    id: "testimonial-007",
    name: "Maria Garcia",
    role: "Operations Manager",
    company: "Construction Corp",
    content: "Our employees feel more comfortable reporting safety concerns now. The anonymous option removes the fear of retaliation, and we've seen a significant increase in reports, helping us create a safer workplace.",
    rating: 4,
    category: "safety",
    isActive: true,
    createdAt: new Date('2024-05-02'),
    featured: false,
    avatar: "/testimonials/maria-garcia.jpg"
  },
  {
    id: "testimonial-008",
    name: "Robert Kim",
    role: "Chief Information Officer",
    company: "Tech Innovations Inc",
    content: "The security architecture is impressive. End-to-end encryption, secure file uploads, and comprehensive access controls give us confidence that sensitive information is protected throughout the process.",
    rating: 5,
    category: "technology",
    isActive: true,
    createdAt: new Date('2024-05-15'),
    featured: true,
    avatar: "/testimonials/robert-kim.jpg"
  },
  {
    id: "testimonial-009",
    name: "Jennifer Adams",
    role: "Compliance Specialist",
    company: "Pharmaceutical Group",
    content: "The case management features are outstanding. We can track investigations from start to finish, collaborate with team members, and generate comprehensive reports for stakeholders.",
    rating: 4,
    category: "case-management",
    isActive: true,
    createdAt: new Date('2024-05-28'),
    featured: false,
    avatar: "/testimonials/jennifer-adams.jpg"
  },
  {
    id: "testimonial-010",
    name: "Thomas Brown",
    role: "General Counsel",
    company: "International Trading Co",
    content: "The platform helps us meet regulatory requirements across multiple jurisdictions. The customizable workflows and reporting features adapt to different compliance frameworks seamlessly.",
    rating: 5,
    category: "regulatory",
    isActive: true,
    createdAt: new Date('2024-06-10'),
    featured: false,
    avatar: "/testimonials/thomas-brown.jpg"
  }
];

// Helper functions for filtering testimonials
export const getFeaturedTestimonials = () => 
  TESTIMONIALS_DATA.filter(testimonial => testimonial.featured && testimonial.isActive);

export const getTestimonialsByCategory = (category: string) =>
  TESTIMONIALS_DATA.filter(testimonial => testimonial.category === category && testimonial.isActive);

export const getActiveTestimonials = () =>
  TESTIMONIALS_DATA.filter(testimonial => testimonial.isActive);
