import { Schema } from 'mongoose';
import { createModel } from '../utils';

const NotificationSchema = new Schema({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  type: { 
    type: String, 
    enum: ['system', 'report_update', 'message', 'alert'],
    required: true 
  },
  title: { type: String, required: true },
  message: { type: String, required: true },
  status: { 
    type: String, 
    enum: ['read', 'unread'],
    default: 'unread'
  },
  priority: { 
    type: String, 
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },
  actionUrl: { type: String },
  reportId: { type: Schema.Types.ObjectId, ref: 'Report' },
  readAt: { type: Date },
  expiresAt: { type: Date },
  metadata: {
    source: { type: String },
    category: { type: String }
  }
}, {
  timestamps: true
});

// Use the utility function to create the model safely
const Notification = createModel('Notification', NotificationSchema);

export default Notification;