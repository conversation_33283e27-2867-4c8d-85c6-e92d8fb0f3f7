import { NextResponse } from 'next/server';
import { withCompanyIsolation, AuthenticatedRequest } from '@/lib/middleware/auth';
import { Escalation, Report } from '@/lib/db/models';
import { IEscalation } from '@/lib/db/models/interfaces';
import connectDB from '@/lib/db/mongodb';

interface EscalationQuery {
  companyId: string;
  status?: string;
  priority?: string;
}

interface PopulatedUser {
  firstName: string;
  lastName: string;
  email: string;
}

interface PopulatedReport {
  reportId: string;
  title: string;
  category: string;
}

interface PopulatedEscalation {
  escalatedBy?: PopulatedUser;
  escalatedTo?: PopulatedUser;
  reportId?: PopulatedReport;
}

interface CreateEscalationData {
  reportId: string;
  caseId?: string;
  subject: string;
  department: string;
  priority: string;
  reason: string;
  slaDeadline: string;
  escalatedTo?: string;
}

export const runtime = 'nodejs';

export const GET = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    if (!request.user) {
      return NextResponse.json(
        { success: false, error: 'User not authenticated' },
        { status: 401 }
      );
    }

    // Only admins and investigators can view escalations
    if (!['admin', 'investigator'].includes(request.user.role)) {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = parseInt(searchParams.get('offset') || '0');
    const status = searchParams.get('status');
    const priority = searchParams.get('priority');

    // Build query for company isolation
    const query: EscalationQuery = { companyId: request.user.companyId };
    
    if (status) {
      query.status = status;
    }
    
    if (priority) {
      query.priority = priority;
    }

    // Get escalations with populated references
    const escalations = await Escalation.find(query)
      .populate('reportId', 'reportId title category')
      .populate('escalatedBy', 'firstName lastName email')
      .populate('escalatedTo', 'firstName lastName email')
      .sort({ escalatedDate: -1 })
      .limit(limit)
      .skip(offset);

    // Transform data for frontend
    const transformedEscalations = escalations.map(escalation => {
      const esc = escalation as unknown as IEscalation;
      const populated = escalation as unknown as PopulatedEscalation;
      return {
        id: esc._id.toString(),
        caseId: esc.caseId,
        subject: esc.subject,
        department: esc.department,
        priority: esc.priority,
        escalatedDate: esc.escalatedDate.toISOString().split('T')[0], // Format as YYYY-MM-DD
        slaStatus: esc.slaStatus,
        daysOverdue: esc.daysOverdue,
        status: esc.status,
        reason: esc.reason,
        escalatedBy: populated.escalatedBy ? {
          name: `${populated.escalatedBy.firstName} ${populated.escalatedBy.lastName}`,
          email: populated.escalatedBy.email
        } : null,
        escalatedTo: populated.escalatedTo ? {
          name: `${populated.escalatedTo.firstName} ${populated.escalatedTo.lastName}`,
          email: populated.escalatedTo.email
        } : null,
        report: populated.reportId ? {
          id: populated.reportId.reportId,
          title: populated.reportId.title,
          category: populated.reportId.category
        } : null,
        resolution: esc.resolution,
        resolvedDate: esc.resolvedDate?.toISOString().split('T')[0],
        notes: esc.notes || []
      };
    });

    // Get total count for pagination
    const totalCount = await Escalation.countDocuments(query);

    return NextResponse.json({
      success: true,
      data: transformedEscalations,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + limit < totalCount
      }
    });
  } catch (error) {
    console.error('Escalations API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});

export const POST = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    if (!request.user) {
      return NextResponse.json(
        { success: false, error: 'User not authenticated' },
        { status: 401 }
      );
    }

    // Only admins can create escalations
    if (request.user.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Access denied. Admin role required.' },
        { status: 403 }
      );
    }

    const escalationData: CreateEscalationData = await request.json();
    
    // Validate required fields
    const requiredFields = ['reportId', 'subject', 'department', 'priority', 'reason', 'slaDeadline'];
    for (const field of requiredFields) {
      if (!escalationData[field]) {
        return NextResponse.json(
          { success: false, error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Verify the report exists and belongs to the same company
    const report = await Report.findOne({ 
      _id: escalationData.reportId,
      // Company isolation will be handled by the report's user's company
    });
    
    if (!report) {
      return NextResponse.json(
        { success: false, error: 'Report not found' },
        { status: 404 }
      );
    }

    // Generate case ID if not provided
    const caseId = escalationData.caseId || `WB-${new Date().getFullYear()}-${String(Math.floor(Math.random() * 1000)).padStart(3, '0')}`;

    // Create escalation
    const newEscalation = await Escalation.create({
      reportId: escalationData.reportId,
      companyId: request.user.companyId,
      caseId,
      subject: escalationData.subject,
      department: escalationData.department,
      priority: escalationData.priority,
      escalatedBy: request.user.id,
      escalatedTo: escalationData.escalatedTo,
      slaDeadline: new Date(escalationData.slaDeadline),
      reason: escalationData.reason,
      status: 'Open',
      metadata: {
        originalStatus: report.status,
        escalationLevel: 1,
        autoEscalated: false
      }
    });

    // Populate the created escalation for response
    const populatedEscalation = await Escalation.findById(newEscalation._id)
      .populate('reportId', 'reportId title category')
      .populate('escalatedBy', 'firstName lastName email')
      .populate('escalatedTo', 'firstName lastName email');

    const esc = populatedEscalation as unknown as IEscalation;

    return NextResponse.json({
      success: true,
      data: {
        id: esc._id.toString(),
        caseId: esc.caseId,
        subject: esc.subject,
        department: esc.department,
        priority: esc.priority,
        status: esc.status,
        escalatedDate: esc.escalatedDate.toISOString().split('T')[0],
        slaStatus: esc.slaStatus,
        daysOverdue: esc.daysOverdue
      },
      message: 'Escalation created successfully'
    }, { status: 201 });
  } catch (error) {
    console.error('Create escalation API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});
