import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import connectDB from '@/lib/db/mongodb';
import { Report, Message } from '@/lib/db/models';
import { SecurityAuditLogger } from '@/lib/security/audit-logger';

export const runtime = 'nodejs';

// Validation schema for sending anonymous messages
const sendMessageSchema = z.object({
  reportToken: z.string().min(1, 'Report token is required'),
  message: z.string().min(1, 'Message content is required').max(1000, 'Message too long')
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate the request body
    const validationResult = sendMessageSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Validation failed',
          details: validationResult.error.issues
        },
        { status: 400 }
      );
    }

    const { reportToken, message } = validationResult.data;

    await connectDB();

    // Find the report by token
    const report = await Report.findOne({ 
      referenceNumber: reportToken,
      isAnonymous: true 
    });

    if (!report) {
      // Log failed access attempt
      await SecurityAuditLogger.logSuspiciousActivity(
        request,
        'Invalid report token for message sending',
        {
          reportToken: reportToken,
          timestamp: new Date().toISOString()
        }
      );

      return NextResponse.json(
        { success: false, error: 'Invalid report token' },
        { status: 404 }
      );
    }

    // Create the message
    const newMessage = await Message.create({
      reportId: report._id,
      senderId: report.userId, // Anonymous user ID
      content: message.trim(),
      messageType: 'user_message',
      isAnonymous: true,
      metadata: {
        sentViaToken: true,
        reportToken: reportToken
      }
    });

    // Update report's last updated timestamp
    await Report.findByIdAndUpdate(report._id, {
      lastUpdated: new Date()
    });

    // Log successful message sending
    console.log('ANONYMOUS MESSAGE SENT:', {
      reportId: report.reportId,
      reportToken: reportToken,
      messageLength: message.length,
      timestamp: new Date().toISOString()
    });

    return NextResponse.json({
      success: true,
      data: {
        messageId: newMessage._id.toString(),
        message: 'Message sent successfully'
      }
    }, { status: 201 });

  } catch (error) {
    console.error('Send anonymous message error:', error);
    
    await SecurityAuditLogger.logSuspiciousActivity(
      request,
      'Anonymous message sending failed',
      {
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      }
    );

    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to send message. Please try again.' 
      },
      { status: 500 }
    );
  }
}
