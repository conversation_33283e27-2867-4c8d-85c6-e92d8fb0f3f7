"use client";

import { useState, useMemo } from "react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { faqData, helpFaqData } from "@/lib/staticContent";

// Use FAQ data from static content and help FAQ data
const allFAQs = [
  // Static content FAQs (pricing, support, etc.)
  ...faqData.map(faq => ({
    id: faq.id,
    question: faq.question,
    answer: faq.answer,
    category: faq.category,
    tags: [faq.category]
  })),
  // Help FAQ data (general, reporting, etc.)
  ...helpFaqData.general.map(faq => ({
    id: `general-${faq.id}`,
    question: faq.question,
    answer: faq.answer,
    category: 'general',
    tags: ['general', 'basics']
  })),
  ...helpFaqData.reporting.map(faq => ({
    id: `reporting-${faq.id}`,
    question: faq.question,
    answer: faq.answer,
    category: 'reporting',
    tags: ['reporting', 'submit']
  })),
  ...helpFaqData.security.map(faq => ({
    id: `security-${faq.id}`,
    question: faq.question,
    answer: faq.answer,
    category: 'security',
    tags: ['security', 'protection']
  }))
];

const categories = [
  { value: "all", label: "All Categories", count: 0 },
  { value: "general", label: "General", count: 0 },
  { value: "pricing", label: "Pricing", count: 0 },
  { value: "security", label: "Security", count: 0 },
  { value: "reporting", label: "Reporting", count: 0 },
  { value: "support", label: "Support", count: 0 },
  { value: "technical", label: "Technical", count: 0 }
];

interface FAQsContentProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
}

export default function FAQsContent({ searchTerm, onSearchChange }: FAQsContentProps) {
  const [activeTab, setActiveTab] = useState("all");

  // Calculate category counts
  const categoriesWithCounts = useMemo(() => {
    const counts = allFAQs.reduce((acc, faq) => {
      acc[faq.category] = (acc[faq.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return categories.map(cat => ({
      ...cat,
      count: cat.value === "all" ? allFAQs.length : (counts[cat.value] || 0)
    }));
  }, []);

  // Filter FAQs based on search term and active tab
  const filteredFAQs = useMemo(() => {
    return allFAQs.filter(faq => {
      const matchesSearch = searchTerm === "" ||
        faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
        faq.answer.toLowerCase().includes(searchTerm.toLowerCase()) ||
        faq.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));

      const matchesCategory = activeTab === "all" || faq.category === activeTab;

      return matchesSearch && matchesCategory;
    });
  }, [searchTerm, activeTab]);



  return (
    <section className="py-16 px-4 sm:px-8 md:px-12 lg:px-[180px]">
      <div className="max-w-6xl mx-auto">
        {/* Search Results Display */}
        {searchTerm && (
          <div className="text-center mb-8">
            <p className="text-gray-600 text-lg">
              {filteredFAQs.length} {filteredFAQs.length === 1 ? 'result' : 'results'} found for &ldquo;{searchTerm}&rdquo;
            </p>
            <Button
              variant="ghost"
              onClick={() => onSearchChange("")}
              className="text-[#1E4841] hover:bg-[#1E4841]/10 mt-2"
            >
              Clear search
            </Button>
          </div>
        )}

        {/* Tabs Section */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-7 mb-12 bg-gray-100 p-2 rounded-lg h-auto">
            {categoriesWithCounts.map((category) => (
              <TabsTrigger
                key={category.value}
                value={category.value}
                className="flex flex-col items-center gap-2 py-4 px-3 text-sm font-medium bg-transparent data-[state=active]:bg-[#1E4841] data-[state=active]:text-white data-[state=inactive]:text-gray-600 hover:bg-gray-200 data-[state=active]:hover:bg-[#1E4841] transition-all duration-200 rounded-md group"
              >
                <span className="truncate font-semibold">{category.label}</span>
                <Badge
                  variant="secondary"
                  className="text-xs group-data-[state=active]:bg-white/20 group-data-[state=active]:text-white bg-gray-200 text-gray-600 border-0 min-w-[24px] h-6 font-medium"
                >
                  {category.count}
                </Badge>
              </TabsTrigger>
            ))}
          </TabsList>

          {/* Tab Content */}
          {categoriesWithCounts.map((category) => (
            <TabsContent key={category.value} value={category.value} className="mt-0">
              {filteredFAQs.length === 0 ? (
                <div className="text-center py-12">
                  <p className="text-gray-500 text-lg mb-4">
                    {searchTerm
                      ? `No FAQs found matching &ldquo;${searchTerm}&rdquo; in ${category.label.toLowerCase()}.`
                      : `No FAQs available in ${category.label.toLowerCase()}.`
                    }
                  </p>
                  {searchTerm && (
                    <Button
                      onClick={() => onSearchChange("")}
                      className="bg-[#1E4841] hover:bg-[#1E4841]/90 text-white"
                    >
                      Clear search
                    </Button>
                  )}
                </div>
              ) : (
                <div className="space-y-4">
                  <Accordion type="single" collapsible className="w-full space-y-4">
                    {filteredFAQs.map((faq) => (
                      <AccordionItem
                        key={faq.id}
                        value={`item-${faq.id}`}
                        className="rounded-lg px-6 py-2 hover:shadow-sm transition-shadow border border-gray-200 bg-white"
                      >
                        <AccordionTrigger className="text-left hover:no-underline text-lg font-semibold text-[#242E2C]">
                          {faq.question}
                        </AccordionTrigger>
                        <AccordionContent className="text-[#242E2C] pt-2 pb-4">
                          {faq.answer}
                          <div className="flex flex-wrap gap-2 mt-3">
                            {faq.tags.map((tag) => (
                              <Badge key={tag} variant="outline" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                </div>
              )}
            </TabsContent>
          ))}
        </Tabs>

        {/* Contact Support Section */}
        <div className="mt-16 text-center bg-[#F9FAFB] rounded-lg p-8">
          <h3 className="text-2xl font-bold text-[#1E4841] mb-4">
            Still have questions?
          </h3>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Can&apos;t find the answer you&apos;re looking for? Our support team is here to help.
            Get in touch and we&apos;ll get back to you as soon as possible.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              asChild
              className="bg-[#1E4841] hover:bg-[#1E4841]/90 text-white px-6 py-3"
            >
              <a href="/contact">Contact Support</a>
            </Button>
            <Button 
              variant="outline" 
              asChild
              className="border-[#1E4841] text-[#1E4841] hover:bg-[#1E4841] hover:text-white px-6 py-3"
            >
              <a href="/contact">Schedule a Demo</a>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
