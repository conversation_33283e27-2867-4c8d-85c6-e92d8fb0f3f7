import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { ArrowRight } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent } from '@/components/ui/card';
import { BlogCard } from '@/lib/types';

interface BlogPostGridProps {
  posts: BlogCard[];
  title?: string;
  showViewAllButton?: boolean;
}

/**
 * BlogPostGrid component for displaying blog posts in a grid layout
 */
const BlogPostGrid: React.FC<BlogPostGridProps> = ({ 
  posts, 
  title = "Latest Articles", 
  showViewAllButton = false 
}) => {
  return (
    <section className="py-8 sm:py-10 md:py-12 bg-gray-50 w-full mt-8 sm:mt-10 md:mt-12">
      <div className="container mx-auto px-4 sm:px-6 md:px-8 lg:px-12 xl:px-[180px]">
        {title && (
          <h2 className="text-xl sm:text-2xl font-bold text-[#1E4841] mb-6 sm:mb-8">{title}</h2>
        )}
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
          {posts.map((post, index) => (
            <Card key={index} className="flex flex-col shadow-md hover:shadow-lg border-none transition-shadow duration-300 pt-0 h-full">
              <div className="aspect-[16/9] w-full overflow-hidden rounded-t-lg">
                <Image
                  src={post.image}
                  alt={post.title}
                  width={400}
                  height={225}
                  className="object-cover w-full h-full hover:scale-105 transition-transform duration-300"
                />
              </div>
              <CardContent className="space-y-3 sm:space-y-4 flex-1 flex flex-col p-3 sm:p-4 md:px-6">
                <div className="flex flex-wrap justify-between items-center gap-2">
                  <p className="text-xs font-medium text-[#1E4841] rounded-4xl py-1 px-2 bg-[#ECF4E9]">{post.category}</p>
                  <p className="text-xs text-[#1E4841]">{post.date}</p>
                </div>
                <h3 className="text-base sm:text-lg font-bold text-[#242E2C] hover:text-[#1E4841] line-clamp-2 transition-colors duration-300">
                  <Link href={`/blog/${post.id}`}>{post.title}</Link>
                </h3>
                <p className="text-[#4B5563] leading-relaxed text-sm sm:text-base line-clamp-2 sm:line-clamp-3 flex-1">
                  {post.description}
                </p>
                <div className="flex items-center justify-between w-full mt-auto flex-wrap gap-2">
                  <div className="flex items-center gap-2 sm:gap-3">
                    <Avatar>
                      <AvatarImage src={post.author.image} alt={post.author.name} />
                      <AvatarFallback>{post.author.initials}</AvatarFallback>
                    </Avatar>
                    <p className="text-sm text-[#4B5563]">{post.author.name}</p>
                  </div>
                  <p className="text-[#4B5563] text-xs">{post.readTime}</p>
                </div>
                <Link
                  href={`/blog/${post.id || post.slug || 'unknown'}`}
                  aria-label={`Read more about ${post.title}`}
                  className="group w-full"
                >
                  <div className="flex items-center rounded-lg transition-all duration-300 gap-2">
                    <p className="text-[#1E4841] font-medium">Read More</p>
                    <ArrowRight className="h-4 w-4 text-[#1E4841] transition-transform duration-300 group-hover:translate-x-2" />
                  </div>
                </Link>
              </CardContent>
            </Card>
          ))}
        </div>
        
        {showViewAllButton && (
          <div className="text-center mt-12">
            <Link href="/blog">
              <button className="bg-[#1E4841] text-white px-6 py-3 rounded-md hover:bg-[#132f2a] transition-colors">
                View All Articles
              </button>
            </Link>
          </div>
        )}
      </div>
    </section>
  );
};

export default BlogPostGrid;