"use client";

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Search, AlertCircle } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface TrackReportDialogProps {
  children: React.ReactNode;
}

export function TrackReportDialog({ children }: TrackReportDialogProps) {
  const [open, setOpen] = useState(false);
  const [reportId, setReportId] = useState('');
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const handleSearch = async () => {
    if (!reportId.trim()) {
      toast({
        title: "Error",
        description: "Please enter a report ID",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      // Simulate API call to validate report ID
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // In real app, validate the report ID exists
      // For now, we'll assume it's valid and redirect
      
      // Close dialog and navigate to track report page with the ID
      setOpen(false);
      router.push(`/dashboard/anonymous/track-report?id=${encodeURIComponent(reportId.trim())}`);
      
      // Reset form
      setReportId('');
      
      toast({
        title: "Success",
        description: "Redirecting to your report...",
      });
    } catch {
      toast({
        title: "Error",
        description: "Failed to find report. Please check your report ID and try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Search className="h-5 w-5 mr-2" />
            Track Your Report
          </DialogTitle>
          <DialogDescription>
            Enter your report ID to view the current status and any updates from our investigation team.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="reportId">Report ID</Label>
            <Input
              id="reportId"
              placeholder="Enter your report ID (e.g., ***********-1234)"
              value={reportId}
              onChange={(e) => setReportId(e.target.value)}
              onKeyPress={handleKeyPress}
              disabled={loading}
            />
          </div>
          
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="text-xs">
              Your report ID was provided when you submitted your anonymous report. 
              Keep it secure and confidential.
            </AlertDescription>
          </Alert>
          
          <div className="flex justify-end space-x-2">
            <Button 
              variant="outline" 
              onClick={() => setOpen(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button 
              onClick={handleSearch} 
              disabled={loading || !reportId.trim()}
              className="bg-[#1E4841] hover:bg-[#2A5A52]"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Searching...
                </>
              ) : (
                <>
                  <Search className="h-4 w-4 mr-2" />
                  Track Report
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
