import crypto from 'crypto';
import * as speakeasy from 'speakeasy';
import * as QRCode from 'qrcode';
import connectDB from '@/lib/db/mongodb';

export interface MFASetup {
  secret: string;
  qrCode: string;
  backupCodes: string[];
}

export interface MFAVerification {
  userId: string;
  token: string;
  isValid: boolean;
  timestamp: Date;
}

export class MFAService {
  private static generateSecret(): string {
    return speakeasy.generateSecret({
      name: 'Whistleblower',
      length: 32
    }).base32;
  }

  private static generateBackupCodes(): string[] {
    return Array.from({ length: 8 }, () => 
      crypto.randomBytes(4).toString('hex').toUpperCase()
    );
  }

  static async setupMFA(userId: string, email: string): Promise<MFASetup> {
    const secret = this.generateSecret();
    const backupCodes = this.generateBackupCodes();
    
    const otpauthUrl = `otpauth://totp/Whistleblower:${email}?secret=${secret}&issuer=Whistleblower`;
    const qrCode = await QRCode.toDataURL(otpauthUrl);
    
    return {
      secret,
      qrCode,
      backupCodes
    };
  }

  static verifyTOTP(secret: string, token: string): boolean {
    return speakeasy.totp.verify({
      secret,
      encoding: 'base32',
      token,
      window: 1
    });
  }

  static async enableMFA(userId: string, secret: string, backupCodes: string[]): Promise<boolean> {
    try {
      await connectDB();
      const User = (await import('@/lib/db/models/User')).default;
      
      await User.findByIdAndUpdate(userId, {
        'twoFactor.enabled': true,
        'twoFactor.secret': secret,
        'twoFactor.backupCodes': backupCodes,
        'twoFactor.method': 'app'
      });
      
      return true;
    } catch (error) {
      console.error('MFA enable error:', error);
      return false;
    }
  }

  static async disableMFA(userId: string): Promise<boolean> {
    try {
      await connectDB();
      const User = (await import('@/lib/db/models/User')).default;
      
      await User.findByIdAndUpdate(userId, {
        'twoFactor.enabled': false,
        'twoFactor.secret': undefined,
        'twoFactor.backupCodes': [],
        'twoFactor.method': 'email'
      });
      
      return true;
    } catch (error) {
      console.error('MFA disable error:', error);
      return false;
    }
  }

  static async verifyBackupCode(userId: string, code: string): Promise<boolean> {
    try {
      await connectDB();
      const User = (await import('@/lib/db/models/User')).default;
      
      const user = await User.findById(userId);
      if (!user || !user.twoFactor.backupCodes.includes(code)) {
        return false;
      }
      
      // Remove used backup code
      await User.findByIdAndUpdate(userId, {
        $pull: { 'twoFactor.backupCodes': code }
      });
      
      return true;
    } catch (error) {
      console.error('Backup code verification error:', error);
      return false;
    }
  }

  static async getUserMFAStatus(userId: string): Promise<{ enabled: boolean; method: string }> {
    try {
      await connectDB();
      const User = (await import('@/lib/db/models/User')).default;
      
      const user = await User.findById(userId).select('twoFactor');
      return {
        enabled: user?.twoFactor?.enabled || false,
        method: user?.twoFactor?.method || 'email'
      };
    } catch (error) {
      console.error('MFA status error:', error);
      return { enabled: false, method: 'email' };
    }
  }
}