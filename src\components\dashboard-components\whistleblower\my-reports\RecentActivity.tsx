"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ActivityItem } from "@/lib/types";
import { useRouter } from "next/navigation";
import {
  MessageSquare,
  FileSearch2,
  CheckCheck
} from "lucide-react";

interface RecentActivityProps {
  activityItems: ActivityItem[];
  getStatusTextColor: (status: string) => string;
}

export default function RecentActivity({ activityItems, getStatusTextColor }: RecentActivityProps) {
  const router = useRouter();
  const getActivityIcon = (iconType: string) => {
    switch (iconType) {
      case 'message':
        return MessageSquare;
      case 'status':
        return FileSearch2;
      case 'check':
        return CheckCheck;
      default:
        return MessageSquare;
    }
  };

  return (
    <Card className="mx-4 sm:mx-6">
      <CardHeader className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 sm:gap-0 px-4">
        <div>
          <CardTitle className="text-base sm:text-lg">Recent Report Activity</CardTitle>
          <p className="text-sm text-gray-600">Latest updates and communications</p>
        </div>
        <Button
          variant="ghost"
          size="sm"
          className="text-[#1E4841] hover:bg-[#ECF4E9] w-full sm:w-auto"
          onClick={() => router.push('/dashboard/whistleblower/activity')}
        >
          View All Activity
        </Button>
      </CardHeader>
      <CardContent>
        {activityItems.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
              <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-sm font-medium text-gray-900 mb-1">No recent activity</h3>
            <p className="text-xs text-gray-500 mb-4">Activity updates will appear here as your reports progress</p>
            <Button
              variant="outline"
              size="sm"
              className="text-[#1E4841] border-[#1E4841] hover:bg-[#ECF4E9]"
              onClick={() => router.push('/dashboard/whistleblower/activity')}
            >
              View All Activity
            </Button>
          </div>
        ) : (
          <div className="space-y-4 border-l-2 p-2">
            {activityItems.map((activity) => {
              const IconComponent = getActivityIcon(activity.icon);
              return (
                <div key={activity.id} className="flex gap-4 p-0">
                  <div className="flex-shrink-0">
                    <div
                      className="w-8 h-8 rounded-full flex items-center justify-center"
                      style={{ color: activity.iconColor, backgroundColor: `${activity.iconColor}10` }}
                    >
                      <IconComponent className="w-4 h-4" />
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <p className="text-sm font-medium text-gray-900">{activity.title}</p>
                      <p className="text-xs text-gray-500">{activity.time}</p>
                    </div>
                    {activity.type === 'message' && (
                      <p className="text-sm text-gray-600 mb-1">
                        Requested additional information for report <span className="text-base" style={{ color: '#1E4841' }}>{activity.reportId}</span>
                      </p>
                    )}
                    {activity.type === 'status_update' && (
                      <p className="text-sm text-gray-600 mb-1">
                        Report <span className="text-base" style={{ color: '#1E4841' }}>{activity.reportId}</span> status changed from{' '}
                        <span className="text-base" style={{ color: getStatusTextColor(activity.metadata?.previousStatus || 'New') }}>
                          {activity.metadata?.previousStatus || 'New'}
                        </span>
                        {' '}to{' '}
                        <span className="text-base" style={{ color: getStatusTextColor(activity.metadata?.currentStatus || 'Under Review') }}>
                          {activity.metadata?.currentStatus || 'Under Review'}
                        </span>
                      </p>
                    )}
                    {activity.type === 'resolution' && (
                      <p className="text-sm text-gray-600 mb-1">
                        Report <span className="text-base" style={{ color: '#1E4841' }}>{activity.reportId}</span> has been marked as{' '}
                        <span className="text-base" style={{ color: getStatusTextColor(activity.metadata?.finalStatus || 'Resolved') }}>
                          {activity.metadata?.finalStatus || 'Resolved'}
                        </span>
                      </p>
                    )}
                    {activity.description && activity.description.trim() && (
                      <p className="text-sm bg-[#F9FAFB] text-[#4B5563] p-2 rounded-2xl leading-relaxed mb-3">
                        &ldquo;{activity.description}&rdquo;
                      </p>
                    )}
                    {activity.actionButton && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="p-0 text-[#1E4841] text-sm font-medium hover:text-[#2A5D54]"
                        onClick={() => {
                          switch (activity.actionButton?.action) {
                            case 'reply':
                              router.push(`/dashboard/whistleblower/messages?report=${activity.reportId}&action=reply`);
                              break;
                            case 'view':
                              router.push(`/dashboard/whistleblower/reports/${activity.reportId}`);
                              break;
                            case 'resolution':
                              router.push(`/dashboard/whistleblower/reports/${activity.reportId}?tab=resolution`);
                              break;
                            default:
                              console.log('Unknown action:', activity.actionButton?.action);
                          }
                        }}
                      >
                        {activity.actionButton.text}
                      </Button>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
}