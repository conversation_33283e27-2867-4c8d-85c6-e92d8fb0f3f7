// Security utilities for input sanitization and validation
export const sanitizeForLog = (input: unknown): string => {
  if (typeof input === 'string') {
    return input.replace(/[\r\n\t]/g, ' ').replace(/[<>]/g, '');
  }
  if (typeof input === 'object' && input !== null) {
    return '[Object]';
  }
  return String(input).replace(/[\r\n\t]/g, ' ');
};

export const sanitizeHtml = (html: string): string => {
  return html
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, '');
};

export const validateUserId = (userId: string): boolean => {
  return /^[a-zA-Z0-9_-]+$/.test(userId) && userId.length <= 50;
};

export const generateSecureRandom = async (length: number): Promise<Uint8Array> => {
  // Use Web Crypto API when available (Edge/runtime safe), fallback to Node crypto on server
  if (typeof globalThis !== 'undefined' && typeof (globalThis as unknown as { crypto?: { getRandomValues?: (arr: Uint8Array) => void } }).crypto?.getRandomValues === 'function') {
    const arr = new Uint8Array(length);
    (globalThis.crypto as Crypto).getRandomValues(arr);
    return arr;
  }
  // Dynamically import to avoid top-level require (complies with no-require-imports)
  const nodeCrypto = await import('crypto');
  return new Uint8Array(nodeCrypto.randomBytes(length));
};