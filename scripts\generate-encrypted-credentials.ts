#!/usr/bin/env ts-node

/**
 * Generate Encrypted Database Credentials File
 * 
 * This script creates an encrypted credentials file that can be used by the
 * SecureDatabaseManager for storing multiple database connection strings securely.
 * 
 * Usage:
 *   pnpm generate-credentials
 */

import { config } from 'dotenv';
import crypto from 'crypto';
import fs from 'fs/promises';
import path from 'path';

// Load environment variables
config({ path: '.env.local' });

interface DatabaseCredentials {
  version: string;
  uri: string;
  encryptedAt: Date;
  isActive: boolean;
  isRetired: boolean;
  description?: string;
}

class CredentialsGenerator {
  private readonly credentialsFile: string;
  private readonly encryptionKey: Buffer;

  constructor() {
    this.credentialsFile = path.join(process.cwd(), 'secure', 'db-credentials.enc');
    this.encryptionKey = this.deriveEncryptionKey();
  }

  /**
   * Derive encryption key for credentials (same as SecureDatabaseManager)
   */
  private deriveEncryptionKey(): Buffer {
    const keyMaterial = process.env.DB_ENCRYPTION_KEY || process.env.JWT_SECRET || 'default-key-change-in-production';
    return crypto.scryptSync(keyMaterial, 'db-credentials-salt', 32);
  }

  /**
   * Generate example encrypted credentials file
   */
  async generateExampleCredentials(): Promise<void> {
    console.log('🔐 Generating Encrypted Database Credentials File...');
    console.log('='.repeat(50));

    // Example credentials data (you would replace these with your actual URIs)
    const credentialsData: DatabaseCredentials[] = [
      {
        version: 'v1',
        uri: process.env.MONGODB_URI || 'mongodb+srv://admin:<EMAIL>/whistleblower-db?retryWrites=true&w=majority',
        encryptedAt: new Date(),
        isActive: true,
        isRetired: false,
        description: 'Primary production database connection'
      },
      {
        version: 'v2',
        uri: 'mongodb+srv://admin:<EMAIL>/whistleblower-db?retryWrites=true&w=majority',
        encryptedAt: new Date(),
        isActive: false,
        isRetired: false,
        description: 'Backup database connection for failover'
      },
      {
        version: 'v3',
        uri: 'mongodb+srv://admin:<EMAIL>/whistleblower-db?retryWrites=true&w=majority',
        encryptedAt: new Date(),
        isActive: false,
        isRetired: false,
        description: 'New rotated credentials (ready for activation)'
      }
    ];

    console.log('📋 Credentials to be encrypted:');
    credentialsData.forEach(creds => {
      console.log(`   ${creds.version}: ${creds.description} (Active: ${creds.isActive})`);
      console.log(`      URI: ${creds.uri.substring(0, 30)}...${creds.uri.substring(creds.uri.length - 20)}`);
    });

    // Encrypt the credentials
    const plaintext = JSON.stringify(credentialsData, null, 2);
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv('aes-256-cbc', this.encryptionKey, iv);
    
    let encrypted = cipher.update(plaintext, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const encryptedData = {
      iv: iv.toString('hex'),
      data: encrypted,
      timestamp: new Date().toISOString(),
      description: 'Encrypted database credentials for secure storage'
    };

    // Create secure directory and save file
    await fs.mkdir(path.dirname(this.credentialsFile), { recursive: true });
    await fs.writeFile(this.credentialsFile, JSON.stringify(encryptedData, null, 2));
    
    console.log('\n✅ Encrypted credentials file generated successfully!');
    console.log(`📁 File location: ${this.credentialsFile}`);
    console.log(`🔑 Encryption key derived from: ${process.env.DB_ENCRYPTION_KEY ? 'DB_ENCRYPTION_KEY' : process.env.JWT_SECRET ? 'JWT_SECRET' : 'default-key'}`);
    
    // Test decryption to verify
    await this.testDecryption();
  }

  /**
   * Test decryption to verify the file was created correctly
   */
  private async testDecryption(): Promise<void> {
    try {
      console.log('\n🧪 Testing decryption...');
      
      const encryptedData = JSON.parse(await fs.readFile(this.credentialsFile, 'utf8'));
      
      const iv = Buffer.from(encryptedData.iv, 'hex');
      const decipher = crypto.createDecipheriv('aes-256-cbc', this.encryptionKey, iv);
      
      let decrypted = decipher.update(encryptedData.data, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      const credentialsData = JSON.parse(decrypted);
      
      console.log('✅ Decryption test successful!');
      console.log(`📊 Found ${credentialsData.length} credential entries`);
      
    } catch (error) {
      console.error('❌ Decryption test failed:', error);
      throw error;
    }
  }

  /**
   * Show example of unencrypted file structure
   */
  showUnencryptedExample(): void {
    console.log('\n📖 Example of UNENCRYPTED credentials structure:');
    console.log('='.repeat(50));
    
    const example = [
      {
        version: 'v1',
        uri: 'mongodb+srv://admin:<EMAIL>/database?retryWrites=true&w=majority',
        encryptedAt: '2024-01-15T10:30:00.000Z',
        isActive: true,
        isRetired: false,
        description: 'Primary production database connection'
      },
      {
        version: 'v2',
        uri: 'mongodb+srv://admin:<EMAIL>/database?retryWrites=true&w=majority',
        encryptedAt: '2024-01-20T14:15:00.000Z',
        isActive: false,
        isRetired: false,
        description: 'Backup database connection for failover'
      }
    ];

    console.log(JSON.stringify(example, null, 2));
    console.log('\n⚠️  This structure gets encrypted and stored in the .enc file');
  }
}

// Main execution
async function main() {
  const generator = new CredentialsGenerator();
  
  try {
    await generator.generateExampleCredentials();
    generator.showUnencryptedExample();
    
    console.log('\n🎉 Encrypted credentials file generation completed!');
    console.log('\n📝 Next steps:');
    console.log('   1. Review the generated file at: secure/db-credentials.enc');
    console.log('   2. Update the URIs in the script with your actual database connections');
    console.log('   3. Re-run this script to generate the final encrypted file');
    console.log('   4. The SecureDatabaseManager will now load these credentials automatically');
    
  } catch (error) {
    console.error('❌ Failed to generate encrypted credentials:', error);
    process.exit(1);
  }
}

// Execute main function
main().catch(error => {
  console.error('Script execution failed:', error);
  process.exit(1);
});
