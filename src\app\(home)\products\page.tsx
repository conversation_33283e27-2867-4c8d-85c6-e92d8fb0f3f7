import { Metadata } from "next";
import Header from "@/components/home-components/shared/Header";
import Footer from "@/components/home-components/shared/Footer";
import Link from "next/link";
import Image from "next/image";

export const metadata: Metadata = {
  title: "Products - 7IRIS Whistleblowing Platform",
  description: "Explore our comprehensive whistleblowing solutions: Whistleblower Portal, Investigator Portal, and Admin Portal. Secure, compliant, and user-friendly tools for every stakeholder.",
  openGraph: {
    title: "7IRIS Product Suite - Complete Whistleblowing Solutions",
    description: "Discover our integrated whistleblowing platform with specialized portals for reporters, investigators, and administrators.",
    type: "website",
  },
};

export default function ProductsPage() {
    return (
        <div className="flex flex-col min-h-screen">
            <Header />
            <main id="main-content" aria-label="Products information" className="flex-1 pt-24">
                <div className="container mx-auto px-4 py-16">
                    <div className="max-w-6xl mx-auto">
                        <div className="text-center mb-16">
                            <h1 className="text-4xl md:text-5xl font-bold text-[#1E4841] mb-6">
                                Our Product Suite
                            </h1>
                            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                                Comprehensive whistleblowing solutions designed for every stakeholder in your organization.
                                From anonymous reporting to case management and system administration.
                            </p>
                        </div>

                        <div className="grid md:grid-cols-3 gap-8 mb-16">
                            {/* Whistleblower Portal */}
                            <div className="bg-[#F7FFF2] shadow-md hover:shadow-lg rounded-lg p-8 hover:from-blue-100 hover:to-blue-200 transition-all duration-300 group">
                                <div className="flex items-start justify-between mb-6">
                                    <Image
                                        src="/desktop/main/header/logo.svg"
                                        alt="Logo"
                                        width={83}
                                        height={37}
                                        className="rounded-full hover:scale-105 transition-transform duration-300 w-12 h-auto"
                                        priority
                                    />
                                </div>
                                <h2 className="font-bold text-2xl text-[#1E4841] mb-4">Whistleblower Portal</h2>
                                <p className="text-[#1E4841] mb-6 leading-relaxed">
                                    A secure platform for employees and stakeholders to submit reports anonymously and safely.
                                </p>
                                <Link
                                    href="/products/whistleblower"
                                    className="inline-flex items-center text-[#1E4841] font-semibold hover:text-[#2A5D54] transition-colors duration-300 group-hover:translate-x-1"
                                >
                                    Explore Report Hub →
                                </Link>
                            </div>

                            {/* Investigator Portal */}
                            <div className="bg-[#E9FFDD] shadow-md hover:shadow-lg rounded-lg p-8 hover:from-green-100 hover:to-green-200 transition-all duration-300 group">
                                <div className="flex items-start justify-between mb-6">
                                    <Image
                                        src="/desktop/main/header/logo.svg"
                                        alt="Logo"
                                        width={83}
                                        height={37}
                                        className="rounded-full hover:scale-105 transition-transform duration-300 w-12 h-auto"
                                        priority
                                    />
                                </div>
                                <h2 className="font-bold text-2xl text-[#1E4841] mb-4">Investigator Portal</h2>
                                <p className="text-[#1E4841] mb-6 leading-relaxed">
                                    A purpose-built workspace to manage investigations, review evidence, and maintain compliance.
                                </p>
                                <Link
                                    href="/products/investigator"
                                    className="inline-flex items-center text-[#1E4841] font-semibold hover:text-[#2A5D54] transition-colors duration-300 group-hover:translate-x-1"
                                >
                                    Explore Case Center →
                                </Link>
                            </div>

                            {/* Admin Portal */}
                            <div className="bg-[#D6FFBD] shadow-md hover:shadow-lg rounded-lg p-8 hover:from-purple-100 hover:to-purple-200 transition-all duration-300 group">
                                <div className="flex items-start justify-between mb-6">
                                    <Image
                                        src="/desktop/main/header/logo.svg"
                                        alt="Logo"
                                        width={83}
                                        height={37}
                                        className="rounded-full hover:scale-105 transition-transform duration-300 w-12 h-auto"
                                        priority
                                    />
                                </div>
                                <h2 className="font-bold text-2xl text-[#1E4841] mb-4">Admin Portal</h2>
                                <p className="text-[#1E4841] mb-6 leading-relaxed">
                                    Monitor all whistleblowing activities, assign cases, configure workflows, and generate insights.
                                </p>
                                <Link
                                    href="/products/admin"
                                    className="inline-flex items-center text-[#1E4841] font-semibold hover:text-[#2A5D54] transition-colors duration-300 group-hover:translate-x-1"
                                >
                                    Explore Control Suite →
                                </Link>
                            </div>
                        </div>

                        <div className="text-center">
                            <h2 className="text-3xl font-bold text-[#1E4841] mb-8">Ready to Get Started?</h2>
                            <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                <Link
                                    href="/start-trial-free"
                                    className="bg-[#1E4841] text-white px-8 py-3 rounded-md hover:bg-green-900 transition-colors duration-300 font-semibold"
                                >
                                    Start Free Trial
                                </Link>
                                <Link
                                    href="/contact"
                                    className="border border-[#1E4841] text-[#1E4841] px-8 py-3 rounded-md hover:bg-[#1E4841] hover:text-white transition-colors duration-300 font-semibold"
                                >
                                    Contact Sales
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
            <Footer />
        </div>
    );
}