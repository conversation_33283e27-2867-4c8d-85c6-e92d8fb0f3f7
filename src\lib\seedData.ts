/**
 * Database Seed Data
 * This file contains all data that should be stored in the database
 * Including: users, reports, messages, conversations, blog posts, etc.
 *
 * Data is imported from the mockData directory for consistency
 */

// Import mock data
import { ALL_BLOG_POSTS } from './mockData/blogData';
import { TESTIMONIALS_DATA } from './mockData/testimonialsData';
import { FAQ_DATA } from './mockData/faqData';

// ===== COMPANIES DATA =====
export const companiesData = [
  {
    name: 'TechCorp Industries',
    industry: 'Technology',
    size: 'Large',
    website: 'https://techcorp.com',
    address: {
      street: '123 Tech Street',
      city: 'San Francisco',
      state: 'CA',
      zipCode: '94105',
      country: 'USA'
    },
    contactEmail: '<EMAIL>',
    contactPhone: '******-0123',
    subscriptionStatus: 'Active',
    isActive: true
  },
  {
    name: 'Global Manufacturing Ltd',
    industry: 'Manufacturing',
    size: 'Enterprise',
    website: 'https://globalmanufacturing.com',
    address: {
      street: '456 Industrial Ave',
      city: 'Detroit',
      state: 'MI',
      zipCode: '48201',
      country: 'USA'
    },
    contactEmail: '<EMAIL>',
    contactPhone: '******-0456',
    subscriptionStatus: 'Active',
    isActive: true
  },
  {
    name: 'Financial Services Inc',
    industry: 'Financial Services',
    size: 'Medium',
    website: 'https://financialservices.com',
    address: {
      street: '789 Wall Street',
      city: 'New York',
      state: 'NY',
      zipCode: '10005',
      country: 'USA'
    },
    contactEmail: '<EMAIL>',
    contactPhone: '******-0789',
    subscriptionStatus: 'Active',
    isActive: true
  }
];

// ===== USERS DATA =====
export const usersData = [
  {
    email: '<EMAIL>',
    firstName: 'Sarah',
    lastName: 'Johnson',
    role: 'admin',
    companyIndex: 0, // TechCorp Industries
    password: 'SecureAdmin123!',
    phoneNumber: '******-0101',
    department: 'Compliance',
    jobTitle: 'Chief Compliance Officer'
  },
  {
    email: '<EMAIL>',
    firstName: 'Michael',
    lastName: 'Chen',
    role: 'investigator',
    companyIndex: 0,
    password: 'SecureInvestigator123!',
    phoneNumber: '******-0102',
    department: 'Internal Audit',
    jobTitle: 'Senior Investigator'
  },
  {
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    role: 'whistleblower',
    companyIndex: 0,
    password: 'SecureUser123!',
    phoneNumber: '******-0103',
    department: 'Engineering',
    jobTitle: 'Software Engineer'
  },
  {
    email: '<EMAIL>',
    firstName: 'Emma',
    lastName: 'Wilson',
    role: 'admin',
    companyIndex: 1, // Global Manufacturing Ltd
    password: 'SecureAdmin123!',
    phoneNumber: '******-0201',
    department: 'Compliance',
    jobTitle: 'Compliance Director'
  },
  {
    email: '<EMAIL>',
    firstName: 'David',
    lastName: 'Thompson',
    role: 'investigator',
    companyIndex: 1,
    password: 'SecureInvestigator123!',
    phoneNumber: '******-0202',
    department: 'Risk Management',
    jobTitle: 'Risk Analyst'
  },
  {
    email: '<EMAIL>',
    firstName: 'Jane',
    lastName: 'Smith',
    role: 'whistleblower',
    companyIndex: 1,
    password: 'SecureUser123!',
    phoneNumber: '******-0203',
    department: 'Operations',
    jobTitle: 'Operations Manager'
  },
  {
    email: '<EMAIL>',
    firstName: 'Robert',
    lastName: 'Davis',
    role: 'admin',
    companyIndex: 2, // Financial Services Inc
    password: 'SecureAdmin123!',
    phoneNumber: '******-0301',
    department: 'Compliance',
    jobTitle: 'Head of Compliance'
  },
  {
    email: '<EMAIL>',
    firstName: 'Lisa',
    lastName: 'Anderson',
    role: 'investigator',
    companyIndex: 2,
    password: 'SecureInvestigator123!',
    phoneNumber: '******-0302',
    department: 'Internal Audit',
    jobTitle: 'Senior Auditor'
  }
];

// ===== REPORTS DATA =====
export const reportsData = [
  {
    reportId: 'WB-2025-0001',
    userIndex: 2, // John Doe
    companyIndex: 0, // TechCorp
    title: 'Potential accounting irregularities in Q1 reports',
    description: 'I have observed discrepancies in the Q1 financial reports that may indicate fraudulent activity. The numbers don\'t add up and there seem to be unexplained adjustments.',
    category: 'Financial Misconduct',
    priority: 'High',
    status: 'Under Review',
    isAnonymous: false,
    assignedInvestigatorIndex: 1, // Michael Chen
    incidentDate: new Date('2025-01-15'),
    location: 'Accounting Department, Floor 3',
    tags: ['financial', 'fraud', 'urgent']
  },
  {
    reportId: 'WB-2025-0002',
    userIndex: 5, // Jane Smith
    companyIndex: 1, // Global Manufacturing
    title: 'Safety protocol violations in manufacturing floor',
    description: 'Multiple safety protocols are being ignored on the manufacturing floor, putting workers at risk. Management seems aware but is not taking action.',
    category: 'Safety Violation',
    priority: 'Critical',
    status: 'Under Review',
    isAnonymous: true,
    assignedInvestigatorIndex: 4, // David Thompson
    incidentDate: new Date('2025-01-20'),
    location: 'Manufacturing Floor B',
    tags: ['safety', 'violations', 'critical']
  },
  {
    reportId: 'WB-2025-0003',
    userIndex: 2, // John Doe
    companyIndex: 0, // TechCorp
    title: 'Discrimination in hiring practices',
    description: 'I have witnessed discriminatory practices in our hiring process. Qualified candidates are being rejected based on personal characteristics rather than merit.',
    category: 'Discrimination',
    priority: 'Medium',
    status: 'Awaiting Response',
    isAnonymous: false,
    assignedInvestigatorIndex: 1, // Michael Chen
    incidentDate: new Date('2025-01-25'),
    location: 'HR Department',
    tags: ['discrimination', 'hiring', 'hr']
  }
];

// ===== BLOG POSTS DATA =====
// Using blog data from mockData directory for consistency
export const blogPostsData = ALL_BLOG_POSTS.map(post => ({
  ...post,
  // Convert date string to Date object for database storage
  createdAt: new Date(post.date),
  updatedAt: new Date(post.date),
  isActive: true,
  viewCount: Math.floor(Math.random() * 1000) + 100, // Random view count for demo
  likes: Math.floor(Math.random() * 50) + 5 // Random likes for demo
}));

// ===== ESCALATIONS DATA =====
export const escalationsData = [
  {
    caseId: "WB-2025-085",
    subject: "Health & safety violation",
    department: "Operations",
    priority: "High",
    reason: "SLA deadline approaching with no progress update",
    slaDeadline: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
    status: "Open",
    reportIndex: 0, // Will be linked to first report
    escalatedByIndex: 1, // Admin user
    escalatedToIndex: 2, // Investigator user
    companyIndex: 0
  },
  {
    caseId: "WB-2025-076",
    subject: "Financial misconduct",
    department: "Finance",
    priority: "Critical",
    reason: "High-priority case with potential regulatory implications",
    slaDeadline: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
    status: "In Progress",
    reportIndex: 1, // Will be linked to second report
    escalatedByIndex: 1, // Admin user
    escalatedToIndex: 3, // Another investigator
    companyIndex: 0
  }
];

// ===== AUDIT LOGS DATA =====
export const auditLogsData = [
  {
    action: "Case Assignment",
    actionType: "assignment",
    resourceType: "report",
    description: "Assigned case #WB-2025-089 to Michael Roberts",
    severity: "low",
    category: "operational",
    userIndex: 1, // Admin
    companyIndex: 0,
    ipAddress: "*************"
  },
  {
    action: "Case Update",
    actionType: "status_change",
    resourceType: "report",
    description: "Updated status of case #WB-2025-087 to In Progress",
    severity: "low",
    category: "operational",
    userIndex: 2, // Investigator
    companyIndex: 0,
    ipAddress: "*************"
  },
  {
    action: "SLA Breach",
    actionType: "system_alert",
    resourceType: "escalation",
    description: "Case #WB-2025-076 exceeded SLA deadline",
    severity: "high",
    category: "compliance",
    userIndex: null, // System generated
    isSystemGenerated: true,
    companyIndex: 0,
    ipAddress: "System"
  },
  {
    action: "Case Closure",
    actionType: "closure",
    resourceType: "report",
    description: "Closed case #WB-2025-071 with resolution",
    severity: "medium",
    category: "operational",
    userIndex: 3, // Another investigator
    companyIndex: 0,
    ipAddress: "*************"
  },
  {
    action: "User Login",
    actionType: "login",
    resourceType: "user",
    description: "Successful admin login",
    severity: "low",
    category: "security",
    userIndex: 1, // Admin
    companyIndex: 0,
    ipAddress: "*************"
  }
];

// ===== SYSTEM SETTINGS DATA =====
export const systemSettingsData = [
  {
    key: "email_notifications",
    category: "notifications",
    name: "Email Notifications",
    description: "Configure system email notifications",
    value: true,
    valueType: "boolean",
    defaultValue: true,
    isPublic: false,
    isReadOnly: false,
    requiresRestart: false,
    uiConfig: {
      displayOrder: 1,
      group: "Notifications",
      helpText: "Enable or disable email notifications for system events"
    }
  },
  {
    key: "auto_assignment",
    category: "workflow",
    name: "Auto Assignment",
    description: "Automatically assign new reports to available investigators",
    value: false,
    valueType: "boolean",
    defaultValue: false,
    isPublic: false,
    isReadOnly: false,
    requiresRestart: false,
    uiConfig: {
      displayOrder: 2,
      group: "Workflow",
      helpText: "When enabled, new reports will be automatically assigned to investigators based on workload"
    }
  },
  {
    key: "session_timeout",
    category: "security",
    name: "Session Timeout",
    description: "User session timeout in minutes",
    value: 30,
    valueType: "number",
    defaultValue: 30,
    validation: {
      required: true,
      min: 5,
      max: 480
    },
    isPublic: false,
    isReadOnly: false,
    requiresRestart: false,
    uiConfig: {
      displayOrder: 3,
      group: "Security",
      helpText: "Time in minutes before user sessions expire due to inactivity"
    }
  },
  {
    key: "max_file_size",
    category: "system",
    name: "Maximum File Size",
    description: "Maximum file upload size in MB",
    value: 10,
    valueType: "number",
    defaultValue: 10,
    validation: {
      required: true,
      min: 1,
      max: 100
    },
    isPublic: true,
    isReadOnly: false,
    requiresRestart: true,
    uiConfig: {
      displayOrder: 4,
      group: "System",
      helpText: "Maximum size for file uploads in megabytes"
    }
  }
];

// ===== SYSTEM STATUS DATA =====
export const systemStatusData = [
  {
    serviceName: "Web Server",
    serviceType: "web_server",
    status: "Running",
    uptime: "99.9%",
    uptimePercentage: 99.9,
    responseTime: 120,
    cpuUsage: 15,
    memoryUsage: 45,
    diskUsage: 60,
    version: "1.0.0",
    environment: "production",
    isMonitored: true,
    alertThreshold: 95
  },
  {
    serviceName: "Database",
    serviceType: "database",
    status: "Running",
    uptime: "99.8%",
    uptimePercentage: 99.8,
    responseTime: 50,
    cpuUsage: 25,
    memoryUsage: 70,
    diskUsage: 40,
    version: "MongoDB 7.0",
    environment: "production",
    isMonitored: true,
    alertThreshold: 95
  },
  {
    serviceName: "Security Service",
    serviceType: "security",
    status: "Running",
    uptime: "100%",
    uptimePercentage: 100,
    responseTime: 30,
    cpuUsage: 5,
    memoryUsage: 20,
    diskUsage: 10,
    version: "2.1.0",
    environment: "production",
    isMonitored: true,
    alertThreshold: 98
  },
  {
    serviceName: "Email Service",
    serviceType: "email",
    status: "Running",
    uptime: "98.5%",
    uptimePercentage: 98.5,
    responseTime: 200,
    cpuUsage: 8,
    memoryUsage: 30,
    diskUsage: 15,
    version: "3.2.1",
    environment: "production",
    isMonitored: true,
    alertThreshold: 95
  }
];

// ===== PRICING PLANS DATA =====
// Pricing plans are kept as static content in staticContent.ts
// They are not stored in the database as they are UI/marketing content

// ===== TESTIMONIALS DATA =====
// Using testimonials data from mockData directory for consistency
export const testimonialsData = TESTIMONIALS_DATA.map(testimonial => ({
  ...testimonial,
  // Ensure database compatibility
  createdAt: testimonial.createdAt,
  updatedAt: testimonial.createdAt
}));

// ===== FAQ DATA =====
// Using FAQ data from mockData directory for consistency
export const faqData = FAQ_DATA.map(faq => ({
  ...faq,
  // Ensure database compatibility
  createdAt: faq.createdAt,
  updatedAt: faq.updatedAt
}));
