import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { ArrowR<PERSON>, Clock } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent } from '@/components/ui/card';
import { BlogCard } from '@/lib/types';

interface BlogCardProps {
  card: BlogCard;
}

/**
 * FeaturedBlogCard component for displaying featured blog posts
 */
export const FeaturedBlogCard: React.FC<BlogCardProps> = ({ card }) => {
  return (
    <Card className="group flex flex-col shadow-md hover:shadow-lg border-none transition-shadow duration-300 pt-0 h-full w-full">
      <div className="aspect-[16/9] w-full overflow-hidden rounded-t-lg">
        <Image
          src={card.image}
          alt={card.title}
          width={400}
          height={225}
          className="object-cover w-full h-full hover:scale-105 transition-transform duration-300"
        />
      </div>
      <CardContent className="space-y-3 sm:space-y-4 flex-1 flex flex-col p-3 sm:p-4 md:px-6">
        <div className="flex flex-wrap justify-between items-center gap-2">
          <p className="text-xs font-medium text-[#1E4841] rounded-4xl py-1 px-2 bg-[#ECF4E9]">{card.category}</p>
          <p className="text-xs text-[#1E4841]">{card.date}</p>
        </div>
        <h3 className="text-lg sm:text-xl font-bold text-[#242E2C] hover:text-[#1E4841] transition-colors duration-300">
          <Link href={`/blog/${card.id || card.slug || 'unknown'}`}>{card.title}</Link>
        </h3>
        <p className="text-[#4B5563] leading-relaxed text-sm sm:text-base line-clamp-2 sm:line-clamp-3 flex-1">
          {card.description}
        </p>
        <div className="flex items-center justify-between w-full mt-auto flex-wrap gap-2">
          <div className="flex items-center gap-2 sm:gap-3">
            <Avatar>
              <AvatarImage src={card.author.image} alt={card.author.name} />
              <AvatarFallback>{card.author.initials}</AvatarFallback>
            </Avatar>
            <p className="text-sm text-[#4B5563]">{card.author.name}</p>
          </div>
          <p className="text-[#4B5563] text-xs">{card.readTime}</p>
        </div>
        <Link
          href={`/blog/${card.id || card.slug || 'unknown'}`}
          aria-label={`Read more about ${card.title}`}
          className="group w-full"
        >
          <div className="flex items-center gap-2">
            <p className="text-[#1E4841] font-medium">Read Article</p>
            <ArrowRight className="h-4 w-4 text-[#1E4841] transition-transform duration-300 group-hover:translate-x-2" />
          </div>
        </Link>
      </CardContent>
    </Card>
  );
};

/**
 * CategoryBlogCard component for displaying blog posts in category sections
 */
export const CategoryBlogCard: React.FC<BlogCardProps> = ({ card }) => {
  return (
    <Card className="group flex flex-col shadow-md hover:shadow-lg border-none transition-shadow duration-300 pt-0 h-fit w-full">
      <div className="aspect-[16/9] w-full overflow-hidden rounded-t-lg">
        <Image
          src={card.image}
          alt={card.title}
          width={400}
          height={225}
          className="object-cover w-full h-full hover:scale-105 transition-transform duration-300"
        />
      </div>
      <CardContent className="space-y-3 sm:space-y-4 flex-1 flex flex-col p-3 sm:p-4 md:px-6">
        <div className="flex flex-wrap justify-between items-center gap-2">
          <p className="text-xs font-medium text-[#1E4841] rounded-4xl py-1 px-2 bg-[#ECF4E9]">{card.category}</p>
          <p className="text-xs text-[#1E4841]">{card.date}</p>
        </div>
        <h3 className="text-base sm:text-lg font-bold text-[#242E2C] hover:text-[#1E4841] line-clamp-2 transition-colors duration-300">
          <Link href={`/blog/${card.id || card.slug || 'unknown'}`}>{card.title}</Link>
        </h3>
        <p className="text-[#4B5563] text-xs sm:text-sm line-clamp-2 sm:line-clamp-3 flex-1">
          {card.description}
        </p>
        <div className="flex items-center justify-between w-full mt-auto flex-wrap gap-2">
          <div className="flex items-center gap-2 sm:gap-3">
            <Avatar>
              <AvatarImage src={card.author.image} alt={card.author.name} />
              <AvatarFallback>{card.author.initials}</AvatarFallback>
            </Avatar>
            <p className="text-xs text-[#4B5563] line-clamp-1">{card.author.name}</p>
          </div>
          <span className="text-xs text-gray-500">{card.readTime}</span>
        </div>
      </CardContent>
    </Card>
  );
};

/**
 * CompactBlogCard component for displaying blog posts in a compact format
 */
export const CompactBlogCard: React.FC<BlogCardProps> = ({ card }) => {
  return (
    <Card className="hover:shadow-md transition-shadow duration-300">
      <CardContent className="p-3 sm:p-4 flex gap-2 sm:gap-4">
        <div className="w-16 h-16 sm:w-20 sm:h-20 relative flex-shrink-0 rounded-md overflow-hidden">
          <Image
            src={card.image}
            alt={card.title}
            fill
            className="object-cover"
          />
        </div>
        <div className="flex flex-col">
          <p className="text-xs font-medium text-[#1E4841] mb-1">{card.category}</p>
          <h3 className="text-xs sm:text-sm font-semibold text-[#242E2C] line-clamp-2 mb-1 hover:text-[#1E4841] transition-colors">
            <Link href={`/blog/${card.id || card.slug || 'unknown'}`}>{card.title}</Link>
          </h3>
          <div className="flex items-center gap-1 sm:gap-2 mt-auto">
            <Clock className="h-3 w-3 text-gray-500" />
            <span className="text-xs text-gray-500">{card.readTime}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};