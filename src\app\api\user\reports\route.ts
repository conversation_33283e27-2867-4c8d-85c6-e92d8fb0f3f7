import { NextResponse } from 'next/server';
import { withCompanyIsolation, AuthenticatedRequest } from '@/lib/middleware/auth';
import { DataService } from '@/lib/db/dataService';
import { ReportStatus, ReportCategory, ReportPriority } from '@/lib/types';
import connectDB from '@/lib/db/mongodb';

export const runtime = 'nodejs';

export const GET = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    if (!request.user) {
      return NextResponse.json(
        { success: false, error: 'User not authenticated' },
        { status: 401 }
      );
    }
    
    const { searchParams } = new URL(request.url);
    const status = searchParams.getAll('status').filter(s => s);
    const category = searchParams.getAll('category').filter(c => c);
    const priority = searchParams.getAll('priority').filter(p => p);
    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 50;
    const offset = searchParams.get('offset') ? parseInt(searchParams.get('offset')!) : 0;
    const includeDrafts = searchParams.get('includeDrafts') === 'true';
    
    // Build filters with proper typing
    const filters: {
      limit: number;
      offset: number;
      status?: ReportStatus[];
      category?: ReportCategory[];
      priority?: ReportPriority[];
    } = {
      limit,
      offset
    };
    
    if (status.length > 0) {
      filters.status = status as ReportStatus[];
    }
    
    if (category.length > 0) {
      filters.category = category as ReportCategory[];
    }
    
    if (priority.length > 0) {
      filters.priority = priority as ReportPriority[];
    }
    
    // Get reports for the authenticated user only
    const reports = await DataService.getReports(
      request.user.id, // userId - only get reports for this user
      filters,
      request.user.companyId
    );
    
    // Filter out drafts unless specifically requested
    const filteredReports = includeDrafts 
      ? reports 
      : reports.filter(report => !report.isDraft);
    
    // Transform reports for frontend
    const transformedReports = filteredReports.map(report => ({
      _id: report._id,
      reportId: report.reportId,
      title: report.title,
      description: report.description,
      category: report.category,
      status: report.status,
      priority: report.priority || report.urgencyLevel,
      urgencyLevel: report.urgencyLevel,
      isDraft: report.isDraft,
      isAnonymous: report.isAnonymous,
      dateSubmitted: report.dateSubmitted || report.submittedAt,
      lastUpdated: report.updatedAt,
      createdAt: report.createdAt,
      
      // Step 1 data
      dateOfOccurrence: report.dateOfOccurrence,
      location: report.location,
      
      // Step 2 data
      incidentDate: report.incidentDate,
      incidentTime: report.incidentTime,
      specificLocation: report.specificLocation,
      departmentInvolved: report.departmentInvolved,
      peopleInvolved: report.peopleInvolved,
      
      // Evidence and witness info
      hasWitnesses: report.hasWitnesses,
      witnessDetails: report.witnessDetails,
      hasEvidence: report.hasEvidence,
      evidenceDescription: report.evidenceDescription,
      evidenceFiles: report.evidenceFiles || report.evidence || [],
      
      // Impact assessment
      financialImpact: report.financialImpact,
      operationalImpact: report.operationalImpact,
      reputationalImpact: report.reputationalImpact,
      
      // Previous reports
      hasPreviousReports: report.hasPreviousReports,
      previousReportDetails: report.previousReportDetails,
      
      // Additional info
      additionalComments: report.additionalComments,
      
      // Communication preferences
      emailUpdates: report.emailUpdates,
      smsUpdates: report.smsUpdates,
      
      // Draft info
      draftStep: report.draftStep,
      lastSavedAt: report.lastSavedAt,
      
      // Investigation info
      assignedInvestigator: report.assignedInvestigator,
      progress: report.progress,
      estimatedCompletion: report.estimatedCompletion
    }));
    
    // Get summary statistics
    const totalReports = transformedReports.length;
    const draftCount = transformedReports.filter(r => r.isDraft).length;
    const submittedCount = totalReports - draftCount;
    const statusCounts = transformedReports.reduce((acc, report) => {
      acc[report.status] = (acc[report.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    return NextResponse.json({
      success: true,
      data: {
        reports: transformedReports,
        pagination: {
          total: totalReports,
          limit,
          offset,
          hasMore: totalReports === limit // Simple check, could be more sophisticated
        },
        summary: {
          total: totalReports,
          drafts: draftCount,
          submitted: submittedCount,
          statusBreakdown: statusCounts
        }
      }
    });
  } catch (error) {
    console.error('Get user reports API error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to retrieve reports' },
      { status: 500 }
    );
  }
});

// POST endpoint to create a new report (alternative to the main reports endpoint)
export const POST = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    if (!request.user) {
      return NextResponse.json(
        { success: false, error: 'User not authenticated' },
        { status: 401 }
      );
    }
    
    const reportData = await request.json();
    
    // Ensure the report is created for the authenticated user
    const newReport = await DataService.createReport({
      ...reportData,
      userId: request.user.id
    });
    
    return NextResponse.json({
      success: true,
      data: newReport,
      message: 'Report created successfully'
    }, { status: 201 });
  } catch (error) {
    console.error('Create user report API error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create report' },
      { status: 500 }
    );
  }
});
