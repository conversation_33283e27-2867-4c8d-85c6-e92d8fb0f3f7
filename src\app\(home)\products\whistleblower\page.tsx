import Header from "@/components/home-components/shared/Header";
import Footer from "@/components/home-components/shared/Footer";
import {
  WhistleblowerHero,
  WhistleblowerFeatures,
  WhistleblowerSteps,
  WhistleblowerPrivateSpace,
  WhistleblowerReports,
  WhistleblowerSecurity,
  WhistleblowerCTA
} from "@/components/home-components/product/whistleblower/WhistleblowerComponents";

export default function WhistleblowerPortalPage() {
    return (
        <div className="flex flex-col min-h-screen">
            <Header />
            <main id="main-content" aria-label="Whistleblower Portal" className="flex-1 pt-20">
                <WhistleblowerHero />
                <WhistleblowerFeatures />
                <WhistleblowerSteps />
                <WhistleblowerPrivateSpace />
                <WhistleblowerReports />
                <WhistleblowerSecurity />
                <WhistleblowerCTA />
            </main>
            <Footer />
        </div>
    );
}