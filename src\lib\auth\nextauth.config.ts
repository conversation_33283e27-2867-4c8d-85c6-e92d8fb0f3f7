import GoogleProvider from 'next-auth/providers/google';
import AzureADProvider from 'next-auth/providers/azure-ad';
import { DataService } from '@/lib/db/dataService';
import { generateToken } from '@/lib/middleware/auth';
import { emailService } from '@/lib/email/emailService';
import { NotificationService } from '@/lib/services/notificationService';
import { assignRoleToOAuthUser, validateOAuthUserDomain, requiresManualApproval } from '@/lib/auth/roleAssignment';
import logger from '@/lib/utils/logger';
import connectDB from '@/lib/db/mongodb';

export const authOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      authorization: {
        params: {
          prompt: "consent",
          access_type: "offline",
          response_type: "code"
        }
      }
    }),
    AzureADProvider({
      clientId: process.env.MICROSOFT_CLIENT_ID!,
      clientSecret: process.env.MICROSOFT_CLIENT_SECRET!,
      tenantId: process.env.MICROSOFT_TENANT_ID!,
    })
  ],
  
  callbacks: {
    async signIn({ user, account, profile }) {
      try {
        await connectDB();
        
        if (!user.email) {
          logger.error('OAuth sign-in attempted without email', { provider: account?.provider });
          return false;
        }

        // Validate user domain
        const domainValidation = validateOAuthUserDomain(user.email);
        if (!domainValidation.allowed) {
          logger.error('OAuth sign-in blocked by domain validation', {
            email: user.email,
            provider: account?.provider,
            reason: domainValidation.reason
          });
          return false;
        }

        // Check if user already exists
        let existingUser = await DataService.getUserByEmail(user.email);

        if (!existingUser) {
          // Assign role based on email and domain
          const domain = user.email.split('@')[1];
          const roleAssignment = assignRoleToOAuthUser({
            email: user.email,
            domain,
            name: user.name,
            provider: account?.provider || ''
          });

          // Check if manual approval is required
          const needsApproval = requiresManualApproval(
            roleAssignment.role,
            account?.provider || ''
          );

          // Create new user from OAuth profile
          const userData = {
            email: user.email,
            firstName: user.name?.split(' ')[0] || 'User',
            lastName: user.name?.split(' ').slice(1).join(' ') || '',
            role: roleAssignment.role,
            isActive: !needsApproval, // Inactive if approval required
            emailVerified: true, // OAuth emails are considered verified
            oauthProvider: account?.provider,
            oauthId: account?.providerAccountId,
            profileImage: user.image,
            oauthProfile: {
              sub: (profile as { sub?: string })?.sub,
              name: (profile as { name?: string })?.name,
              given_name: (profile as { given_name?: string })?.given_name,
              family_name: (profile as { family_name?: string })?.family_name,
              picture: (profile as { picture?: string })?.picture,
              email_verified: (profile as { email_verified?: boolean })?.email_verified
            },
            createdAt: new Date(),
            lastLogin: new Date()
          };

          try {
            const result = await DataService.createUser(userData);
            if (result && typeof result === 'object' && 'error' in result) {
              logger.error('Failed to create OAuth user:', result.error);
              return false;
            }
            
            existingUser = result as typeof existingUser;
            
            // Send welcome email for new OAuth users
            try {
              await emailService.sendWelcomeEmail({
                id: existingUser._id.toString(),
                email: existingUser.email,
                firstName: existingUser.firstName,
                lastName: existingUser.lastName,
                role: existingUser.role
              });
              logger.info('Welcome email sent to new OAuth user', {
                email: existingUser.email,
                provider: account?.provider
              });
            } catch (emailError) {
              logger.error('Failed to send welcome email to OAuth user:', emailError);
              // Don't fail sign-in if email fails
            }

            // Create welcome notification for new OAuth users
            try {
              const userName = `${existingUser.firstName} ${existingUser.lastName}`.trim();
              let companyName: string | undefined;

              if (existingUser.companyId) {
                const company = await DataService.getCompanyById(existingUser.companyId.toString());
                const companyData = company as unknown as { name?: string };
                companyName = companyData?.name;
              }

              await NotificationService.createWelcomeNotification(
                existingUser._id.toString(),
                existingUser.role,
                userName,
                companyName
              );
              logger.info('Welcome notification created for new OAuth user', {
                email: existingUser.email,
                provider: account?.provider
              });
            } catch (notificationError) {
              logger.error('Failed to create welcome notification for OAuth user:', notificationError);
              // Don't fail sign-in if notification fails
            }
            
          } catch (createError) {
            logger.error('Error creating OAuth user:', createError);
            return false;
          }
        } else {
          // Update existing user's last login and OAuth info
          try {
            await DataService.updateUser(existingUser._id.toString(), {
              lastLogin: new Date(),
              lastActive: new Date(),
              oauthProvider: account?.provider,
              oauthId: account?.providerAccountId
            });
          } catch (updateError) {
            logger.error('Error updating OAuth user login:', updateError);
            // Don't fail sign-in if update fails
          }
        }

        // Store user info in the user object for use in jwt callback
        user.id = existingUser._id.toString();
        user.role = existingUser.role;
        user.companyId = existingUser.companyId?.toString();
        user.firstName = existingUser.firstName;
        user.lastName = existingUser.lastName;

        logger.info('OAuth sign-in successful', { 
          email: user.email, 
          provider: account?.provider,
          userId: user.id 
        });
        
        return true;
      } catch (error) {
        logger.error('OAuth sign-in error:', error);
        return false;
      }
    },

    async jwt({ token, user, account }) {
      // Initial sign in
      if (user && account) {
        token.id = user.id;
        token.role = user.role;
        token.companyId = user.companyId;
        token.firstName = user.firstName;
        token.lastName = user.lastName;
        token.provider = account.provider;
        
        // Generate our custom JWT token for API authentication
        try {
          const customToken = generateToken(
            user.id as string,
            user.role as string,
            user.companyId as string
          );
          token.customToken = customToken;
        } catch (tokenError) {
          logger.error('Error generating custom token:', tokenError);
        }
      }
      
      return token;
    },

    async session({ session, token }) {
      // Send properties to the client
      if (token) {
        session.user.id = token.id as string;
        session.user.role = token.role as string;
        session.user.companyId = token.companyId as string;
        session.user.firstName = token.firstName as string;
        session.user.lastName = token.lastName as string;
        session.user.provider = token.provider as string;
        session.customToken = token.customToken as string;
      }
      
      return session;
    },

    async redirect({ url, baseUrl }) {
      // Allows relative callback URLs
      if (url.startsWith("/")) return `${baseUrl}${url}`;
      // Allows callback URLs on the same origin
      else if (new URL(url).origin === baseUrl) return url;
      return baseUrl;
    }
  },

  pages: {
    signIn: '/login',
    error: '/auth/error',
  },

  session: {
    strategy: 'jwt' as const,
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },

  secret: process.env.NEXTAUTH_SECRET,

  debug: process.env.NODE_ENV === 'development',

  events: {
    async signIn({ user, account, isNewUser }) {
      logger.info('User signed in via OAuth', {
        userId: user.id,
        email: user.email,
        provider: account?.provider,
        isNewUser
      });
    },
    async signOut({ token }) {
      logger.info('User signed out', {
        userId: token?.id,
        email: token?.email
      });
    }
  }
};
