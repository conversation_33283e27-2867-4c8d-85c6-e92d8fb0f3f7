import { NextResponse } from 'next/server';
import { DataService } from '@/lib/db/dataService';
import { withCompanyIsolation, AuthenticatedRequest } from '@/lib/middleware/auth';

export const runtime = 'nodejs';

// POST endpoint to run the migration (admin only)
export const POST = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    // Only allow admins to run migrations
    if (request.user!.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Access denied. Admin role required.' },
        { status: 403 }
      );
    }

    // Run the migration
    await DataService.migrateToOneOnOneConversations();

    return NextResponse.json({
      success: true,
      message: 'Successfully migrated conversations to 1-on-1 format'
    });
  } catch {
    return NextResponse.json(
      { success: false, error: 'Migration failed' },
      { status: 500 }
    );
  }
});
