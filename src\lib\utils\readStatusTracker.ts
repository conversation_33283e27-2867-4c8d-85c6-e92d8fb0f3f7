/**
 * Read Status Tracking Utility
 * Manages read/unread status for conversations and messages
 */

interface ReadStatus {
  conversationId: string;
  userId: string;
  lastReadAt: Date;
  lastMessageId?: string;
}

class ReadStatusTracker {
  private static instance: ReadStatusTracker;
  private readStatuses: Map<string, ReadStatus> = new Map();

  static getInstance(): ReadStatusTracker {
    if (!ReadStatusTracker.instance) {
      ReadStatusTracker.instance = new ReadStatusTracker();
    }
    return ReadStatusTracker.instance;
  }

  /**
   * Mark a conversation as read by a user
   */
  markConversationAsRead(conversationId: string, userId: string, lastMessageId?: string): void {
    const key = `${conversationId}-${userId}`;
    this.readStatuses.set(key, {
      conversationId,
      userId,
      lastReadAt: new Date(),
      lastMessageId
    });

    // In a real implementation, this would update the database
    this.persistReadStatus(conversationId, userId, lastMessageId);
  }

  /**
   * Check if a conversation is unread for a user
   */
  isConversationUnread(conversationId: string, userId: string, lastMessageAt?: Date): boolean {
    const key = `${conversationId}-${userId}`;
    const readStatus = this.readStatuses.get(key);

    if (!readStatus) {
      return true; // Never read
    }

    if (!lastMessageAt) {
      return false; // No messages to be unread
    }

    return lastMessageAt > readStatus.lastReadAt;
  }

  /**
   * Get unread count for a user across all conversations
   */
  getUnreadCount(userId: string, conversations: Array<{ id: string; lastMessageAt?: Date }>): number {
    return conversations.filter(conv => 
      this.isConversationUnread(conv.id, userId, conv.lastMessageAt)
    ).length;
  }

  /**
   * Persist read status to database (placeholder)
   */
  private async persistReadStatus(conversationId: string, userId: string, lastMessageId?: string): Promise<void> {
    try {
      // In a real implementation, this would make an API call to update the database
      await fetch('/api/conversations/read-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        },
        body: JSON.stringify({
          conversationId,
          userId,
          lastMessageId,
          readAt: new Date().toISOString()
        })
      });
    } catch (error) {
      console.error('Failed to persist read status:', error);
    }
  }

  /**
   * Load read statuses from server (placeholder)
   */
  async loadReadStatuses(userId: string): Promise<void> {
    try {
      // In a real implementation, this would fetch from the database
      const response = await fetch(`/api/conversations/read-status?userId=${userId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.readStatuses) {
          data.readStatuses.forEach((status: ReadStatus) => {
            const key = `${status.conversationId}-${status.userId}`;
            this.readStatuses.set(key, {
              ...status,
              lastReadAt: new Date(status.lastReadAt)
            });
          });
        }
      }
    } catch (error) {
      console.error('Failed to load read statuses:', error);
    }
  }

  /**
   * Clear read statuses (for logout)
   */
  clearReadStatuses(): void {
    this.readStatuses.clear();
  }
}

export default ReadStatusTracker;

// Export singleton instance
export const readStatusTracker = ReadStatusTracker.getInstance();
