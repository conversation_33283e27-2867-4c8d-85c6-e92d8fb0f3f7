#!/usr/bin/env ts-node

/**
 * Consolidated Database Operations
 * 
 * This script consolidates all database-related operations including:
 * - Database seeding and initialization
 * - Conversation and message management
 * - Data verification and fixing
 * - Report management
 * - User management
 * - Database health checks
 * 
 * Usage:
 *   pnpm db:seed                        - Seed database with initial data
 *   pnpm db:check-conversations         - Check conversation integrity
 *   pnpm db:fix-conversations           - Fix conversation issues
 *   pnpm db:check-messages              - Check message integrity
 *   pnpm db:fix-messages                - Fix message issues
 *   pnpm db:check-reports               - Check report data
 *   pnpm db:update-reports              - Update report data
 *   pnpm db:verify-all                  - Verify all data integrity
 *   pnpm db:health-check                - Run database health check
 */

import { config } from 'dotenv';
import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';

// Load environment variables
config({ path: '.env.local' });

// Import database connection and models
import connectDB from '../src/lib/db/mongodb';
import {
  User,
  Company,
  Report,
  Notification,
  Conversation,
  Message,
  Blog,
  Escalation,
  AuditLog,
  SystemSetting,
  SystemStatus,
  Testimonial,
  FAQ
} from '../src/lib/db/models';

interface DatabaseHealthReport {
  timestamp: Date;
  overallStatus: 'HEALTHY' | 'WARNING' | 'CRITICAL';
  collections: Array<{
    name: string;
    status: 'HEALTHY' | 'WARNING' | 'CRITICAL';
    count: number;
    issues: string[];
  }>;
  recommendations: string[];
}

interface DataIntegrityReport {
  conversations: {
    total: number;
    withIssues: number;
    fixed: number;
    issues: string[];
  };
  messages: {
    total: number;
    withIssues: number;
    fixed: number;
    issues: string[];
  };
  reports: {
    total: number;
    withIssues: number;
    fixed: number;
    issues: string[];
  };
}

class DatabaseOperations {
  private encryptMessage: any;

  constructor() {
    // Will be initialized when needed
  }

  async initializeEncryption(): Promise<void> {
    if (!this.encryptMessage) {
      const { encryptMessage } = await import('../src/lib/encryption/messageEncryption');
      this.encryptMessage = encryptMessage;
    }
  }

  // Database Seeding
  async seedDatabase(): Promise<void> {
    console.log('🌱 Seeding Database...');
    console.log('='.repeat(30));

    await connectDB();
    console.log('✅ Connected to database');

    // Clear existing data
    await this.clearDatabase();

    // Import seed data
    const {
      companiesData,
      usersData,
      reportsData
    } = await import('../src/lib/seedData');

    // Seed companies
    console.log('🏢 Seeding companies...');
    const companies = await Company.insertMany(companiesData);
    console.log(`✅ Created ${companies.length} companies`);

    // Seed users
    console.log('👥 Seeding users...');
    const hashedUsers = await Promise.all(
      usersData.map(async (user: any) => ({
        ...user,
        password: await bcrypt.hash(user.password, 12),
        company: companies.find(c => c.name === user.companyName)?._id
      }))
    );
    const users = await User.insertMany(hashedUsers);
    console.log(`✅ Created ${users.length} users`);

    // Seed reports
    console.log('📋 Seeding reports...');
    const reportsWithUsers = reportsData.map((report: any) => ({
      ...report,
      userId: users[report.userIndex]?._id, // Map userIndex to actual user ID
      assignedInvestigator: users[report.assignedInvestigatorIndex]?._id, // Map investigator index
      companyId: companies[report.companyIndex]?._id, // Map company index
      // Remove the index fields as they're not part of the schema
      userIndex: undefined,
      assignedInvestigatorIndex: undefined,
      companyIndex: undefined
    }));
    const reports = await Report.insertMany(reportsWithUsers);
    console.log(`✅ Created ${reports.length} reports`);

    // Seed other collections
    await this.seedOtherCollections(companies, users, reports);

    console.log('🎉 Database seeding completed successfully!');
  }

  private async clearDatabase(): Promise<void> {
    console.log('🧹 Clearing existing data...');

    try {
      // Get all collections in the database
      const db = mongoose.connection.db;
      if (!db) {
        console.error('❌ Database connection not available');
        return;
      }

      const collections = await db.listCollections().toArray();
      console.log(`Found ${collections.length} collections to clear`);

      // Clear known collections first
      const knownCollections = [
        Company, User, Report, Notification, Conversation,
        Message, Blog, Escalation, AuditLog,
        SystemSetting, SystemStatus, Testimonial, FAQ
      ];

      console.log('Clearing known collections...');
      await Promise.all(knownCollections.map(async (Model) => {
        try {
          const result = await Model.deleteMany({});
          console.log(`  ✅ ${Model.collection.name}: ${result.deletedCount} documents deleted`);
        } catch (error) {
          console.log(`  ⚠️ ${Model.collection.name}: ${error.message}`);
        }
      }));

      // Clear any additional collections that might exist
      for (const collection of collections) {
        if (!collection.name.startsWith('system.')) {
          try {
            const collectionObj = db.collection(collection.name);
            const count = await collectionObj.countDocuments();
            if (count > 0) {
              await collectionObj.deleteMany({});
              console.log(`  ✅ ${collection.name}: ${count} additional documents cleared`);
            }
          } catch (error: any) {
            console.log(`  ⚠️ ${collection.name}: Could not clear - ${error.message}`);
          }
        }
      }

      console.log('✅ Database cleared successfully');

    } catch (error) {
      console.error('❌ Error during database clearing:', error);
      throw error;
    }
  }

  private async seedOtherCollections(_companies: any[], users: any[], reports: any[]): Promise<void> {
    // Seed conversations and messages
    console.log('💬 Creating conversations and messages...');
    await this.createConversationsAndMessages(users, reports);

    // Seed other data
    const { blogPostsData, testimonialsData, faqData } = await import('../src/lib/seedData');

    // Seed blog posts
    await Blog.insertMany(blogPostsData);
    console.log(`✅ Created ${blogPostsData.length} blog posts`);

    // Seed testimonials
    if (testimonialsData && testimonialsData.length > 0) {
      await Testimonial.insertMany(testimonialsData);
      console.log(`✅ Created ${testimonialsData.length} testimonials`);
    }

    // Seed FAQs
    if (faqData && faqData.length > 0) {
      await FAQ.insertMany(faqData);
      console.log(`✅ Created ${faqData.length} FAQs`);
    }

    // Note: Pricing plans are kept as static content in staticContent.ts
    // They are not stored in the database as they are UI/marketing content

    console.log('✅ Additional data seeded');
  }

  private async createConversationsAndMessages(_users: any[], reports: any[]): Promise<void> {
    await this.initializeEncryption();

    for (const report of reports) {
      if (report.userId && report.assignedInvestigator) {
        // Create conversation
        const conversation = await Conversation.create({
          participants: [report.userId, report.assignedInvestigator],
          reportId: report._id,
          status: 'active',
          lastMessageAt: new Date()
        });

        // Create initial messages
        const message1Content = 'Thank you for reviewing my report. I have additional information that might be helpful.';
        const message2Content = 'Thank you for your report. I will review the details and may have follow-up questions.';

        const messages = [
          {
            conversationId: conversation._id,
            senderId: report.userId,
            content: message1Content,
            timestamp: new Date(Date.now() - 86400000), // 1 day ago
            messageType: 'text',
            isEncrypted: false // For simplicity in seeding, we'll store as plain text
          },
          {
            conversationId: conversation._id,
            senderId: report.assignedInvestigator,
            content: message2Content,
            timestamp: new Date(Date.now() - 43200000), // 12 hours ago
            messageType: 'text',
            isEncrypted: false // For simplicity in seeding, we'll store as plain text
          }
        ];

        await Message.insertMany(messages);
      }
    }
  }

  // Conversation Management
  async checkConversations(): Promise<DataIntegrityReport['conversations']> {
    console.log('🔍 Checking Conversations...');
    
    await connectDB();
    const conversations = await Conversation.find().populate('participants', 'firstName lastName email');
    
    const issues: string[] = [];
    let withIssues = 0;

    for (const conv of conversations) {
      if (!conv.participants || conv.participants.length === 0) {
        issues.push(`Conversation ${conv._id} has no participants`);
        withIssues++;
      }
      
      if (!conv.reportId) {
        issues.push(`Conversation ${conv._id} has no associated report`);
        withIssues++;
      }
    }

    console.log(`📋 Found ${conversations.length} conversations`);
    if (withIssues > 0) {
      console.log(`⚠️ ${withIssues} conversations have issues`);
      issues.forEach(issue => console.log(`  - ${issue}`));
    } else {
      console.log('✅ All conversations are healthy');
    }

    return {
      total: conversations.length,
      withIssues,
      fixed: 0,
      issues
    };
  }

  async fixConversations(): Promise<DataIntegrityReport['conversations']> {
    console.log('🔧 Fixing Conversation Issues...');
    
    await connectDB();
    await this.initializeEncryption();

    const conversations = await Conversation.find();
    const reports = await Report.find();

    let fixed = 0;
    const issues: string[] = [];

    for (const conv of conversations) {
      let needsUpdate = false;
      const updates: any = {};

      // Fix missing participants
      if (!conv.participants || conv.participants.length === 0) {
        const report = reports.find(r => r._id.toString() === conv.reportId?.toString());
        if (report && report.userId && report.assignedInvestigator) {
          updates.participants = [report.userId, report.assignedInvestigator];
          needsUpdate = true;
        }
      }

      // Fix missing report association
      if (!conv.reportId) {
        // Try to find a report that should be associated
        const potentialReport = reports.find(r =>
          r.userId && r.assignedInvestigator &&
          conv.participants?.includes(r.userId) &&
          conv.participants?.includes(r.assignedInvestigator)
        );

        if (potentialReport) {
          updates.reportId = potentialReport._id;
          needsUpdate = true;
        }
      }

      if (needsUpdate) {
        await Conversation.findByIdAndUpdate(conv._id, updates);
        fixed++;
      }
    }

    console.log(`✅ Fixed ${fixed} conversations`);
    
    return {
      total: conversations.length,
      withIssues: fixed,
      fixed,
      issues
    };
  }

  // Message Management
  async checkMessages(): Promise<DataIntegrityReport['messages']> {
    console.log('🔍 Checking Messages...');
    
    await connectDB();
    const messages = await Message.find();
    
    const issues: string[] = [];
    let withIssues = 0;

    for (const message of messages) {
      if (!message.conversationId) {
        issues.push(`Message ${message._id} has no conversation`);
        withIssues++;
      }

      if (!message.senderId) {
        issues.push(`Message ${message._id} has no sender`);
        withIssues++;
      }

      if (!message.content || message.content.trim() === '') {
        issues.push(`Message ${message._id} has empty content`);
        withIssues++;
      }
    }

    console.log(`📋 Found ${messages.length} messages`);
    if (withIssues > 0) {
      console.log(`⚠️ ${withIssues} messages have issues`);
    } else {
      console.log('✅ All messages are healthy');
    }

    return {
      total: messages.length,
      withIssues,
      fixed: 0,
      issues
    };
  }

  async fixMessages(): Promise<DataIntegrityReport['messages']> {
    console.log('🔧 Fixing Message Issues...');
    
    await connectDB();
    await this.initializeEncryption();

    const messages = await Message.find();
    let fixed = 0;

    for (const message of messages) {
      let needsUpdate = false;
      const updates: any = {};

      // Fix empty content
      if (!message.content || message.content.trim() === '') {
        updates.content = await this.encryptMessage('Message content restored');
        needsUpdate = true;
      }

      // Fix missing timestamp
      if (!message.timestamp) {
        updates.timestamp = new Date();
        needsUpdate = true;
      }

      if (needsUpdate) {
        await Message.findByIdAndUpdate(message._id, updates);
        fixed++;
      }
    }

    console.log(`✅ Fixed ${fixed} messages`);
    
    return {
      total: messages.length,
      withIssues: fixed,
      fixed,
      issues: []
    };
  }

  // Report Management
  async checkReports(): Promise<DataIntegrityReport['reports']> {
    console.log('🔍 Checking Reports...');
    
    await connectDB();
    const reports = await Report.find();
    
    const issues: string[] = [];
    let withIssues = 0;

    for (const report of reports) {
      if (!report.title || report.title.trim() === '') {
        issues.push(`Report ${report._id} has empty title`);
        withIssues++;
      }
      
      if (!report.description || report.description.trim() === '') {
        issues.push(`Report ${report._id} has empty description`);
        withIssues++;
      }

      if (!report.status) {
        issues.push(`Report ${report._id} has no status`);
        withIssues++;
      }
    }

    console.log(`📋 Found ${reports.length} reports`);
    if (withIssues > 0) {
      console.log(`⚠️ ${withIssues} reports have issues`);
    } else {
      console.log('✅ All reports are healthy');
    }

    return {
      total: reports.length,
      withIssues,
      fixed: 0,
      issues
    };
  }

  async updateReports(): Promise<DataIntegrityReport['reports']> {
    console.log('🔧 Updating Reports...');
    
    await connectDB();
    const reports = await Report.find();
    let fixed = 0;

    for (const report of reports) {
      let needsUpdate = false;
      const updates: any = {};

      // Fix missing status
      if (!report.status) {
        updates.status = 'submitted';
        needsUpdate = true;
      }

      // Fix missing priority
      if (!report.priority) {
        updates.priority = 'medium';
        needsUpdate = true;
      }

      // Fix missing dates
      if (!report.submittedAt) {
        updates.submittedAt = new Date();
        needsUpdate = true;
      }

      if (needsUpdate) {
        await Report.findByIdAndUpdate(report._id, updates);
        fixed++;
      }
    }

    console.log(`✅ Updated ${fixed} reports`);
    
    return {
      total: reports.length,
      withIssues: fixed,
      fixed,
      issues: []
    };
  }

  // Comprehensive Data Verification
  async verifyAllData(): Promise<DataIntegrityReport> {
    console.log('🔍 Comprehensive Data Verification...');
    console.log('='.repeat(40));

    const conversations = await this.checkConversations();
    const messages = await this.checkMessages();
    const reports = await this.checkReports();

    const report: DataIntegrityReport = {
      conversations,
      messages,
      reports
    };

    console.log('\n📊 Data Integrity Summary:');
    console.log(`Conversations: ${conversations.total} total, ${conversations.withIssues} with issues`);
    console.log(`Messages: ${messages.total} total, ${messages.withIssues} with issues`);
    console.log(`Reports: ${reports.total} total, ${reports.withIssues} with issues`);

    return report;
  }

  // Database Health Check
  async runHealthCheck(): Promise<DatabaseHealthReport> {
    console.log('🏥 Running Database Health Check...');
    
    await connectDB();
    
    const collections = [
      { name: 'users', model: User },
      { name: 'companies', model: Company },
      { name: 'reports', model: Report },
      { name: 'conversations', model: Conversation },
      { name: 'messages', model: Message }
    ];

    const collectionReports: Array<{
      name: string;
      status: 'HEALTHY' | 'WARNING' | 'CRITICAL';
      count: number;
      issues: string[];
    }> = [];

    for (const collection of collections) {
      try {
        const count = await collection.model.countDocuments();
        const issues: string[] = [];

        // Basic health checks
        if (count === 0 && collection.name !== 'messages') {
          issues.push('Collection is empty');
        }

        const status: 'HEALTHY' | 'WARNING' | 'CRITICAL' =
          issues.length === 0 ? 'HEALTHY' :
          issues.some(i => i.includes('empty')) ? 'WARNING' : 'CRITICAL';

        collectionReports.push({
          name: collection.name,
          status,
          count,
          issues
        });

      } catch (error) {
        collectionReports.push({
          name: collection.name,
          status: 'CRITICAL' as const,
          count: 0,
          issues: [`Error accessing collection: ${error}`]
        });
      }
    }

    const criticalIssues = collectionReports.filter(c => c.status === 'CRITICAL').length;
    const warningIssues = collectionReports.filter(c => c.status === 'WARNING').length;

    const overallStatus: 'HEALTHY' | 'WARNING' | 'CRITICAL' = 
      criticalIssues > 0 ? 'CRITICAL' : 
      warningIssues > 0 ? 'WARNING' : 'HEALTHY';

    const report: DatabaseHealthReport = {
      timestamp: new Date(),
      overallStatus,
      collections: collectionReports,
      recommendations: this.generateHealthRecommendations(collectionReports)
    };

    this.printHealthReport(report);
    return report;
  }

  private generateHealthRecommendations(collections: any[]): string[] {
    const recommendations: string[] = [];
    
    const criticalCollections = collections.filter(c => c.status === 'CRITICAL');
    if (criticalCollections.length > 0) {
      recommendations.push('Address critical collection issues immediately');
    }

    const emptyCollections = collections.filter(c => c.count === 0);
    if (emptyCollections.length > 0) {
      recommendations.push('Consider seeding empty collections with initial data');
    }

    recommendations.push('Regular database health checks recommended');
    
    return recommendations;
  }

  private printHealthReport(report: DatabaseHealthReport): void {
    console.log('\n' + '='.repeat(40));
    console.log('🏥 DATABASE HEALTH REPORT');
    console.log('='.repeat(40));
    console.log(`Overall Status: ${report.overallStatus}`);
    console.log(`Timestamp: ${report.timestamp.toISOString()}`);
    console.log('\nCollections:');
    
    report.collections.forEach(collection => {
      const icon = collection.status === 'HEALTHY' ? '✅' : 
                   collection.status === 'WARNING' ? '⚠️' : '❌';
      console.log(`${icon} ${collection.name}: ${collection.count} documents`);
      
      if (collection.issues.length > 0) {
        collection.issues.forEach(issue => {
          console.log(`    - ${issue}`);
        });
      }
    });
    
    if (report.recommendations.length > 0) {
      console.log('\nRecommendations:');
      report.recommendations.forEach(rec => {
        console.log(`• ${rec}`);
      });
    }
    
    console.log('='.repeat(40));
  }
}

// Main execution
async function main() {
  const command = process.argv[2];
  const dbOps = new DatabaseOperations();

  try {
    switch (command) {
      case 'seed':
        await dbOps.seedDatabase();
        break;
      case 'check-conversations':
        await dbOps.checkConversations();
        break;
      case 'fix-conversations':
        await dbOps.fixConversations();
        break;
      case 'check-messages':
        await dbOps.checkMessages();
        break;
      case 'fix-messages':
        await dbOps.fixMessages();
        break;
      case 'check-reports':
        await dbOps.checkReports();
        break;
      case 'update-reports':
        await dbOps.updateReports();
        break;
      case 'verify-all':
        await dbOps.verifyAllData();
        break;
      case 'health-check':
        await dbOps.runHealthCheck();
        break;
      default:
        console.log('Available commands:');
        console.log('  pnpm db:seed - Seed database with initial data');
        console.log('  pnpm db:check-conversations - Check conversation integrity');
        console.log('  pnpm db:fix-conversations - Fix conversation issues');
        console.log('  pnpm db:check-messages - Check message integrity');
        console.log('  pnpm db:fix-messages - Fix message issues');
        console.log('  pnpm db:check-reports - Check report data');
        console.log('  pnpm db:update-reports - Update report data');
        console.log('  pnpm db:verify-all - Verify all data integrity');
        console.log('  pnpm db:health-check - Run database health check');
    }
  } catch (error) {
    console.error('❌ Database operation failed:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
  }
}

// Execute main function
main().catch(error => {
  console.error('Script execution failed:', error);
  process.exit(1);
});

export { DatabaseOperations };
