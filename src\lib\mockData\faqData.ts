/**
 * FAQ Mock Data
 * Used for seeding the database and as fallback data
 */

export interface FAQData {
  id: string;
  question: string;
  answer: string;
  category: string;
  order: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  tags?: string[];
  helpful?: number;
}

export const FAQ_DATA: FAQData[] = [
  {
    id: "faq-001",
    question: "How do I submit an anonymous report?",
    answer: "You can submit an anonymous report by visiting our whistleblower portal and selecting the 'Anonymous Report' option. No personal information is required, and you'll receive a unique reference number to track your report's progress.",
    category: "reporting",
    order: 1,
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    tags: ["anonymous", "reporting", "portal"],
    helpful: 45
  },
  {
    id: "faq-002",
    question: "Is my identity protected when I file a report?",
    answer: "Yes, absolutely. We use end-to-end encryption and do not track IP addresses. If you choose to remain anonymous, there is no way for anyone to identify you. Even if you provide contact information, it's stored securely and only accessible to authorized personnel.",
    category: "privacy",
    order: 2,
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    tags: ["privacy", "identity", "protection"],
    helpful: 52
  },
  {
    id: "faq-003",
    question: "What types of issues can I report?",
    answer: "You can report any ethical concerns including fraud, harassment, discrimination, safety violations, environmental issues, conflicts of interest, and any other misconduct that violates company policies or laws.",
    category: "reporting",
    order: 3,
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    tags: ["issues", "misconduct", "violations"],
    helpful: 38
  },
  {
    id: "faq-004",
    question: "How long does the investigation process take?",
    answer: "Investigation timelines vary depending on the complexity of the case. Simple matters may be resolved within 2-3 weeks, while complex cases can take 30-90 days. You'll receive regular updates on your case's progress through the secure portal.",
    category: "process",
    order: 4,
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    tags: ["investigation", "timeline", "process"],
    helpful: 41
  },
  {
    id: "faq-005",
    question: "Can I communicate with investigators after submitting a report?",
    answer: "Yes, our platform supports secure two-way communication. You can ask questions, provide additional information, or respond to investigator inquiries while maintaining your anonymity if you chose that option.",
    category: "communication",
    order: 5,
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    tags: ["communication", "investigators", "follow-up"],
    helpful: 33
  },
  {
    id: "faq-006",
    question: "What happens after I submit a report?",
    answer: "After submission, your report is immediately logged in our secure system. Within 24-48 hours, it's reviewed and assigned to an appropriate investigator. You'll receive confirmation and regular updates throughout the process.",
    category: "process",
    order: 6,
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    tags: ["submission", "process", "follow-up"],
    helpful: 29
  },
  {
    id: "faq-007",
    question: "Can I attach files or evidence to my report?",
    answer: "Yes, you can securely upload documents, images, audio files, and other evidence to support your report. All files are encrypted and stored securely. Maximum file size is 50MB per upload.",
    category: "technical",
    order: 7,
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    tags: ["files", "evidence", "upload"],
    helpful: 27
  },
  {
    id: "faq-008",
    question: "Is there protection against retaliation?",
    answer: "Yes, we have strict anti-retaliation policies. Any form of retaliation against whistleblowers is prohibited and will be investigated as a separate violation. We also provide resources and support for anyone experiencing retaliation.",
    category: "protection",
    order: 8,
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    tags: ["retaliation", "protection", "policy"],
    helpful: 48
  },
  {
    id: "faq-009",
    question: "Can I report issues about my direct supervisor?",
    answer: "Absolutely. The system is designed to handle reports about anyone in the organization, including supervisors and senior management. Reports are routed to appropriate investigators who are independent of the reported parties.",
    category: "reporting",
    order: 9,
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    tags: ["supervisor", "management", "hierarchy"],
    helpful: 35
  },
  {
    id: "faq-010",
    question: "What if I'm not sure if something is worth reporting?",
    answer: "When in doubt, report it. It's better to raise a concern that turns out to be minor than to let a serious issue go unreported. Our team can help assess the situation and determine the appropriate course of action.",
    category: "guidance",
    order: 10,
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    tags: ["guidance", "uncertainty", "assessment"],
    helpful: 31
  },
  {
    id: "faq-011",
    question: "Can I make a report on behalf of someone else?",
    answer: "Yes, you can report concerns you've witnessed or been told about. However, encourage the affected person to report directly if possible, as first-hand accounts are often more detailed and actionable.",
    category: "reporting",
    order: 11,
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    tags: ["third-party", "witness", "behalf"],
    helpful: 22
  },
  {
    id: "faq-012",
    question: "Is the platform available 24/7?",
    answer: "Yes, the reporting platform is available 24 hours a day, 7 days a week. You can submit reports at any time. While investigations follow business hours, urgent matters are escalated immediately.",
    category: "technical",
    order: 12,
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    tags: ["availability", "24/7", "access"],
    helpful: 18
  }
];

// Helper functions for filtering FAQs
export const getFAQsByCategory = (category: string) =>
  FAQ_DATA.filter(faq => faq.category === category && faq.isActive)
    .sort((a, b) => a.order - b.order);

export const getActiveFAQs = () =>
  FAQ_DATA.filter(faq => faq.isActive)
    .sort((a, b) => a.order - b.order);

export const searchFAQs = (query: string) =>
  FAQ_DATA.filter(faq => 
    faq.isActive && (
      faq.question.toLowerCase().includes(query.toLowerCase()) ||
      faq.answer.toLowerCase().includes(query.toLowerCase()) ||
      faq.tags?.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
    )
  ).sort((a, b) => a.order - b.order);
