"use client";

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Check } from 'lucide-react';
import { PRICING_PLANS } from '@/lib/staticContent';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";

type BillingPeriod = 'monthly' | 'yearly';
type Plan = 'starter' | 'professional' | 'enterprise';

// Contact form schema
const contactFormSchema = z.object({
    fullName: z.string().min(2, 'Full name is required'),
    companyName: z.string().min(2, 'Company name is required'),
    companyEmail: z.email('Please enter a valid company email address'),
    phoneNumber: z.string().min(10, 'Phone number must be at least 10 digits'),
    employeeRange: z.string().min(1, 'Please select a range'),
    requirements: z.string().min(10, 'Please provide more details about your requirements'),
});

// Using PRICING_PLANS from staticContent.ts

export default function SubscriptionPage() {
    const [billingPeriod, setBillingPeriod] = useState<BillingPeriod>('monthly');
    const [selectedPlan, setSelectedPlan] = useState<Plan>('professional');
    const [isLoading, setIsLoading] = useState(false);
    const [showContactForm, setShowContactForm] = useState(false);
    const [showPendingDialog, setShowPendingDialog] = useState(false); const form = useForm({
        resolver: zodResolver(contactFormSchema),
        defaultValues: {
            fullName: '',
            companyName: '',
            companyEmail: '',
            phoneNumber: '',
            employeeRange: '',
            requirements: ''
        },
        mode: 'onChange'
    });
    const router = useRouter();

    const handleContinue = async () => {
        try {
            setIsLoading(true);
            // TODO: Implement subscription selection logic
            console.log({ plan: selectedPlan, billing: billingPeriod });
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 1500));
            router.push('/signup/admin/completion');
        } catch (error) {
            console.error('Subscription error:', error);
        } finally {
            setIsLoading(false);
        }
    };

// Pricing calculation is now handled by static content pricing format

    return (
        <>
            <Dialog open={showContactForm} onOpenChange={setShowContactForm}>
                <DialogContent className="sm:max-w-[525px]">
                    <DialogHeader>
                        <DialogTitle>Contact Sales Team</DialogTitle>
                        <DialogDescription>
                            Fill out this form and our enterprise sales team will get back to you within 24 hours to discuss your compliance needs.
                        </DialogDescription>
                    </DialogHeader>
                    <form onSubmit={form.handleSubmit(async (data) => {
                        setIsLoading(true);
                        try {
                            const response = await fetch('/api/contact/enterprise', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify(data),
                            });

                            const result = await response.json();

                            if (result.success) {
                                setShowContactForm(false);
                                setShowPendingDialog(true);
                            } else {
                                throw new Error(result.error || 'Failed to submit form');
                            }
                        } catch (error) {
                            console.error('Contact form error:', error);
                            // You could show an error toast here
                        } finally {
                            setIsLoading(false);
                        }
                    })}>
                        <div className="grid gap-4 py-4">
                            <div className="grid grid-cols-1 gap-4">
                                <div>
                                    <Label htmlFor="fullName">Full Name</Label>
                                    <Input
                                        id="fullName"
                                        {...form.register('fullName')}
                                        className="col-span-3"
                                    />
                                    {form.formState.errors.fullName && (
                                        <span className="text-sm text-red-500">
                                            {form.formState.errors.fullName.message}
                                        </span>
                                    )}
                                </div>
                                <div>
                                    <Label htmlFor="companyName">Company Name</Label>
                                    <Input
                                        id="companyName"
                                        {...form.register('companyName')}
                                        className="col-span-3"
                                    />
                                    {form.formState.errors.companyName && (
                                        <span className="text-sm text-red-500">
                                            {form.formState.errors.companyName.message}
                                        </span>
                                    )}
                                </div>
                                <div>
                                    <Label htmlFor="companyEmail">Work Email</Label>
                                    <Input
                                        id="companyEmail"
                                        type="email"
                                        {...form.register('companyEmail')}
                                        className="col-span-3"
                                    />
                                    {form.formState.errors.companyEmail && (
                                        <span className="text-sm text-red-500">
                                            {form.formState.errors.companyEmail.message}
                                        </span>
                                    )}
                                </div>
                                <div>
                                    <Label htmlFor="phoneNumber">Phone Number</Label>
                                    <Input
                                        id="phoneNumber"
                                        {...form.register('phoneNumber')}
                                        className="col-span-3"
                                    />
                                    {form.formState.errors.phoneNumber && (
                                        <span className="text-sm text-red-500">
                                            {form.formState.errors.phoneNumber.message}
                                        </span>
                                    )}
                                </div>
                                <div>
                                    <Label htmlFor="employeeRange">Number of Employees</Label>
                                    <Select onValueChange={(value) => form.setValue('employeeRange', value)}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select a range" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="1-50">1-50 employees</SelectItem>
                                            <SelectItem value="51-200">51-200 employees</SelectItem>
                                            <SelectItem value="201-500">201-500 employees</SelectItem>
                                            <SelectItem value="501-1000">501-1000 employees</SelectItem>
                                            <SelectItem value="1000+">1000+ employees</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    <span className="text-sm text-red-500">
                                        {form.formState.errors.employeeRange?.message}
                                    </span>
                                </div>
                                <div>
                                    <Label htmlFor="requirements">Additional Requirements</Label>
                                    <Textarea
                                        id="requirements"
                                        {...form.register('requirements')}
                                        className="col-span-3"
                                        placeholder="Tell us about your specific needs..."
                                    />
                                    {form.formState.errors.requirements && (
                                        <span className="text-sm text-red-500">
                                            {form.formState.errors.requirements.message}
                                        </span>
                                    )}
                                </div>
                            </div>
                        </div>
                        <DialogFooter>
                            <Button type="button" variant="outline" onClick={() => setShowContactForm(false)}>
                                Cancel
                            </Button>
                            <Button type="submit" disabled={isLoading}>
                                {isLoading ? "Processing..." : "Submit"}
                            </Button>
                        </DialogFooter>
                    </form>
                </DialogContent>
            </Dialog>
            <div className="min-h-screen flex">
                {/* Left side content */}
                <div className="w-1/2 p-8 flex flex-col justify-center items-center bg-white">
                    <div className="w-full max-w-2xl">
                        <div className="mb-8">
                            <Link href="/">
                                <Image src="/logo.svg" alt="Logo" width={120} height={40} />
                            </Link>
                        </div>

                        <div className="mb-8">
                            <h1 className="text-2xl font-bold text-gray-900 mb-2">Choose Your Plan</h1>
                            <p className="text-gray-500">Choose the right plan to match your team&apos;s compliance goals and case volume.</p>
                        </div>

                        <div className="mb-8 flex justify-center">
                            <div className="inline-flex items-center rounded-full bg-gray-100 p-1">
                                <button
                                    className={`px-4 py-2 rounded-full text-sm font-medium ${billingPeriod === 'monthly'
                                        ? 'bg-white shadow text-gray-900'
                                        : 'text-gray-500 hover:text-gray-900'
                                        }`}
                                    onClick={() => setBillingPeriod('monthly')}
                                >
                                    Monthly
                                </button>
                                <button
                                    className={`px-4 py-2 rounded-full text-sm font-medium flex items-center gap-2 ${billingPeriod === 'yearly'
                                        ? 'bg-white shadow text-gray-900'
                                        : 'text-gray-500 hover:text-gray-900'
                                        }`}
                                    onClick={() => setBillingPeriod('yearly')}
                                >
                                    Yearly
                                    <span className="bg-[#ECF4E9] text-[#1E4841] text-xs px-2 py-0.5 rounded">
                                        Save 20%
                                    </span>
                                </button>
                            </div>
                        </div>

                        <RadioGroup
                            value={selectedPlan}
                            onValueChange={(value: Plan) => setSelectedPlan(value)}
                            className="grid gap-4"
                        >
                            {PRICING_PLANS.map((plan) => {
                                const key = plan.name.toLowerCase() as Plan;
                                return (
                                <div key={key} className="relative">
                                    <RadioGroupItem
                                        value={key}
                                        id={key}
                                        className="peer sr-only"
                                    />
                                    <Label
                                        htmlFor={key}
                                        className={`flex flex-col p-6 border-2 rounded-lg cursor-pointer transition-all
                    ${plan.isHighlighted ? 'border-[#1E4841]' : 'border-gray-200'}
                    peer-checked:border-[#1E4841] peer-checked:ring-1 peer-checked:ring-[#1E4841]
                    hover:border-[#1E4841]/50`}
                                    >
                                        <div className="flex justify-between items-start mb-4">
                                            <div>
                                                <h3 className="font-semibold text-lg text-gray-900">{plan.name}</h3>
                                                <p className="text-sm text-gray-500">{plan.description}</p>
                                            </div>
                                            <div className="text-right">
                                                {plan.name === 'Enterprise' ? (
                                                    <div className="font-semibold text-xl text-gray-900">Custom Price</div>
                                                ) : (
                                                    <div className="font-semibold text-xl text-gray-900">
                                                        {billingPeriod === 'yearly' ? plan.yearlyPrice : plan.monthlyPrice}
                                                    </div>
                                                )}
                                            </div>
                                        </div>

                                        <div className="space-y-2">
                                            <div className="flex gap-2 items-center">
                                                <Check className="h-4 w-4 text-[#1E4841]" />
                                                <span className="text-sm">
                                                    <strong>Whistleblower:</strong> {plan.access?.whistleblower || 'Unlimited'}
                                                </span>
                                            </div>
                                            <div className="flex gap-2 items-center">
                                                <Check className="h-4 w-4 text-[#1E4841]" />
                                                <span className="text-sm">
                                                    <strong>Investigator:</strong> {plan.access?.investigator || 'Unlimited'}
                                                </span>
                                            </div>
                                            <div className="flex gap-2 items-center">
                                                <Check className="h-4 w-4 text-[#1E4841]" />
                                                <span className="text-sm">
                                                    <strong>Admin Panel:</strong> {plan.access?.admin || 'Unlimited'}
                                                </span>
                                            </div>
                                            <div className="mt-3 space-y-1">
                                                {plan.features.slice(0, 3).map((feature, featureIndex) => (
                                                    <div key={featureIndex} className="flex gap-2 items-center">
                                                        <Check className="h-4 w-4 text-[#1E4841]" />
                                                        <span className="text-sm">{feature}</span>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>

                                        <div className="mt-4">
                                            {key === 'enterprise' ? (
                                                <Button
                                                    type="button"
                                                    variant="outline"
                                                    className="w-full"
                                                    onClick={() => window.location.href = 'mailto:<EMAIL>'}
                                                >
                                                    Contact Sales
                                                </Button>
                                            ) : (
                                                <Button
                                                    type="button"
                                                    variant="outline"
                                                    className="w-full"
                                                    onClick={() => router.push(`/signup/admin/completion?plan=${key}&billing=${billingPeriod}`)}
                                                >
                                                    Subscribe Now
                                                </Button>
                                            )}
                                        </div>
                                    </Label>
                                </div>
                                );
                            })}
                        </RadioGroup>

                        <div className="mt-8">
                            {selectedPlan === 'enterprise' ? (
                                <Button
                                    onClick={() => setShowContactForm(true)}
                                    className="w-full bg-[#1E4841] hover:bg-[#1E4841]/90 text-white"
                                    disabled={isLoading}
                                >
                                    Contact Sales
                                </Button>
                            ) : (
                                <Button
                                    onClick={handleContinue}
                                    className="w-full bg-[#1E4841] hover:bg-[#1E4841]/90 text-white"
                                    disabled={isLoading}
                                >
                                    {isLoading ? "Processing..." : "Continue"}
                                </Button>
                            )}
                        </div>
                    </div>
                </div>

                {/* Right side content */}
                <div className="w-1/2 bg-[#F9FAFB] p-8 flex items-center justify-center">
                    <div className="max-w-lg space-y-6">
                        <div className="space-y-2">
                            <div className="flex items-center space-x-4">
                                <div className="w-8 h-8 rounded-full bg-[#1E4841] text-white flex items-center justify-center font-semibold">
                                    1
                                </div>
                                <div className="h-1 flex-1 bg-[#1E4841]" />
                                <div className="w-8 h-8 rounded-full bg-[#1E4841] text-white flex items-center justify-center font-semibold">
                                    2
                                </div>
                                <div className="h-1 flex-1 bg-gray-200" />
                                <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center font-semibold">
                                    3
                                </div>
                            </div>
                            <div className="flex justify-between text-sm text-gray-600">
                                <span>Admin Signup</span>
                                <span>Subscription Plan</span>
                                <span>Completion</span>
                            </div>
                        </div>

                        <div className="space-y-4">
                            <h2 className="text-2xl font-bold text-[#1E4841]">Choose Your Plan</h2>
                            <p className="text-gray-600">
                                Choose the right plan to match your team&apos;s compliance goals and case volume.
                            </p>
                        </div>

                        <div className="relative aspect-video rounded-lg overflow-hidden">
                            <Image
                                src="/dashboard/investigator.jpg"
                                alt="Investigator dashboard preview"
                                fill
                                className="object-cover"
                            />
                        </div>

                        <div className="space-y-4">
                            <div className="flex items-start space-x-3">
                                <div className="w-6 h-6 rounded-full bg-[#ECF4E9] flex items-center justify-center mt-1">
                                    <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                                        <path d="M10 3L4.5 8.5L2 6" stroke="#1E4841" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                    </svg>
                                </div>
                                <div>
                                    <h3 className="font-semibold text-[#1E4841]">Flexible Scaling</h3>
                                    <p className="text-sm text-gray-600">Easily upgrade or modify your plan as your organization&apos;s needs evolve.</p>
                                </div>
                            </div>

                            <div className="flex items-start space-x-3">
                                <div className="w-6 h-6 rounded-full bg-[#ECF4E9] flex items-center justify-center mt-1">
                                    <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                                        <path d="M10 3L4.5 8.5L2 6" stroke="#1E4841" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                    </svg>
                                </div>
                                <div>
                                    <h3 className="font-semibold text-[#1E4841]">Transparent Pricing</h3>
                                    <p className="text-sm text-gray-600">Clear, upfront pricing with no hidden fees or surprise charges.</p>
                                </div>
                            </div>

                            <div className="flex items-start space-x-3">
                                <div className="w-6 h-6 rounded-full bg-[#ECF4E9] flex items-center justify-center mt-1">
                                    <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                                        <path d="M10 3L4.5 8.5L2 6" stroke="#1E4841" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                    </svg>
                                </div>
                                <div>
                                    <h3 className="font-semibold text-[#1E4841]">24/7 Support</h3>
                                    <p className="text-sm text-gray-600">Get help anytime with our dedicated support team and extensive resources.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <Dialog open={showPendingDialog} onOpenChange={setShowPendingDialog}>
                <DialogContent className="sm:max-w-[525px]">
                    <DialogHeader>
                        <DialogTitle>Subscription Pending</DialogTitle>
                        <DialogDescription className="space-y-4">
                            <p>
                                Thanks for reaching out! Your custom subscription is under review. Our
                                team will contact you soon to discuss your requirements and set up
                                your account.
                            </p>
                        </DialogDescription>
                    </DialogHeader>
                    <div className="flex flex-col gap-4">
                        <div className="flex justify-between gap-4">
                            <Button variant="outline" onClick={() => { setShowPendingDialog(false); router.push('/'); }}>
                                Return to Homepage
                            </Button>
                            <Button variant="outline" onClick={() => { setShowPendingDialog(false); window.location.href = 'mailto:<EMAIL>'; }}>
                                Contact Support
                            </Button>
                            <Button onClick={() => setShowPendingDialog(false)}>
                                Choose Another Plan
                            </Button>
                        </div>
                        <p className="text-sm text-muted-foreground text-center">
                            Need immediate assistance? Contact our support team at{' '}
                            <a href="mailto:<EMAIL>" className="text-primary hover:underline">
                                <EMAIL>
                            </a>
                        </p>
                    </div>
                </DialogContent>
            </Dialog>
        </>
    );
};