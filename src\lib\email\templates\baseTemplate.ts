/**
 * Base Email Template System
 * Provides consistent branding and styling for all email types
 */

export interface EmailTemplateOptions {
  title: string;
  preheader?: string;
  headerColor?: string;
  headerIcon?: string;
  footerText?: string;
  unsubscribeLink?: string;
  recipientEmail?: string;
}

export class EmailTemplate {
  private static readonly BRAND_COLORS = {
    primary: '#1E4841',
    secondary: '#2D5A52',
    accent: '#4A9B8E',
    success: '#28a745',
    warning: '#ffc107',
    danger: '#dc3545',
    info: '#17a2b8',
    light: '#f8f9fa',
    dark: '#343a40',
    muted: '#6c757d'
  };

  private static readonly BRAND_FONTS = {
    primary: 'Arial, sans-serif',
    heading: 'Georgia, serif'
  };

  static createBaseTemplate(content: string, options: EmailTemplateOptions): string {
    const {
      title,
      preheader = '',
      headerColor = this.BRAND_COLORS.primary,
      headerIcon = '🛡️',
      footerText = 'Whistleblower System - Secure, Anonymous, Trusted',
      unsubscribeLink,
      recipientEmail
    } = options;

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>${title}</title>
    ${preheader ? `<meta name="description" content="${preheader}">` : ''}
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
    <style>
        /* Reset styles */
        body, table, td, p, a, li, blockquote {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        table, td {
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        img {
            -ms-interpolation-mode: bicubic;
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
        }
        
        /* Base styles */
        body {
            margin: 0 !important;
            padding: 0 !important;
            background-color: #f4f4f4;
            font-family: ${this.BRAND_FONTS.primary};
            font-size: 16px;
            line-height: 1.6;
            color: #333333;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
        }
        
        .email-header {
            background-color: ${headerColor};
            padding: 30px 20px;
            text-align: center;
        }
        
        .email-header h1 {
            margin: 0;
            color: #ffffff;
            font-size: 28px;
            font-weight: bold;
            font-family: ${this.BRAND_FONTS.heading};
        }
        
        .email-content {
            padding: 40px 30px;
        }
        
        .email-footer {
            background-color: #f8f9fa;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 30px;
            background-color: ${this.BRAND_COLORS.primary};
            color: #ffffff !important;
            text-decoration: none;
            border-radius: 6px;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
        }
        
        .btn:hover {
            background-color: ${this.BRAND_COLORS.secondary};
        }
        
        .btn-secondary {
            background-color: ${this.BRAND_COLORS.muted};
        }
        
        .btn-success {
            background-color: ${this.BRAND_COLORS.success};
        }
        
        .btn-warning {
            background-color: ${this.BRAND_COLORS.warning};
            color: #212529 !important;
        }
        
        .btn-danger {
            background-color: ${this.BRAND_COLORS.danger};
        }
        
        .alert {
            padding: 15px;
            margin: 20px 0;
            border-radius: 6px;
            border: 1px solid transparent;
        }
        
        .alert-success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        
        .alert-warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        
        .alert-danger {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        
        .alert-info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        
        .card {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
            border: 1px solid #e9ecef;
        }
        
        .text-center {
            text-align: center;
        }
        
        .text-muted {
            color: ${this.BRAND_COLORS.muted} !important;
        }
        
        .small {
            font-size: 12px;
        }
        
        /* Responsive styles */
        @media only screen and (max-width: 600px) {
            .email-container {
                width: 100% !important;
            }
            
            .email-content {
                padding: 20px !important;
            }
            
            .email-header {
                padding: 20px !important;
            }
            
            .email-header h1 {
                font-size: 24px !important;
            }
        }
    </style>
</head>
<body>
    ${preheader ? `
    <!-- Preheader text -->
    <div style="display: none; font-size: 1px; color: #fefefe; line-height: 1px; font-family: ${this.BRAND_FONTS.primary}; max-height: 0px; max-width: 0px; opacity: 0; overflow: hidden;">
        ${preheader}
    </div>
    ` : ''}
    
    <div class="email-container">
        <!-- Header -->
        <div class="email-header">
            <h1>${headerIcon} ${title}</h1>
        </div>
        
        <!-- Content -->
        <div class="email-content">
            ${content}
        </div>
        
        <!-- Footer -->
        <div class="email-footer">
            <p class="text-muted small" style="margin: 0 0 10px 0;">
                ${footerText}
            </p>
            ${recipientEmail ? `
            <p class="text-muted small" style="margin: 0 0 10px 0;">
                This email was sent to ${recipientEmail}
            </p>
            ` : ''}
            ${unsubscribeLink ? `
            <p class="text-muted small" style="margin: 0;">
                <a href="${unsubscribeLink}" style="color: ${this.BRAND_COLORS.muted};">Unsubscribe</a>
            </p>
            ` : ''}
            <p class="text-muted small" style="margin: 10px 0 0 0;">
                © ${new Date().getFullYear()} Whistleblower System. All rights reserved.
            </p>
        </div>
    </div>
</body>
</html>
    `.trim();
  }

  static createButton(text: string, url: string, type: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'primary'): string {
    return `
    <div class="text-center">
        <a href="${url}" class="btn ${type !== 'primary' ? `btn-${type}` : ''}" style="color: #ffffff; text-decoration: none;">
            ${text}
        </a>
    </div>
    `;
  }

  static createAlert(content: string, type: 'success' | 'warning' | 'danger' | 'info' = 'info'): string {
    const icons = {
      success: '✅',
      warning: '⚠️',
      danger: '❌',
      info: 'ℹ️'
    };

    return `
    <div class="alert alert-${type}">
        <strong>${icons[type]} ${type.charAt(0).toUpperCase() + type.slice(1)}</strong><br>
        ${content}
    </div>
    `;
  }

  static createCard(content: string, title?: string): string {
    return `
    <div class="card">
        ${title ? `<h3 style="margin: 0 0 15px 0; color: ${this.BRAND_COLORS.primary};">${title}</h3>` : ''}
        ${content}
    </div>
    `;
  }
}
