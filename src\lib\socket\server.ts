import { Server as HTTPServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import { DataService } from '@/lib/db/dataService';
import { InputSanitizer } from '@/lib/utils/inputSanitizer';
import connectDB from '@/lib/db/mongodb';

export interface SocketUser {
  userId: string;
  role: 'whistleblower' | 'investigator' | 'admin';
  socketId: string;
  conversationIds: string[];
}

export interface TypingData {
  conversationId: string;
  userId: string;
  userName: string;
  isTyping: boolean;
}

export interface MessageData {
  conversationId: string;
  senderId: string;
  content: string;
  messageType: 'text' | 'file' | 'system';
  attachments?: Array<{
    fileName: string;
    fileUrl: string;
    fileSize: number;
    mimeType: string;
  }>;
}

class SocketManager {
  private io: SocketIOServer | null = null;
  private connectedUsers: Map<string, SocketUser> = new Map();
  private typingUsers: Map<string, Set<string>> = new Map();
  private cleanupInterval: NodeJS.Timeout | null = null;

  initialize(server: HTTPServer) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: process.env.NODE_ENV === 'production' 
          ? process.env.NEXTAUTH_URL 
          : ["http://localhost:3002", "http://localhost:3000"],
        methods: ["GET", "POST"],
        credentials: true
      },
      transports: ['websocket', 'polling']
    });

    // Setup cleanup interval
    this.cleanupInterval = setInterval(() => {
      this.cleanupTypingUsers();
    }, 300000); // 5 minutes

    this.io.on('connection', (socket) => {
      // Handle user authentication and joining
      socket.on('authenticate', async (data: { userId: string; role: string }) => {
        try {
          const user = await DataService.getUserById(data.userId);
          
          if (!user) {
            socket.emit('auth_error', { message: 'User not found' });
            return;
          }

          // Get user's conversations
          const conversations = await DataService.getConversations(data.userId);
          const conversationIds = conversations.map(conv => conv._id?.toString() || '');

          const socketUser: SocketUser = {
            userId: data.userId,
            role: data.role as 'whistleblower' | 'investigator' | 'admin',
            socketId: socket.id,
            conversationIds
          };

          this.connectedUsers.set(socket.id, socketUser);

          // Join conversation rooms
          conversationIds.forEach(convId => {
            socket.join(`conversation:${convId}`);
          });

          // Notify others in conversations about online status
          this.broadcastUserStatus(data.userId, true);

          socket.emit('authenticated', {
            success: true,
            conversations: conversationIds
          });
        } catch {
          socket.emit('auth_error', { message: 'Authentication failed' });
        }
      });

      // Handle joining specific conversations
      socket.on('join_conversation', (conversationId: string) => {
        socket.join(`conversation:${conversationId}`);
      });

      // Handle leaving conversations
      socket.on('leave_conversation', (conversationId: string) => {
        socket.leave(`conversation:${conversationId}`);
      });

      // Handle sending messages
      socket.on('send_message', async (messageData: MessageData) => {
        try {
          await connectDB();
          
          // Validate and sanitize input
          if (!InputSanitizer.validateObjectId(messageData.conversationId) || 
              !InputSanitizer.validateObjectId(messageData.senderId)) {
            socket.emit('message_error', { message: 'Invalid message data' });
            return;
          }
          
          const sanitizedContent = InputSanitizer.sanitizeString(messageData.content);
          if (!sanitizedContent) {
            socket.emit('message_error', { message: 'Message content cannot be empty' });
            return;
          }
          
          // Save message to database
          const savedMessage = await DataService.createMessage({
            conversationId: messageData.conversationId,
            senderId: messageData.senderId,
            content: sanitizedContent,
            messageType: messageData.messageType,
            attachments: messageData.attachments
          });

          // Get sender info for broadcasting
          const sender = await DataService.getUserById(messageData.senderId);
          
          const messageWithSender = {
            ...savedMessage.toObject(),
            senderId: {
              _id: sender?._id,
              firstName: sender?.firstName,
              lastName: sender?.lastName,
              role: sender?.role
            }
          };

          // Broadcast to all users in the conversation
          this.io?.to(`conversation:${messageData.conversationId}`)
            .emit('new_message', messageWithSender);

          // Stop typing indicator for sender
          this.handleTypingStop(messageData.conversationId, messageData.senderId);
        } catch {
          socket.emit('message_error', { message: 'Failed to send message' });
        }
      });

      // Handle typing indicators
      socket.on('typing_start', async (data: { conversationId: string; userId: string }) => {
        this.handleTypingStart(data.conversationId, data.userId, socket);
      });

      socket.on('typing_stop', (data: { conversationId: string; userId: string }) => {
        this.handleTypingStop(data.conversationId, data.userId);
      });

      // Handle message read receipts
      socket.on('mark_message_read', async (data: { messageId: string; userId: string }) => {
        try {
          // Update message read status in database
          // This would require updating the Message model to track read receipts
          
          // Broadcast read receipt to conversation participants
          const user = this.connectedUsers.get(socket.id);
          if (user) {
            user.conversationIds.forEach(convId => {
              socket.to(`conversation:${convId}`).emit('message_read', {
                messageId: data.messageId,
                userId: data.userId,
                readAt: new Date()
              });
            });
          }
        } catch {
          // Handle read status errors silently
        }
      });

      // Handle conversation creation
      socket.on('create_conversation', async (data: { reportId: string; participants: string[] }) => {
        try {
          await connectDB();

          // Enforce 1-on-1 conversations
          if (data.participants.length !== 2) {
            socket.emit('conversation_error', {
              message: 'Conversations must have exactly 2 participants (1-on-1 only)'
            });
            return;
          }

          const conversation = await DataService.createConversation({
            reportId: data.reportId,
            participants: data.participants
          });

          // Add all participants to the conversation room
          data.participants.forEach(participantId => {
            const participantSocket = this.findSocketByUserId(participantId);
            if (participantSocket && typeof participantSocket === 'object' && 'join' in participantSocket) {
              (participantSocket as { join: (room: string) => void }).join(`conversation:${conversation._id}`);
            }
          });

          // Notify all participants
          this.io?.to(`conversation:${conversation._id}`)
            .emit('conversation_created', conversation);
        } catch {
          socket.emit('conversation_error', { message: 'Failed to create conversation' });
        }
      });

      // Handle disconnection
      socket.on('disconnect', () => {
        const user = this.connectedUsers.get(socket.id);
        if (user) {
          // Remove from typing indicators
          user.conversationIds.forEach(convId => {
            this.handleTypingStop(convId, user.userId);
          });

          // Notify others about offline status
          this.broadcastUserStatus(user.userId, false);

          this.connectedUsers.delete(socket.id);
        }
      });
    });
  }

  private cleanupTypingUsers() {
    for (const [conversationId, userSet] of this.typingUsers.entries()) {
      if (userSet.size === 0) {
        this.typingUsers.delete(conversationId);
      }
    }
  }

  private handleTypingStart(conversationId: string, userId: string, socket: { to: (room: string) => { emit: (event: string, data: unknown) => void } }) {
    if (!this.typingUsers.has(conversationId)) {
      this.typingUsers.set(conversationId, new Set());
    }
    
    const typingSet = this.typingUsers.get(conversationId)!;
    typingSet.add(userId);

    // Broadcast typing indicator to others in conversation
    socket.to(`conversation:${conversationId}`).emit('user_typing', {
      conversationId,
      userId,
      isTyping: true
    });
  }

  private handleTypingStop(conversationId: string, userId: string) {
    const typingSet = this.typingUsers.get(conversationId);
    if (typingSet) {
      typingSet.delete(userId);
      
      // Broadcast stop typing to conversation
      this.io?.to(`conversation:${conversationId}`).emit('user_typing', {
        conversationId,
        userId,
        isTyping: false
      });
    }
  }

  private broadcastUserStatus(userId: string, isOnline: boolean) {
    const sanitizedUserId = userId.replace(/[^a-zA-Z0-9]/g, '').substring(0, 24);
    
    this.connectedUsers.forEach(user => {
      if (user.userId !== userId) {
        user.conversationIds.forEach(convId => {
          this.io?.to(`conversation:${convId}`).emit('user_status', {
            userId: sanitizedUserId,
            isOnline,
            timestamp: new Date()
          });
        });
      }
    });
  }

  private findSocketByUserId(userId: string): unknown {
    for (const [socketId, user] of this.connectedUsers.entries()) {
      if (user.userId === userId) {
        return this.io?.sockets.sockets.get(socketId);
      }
    }
    return null;
  }

  getConnectedUsers(): SocketUser[] {
    return Array.from(this.connectedUsers.values());
  }

  isUserOnline(userId: string): boolean {
    return Array.from(this.connectedUsers.values())
      .some(user => user.userId === userId);
  }
}

export const socketManager = new SocketManager();