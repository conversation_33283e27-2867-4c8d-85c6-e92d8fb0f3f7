"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from '@/components/ui/use-toast';
import { Shield, Send, CheckCircle } from 'lucide-react';

interface TestReportData {
  title: string;
  category: string;
  dateOfOccurrence: string;
  location: string;
  description: string;
  evidenceDescription: string;
  privacyConsent: boolean;
}

const categories = [
  "Financial Misconduct",
  "Accounting Fraud / Financial Manipulation", 
  "Corruption",
  "Harassment",
  "Safety Violation",
  "Environmental",
  "Discrimination",
  "Other"
];

export default function AnonymousReportTest() {
  const [formData, setFormData] = useState<TestReportData>({
    title: '',
    category: '',
    dateOfOccurrence: '',
    location: '',
    description: '',
    evidenceDescription: '',
    privacyConsent: false
  });
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [reportToken, setReportToken] = useState<string | null>(null);
  const [sessionId] = useState(`test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);

  const handleInputChange = (field: keyof TestReportData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const isFormValid = () => {
    return (
      formData.title.trim() !== '' &&
      formData.category !== '' &&
      formData.description.trim() !== '' &&
      formData.privacyConsent
    );
  };

  const handleSubmit = async () => {
    if (!isFormValid()) {
      toast({
        title: "Form Incomplete",
        description: "Please fill in all required fields and accept the privacy consent.",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch('/api/reports/anonymous/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          sessionId: sessionId,
          companyName: 'Test Company'
        }),
      });

      const result = await response.json();

      if (result.success) {
        setReportToken(result.data.reportToken);
        toast({
          title: "Report Submitted Successfully",
          description: "Your anonymous report has been securely submitted."
        });
      } else {
        throw new Error(result.error || 'Failed to submit report');
      }
    } catch (error) {
      console.error('Report submission error:', error);
      toast({
        title: "Submission Failed",
        description: "There was an error submitting your report. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleTestTracking = async () => {
    if (!reportToken) return;

    try {
      const response = await fetch('/api/reports/anonymous/track', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ reportToken }),
      });

      const result = await response.json();

      if (result.success) {
        toast({
          title: "Report Found",
          description: `Report Status: ${result.data.status} | Priority: ${result.data.priority}`
        });
      } else {
        throw new Error(result.error || 'Failed to track report');
      }
    } catch (error) {
      console.error('Track report error:', error);
      toast({
        title: "Tracking Failed",
        description: "Unable to track report. Please try again.",
        variant: "destructive"
      });
    }
  };

  if (reportToken) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader className="text-center">
          <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
          <CardTitle className="text-xl font-semibold text-gray-900">
            Test Report Submitted Successfully
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <div className="text-2xl font-mono font-bold text-[#1E4841] mb-4 tracking-wider">
              {reportToken}
            </div>
            <p className="text-sm text-gray-600 mb-4">
              Your test report token (save this for testing tracking functionality)
            </p>
          </div>

          <div className="flex gap-3">
            <Button
              onClick={handleTestTracking}
              className="flex-1 bg-[#1E4841] hover:bg-[#1E4841]/90"
            >
              Test Tracking
            </Button>
            <Button
              onClick={() => {
                setReportToken(null);
                setFormData({
                  title: '',
                  category: '',
                  dateOfOccurrence: '',
                  location: '',
                  description: '',
                  evidenceDescription: '',
                  privacyConsent: false
                });
              }}
              variant="outline"
            >
              Submit Another Test
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5 text-[#1E4841]" />
          Anonymous Report Test
        </CardTitle>
        <p className="text-sm text-gray-600">
          Test the anonymous reporting functionality
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Title */}
        <div className="space-y-2">
          <Label htmlFor="title" className="text-sm font-medium">
            Report Title*
          </Label>
          <Input
            id="title"
            type="text"
            placeholder="Test Report Title"
            value={formData.title}
            onChange={(e) => handleInputChange('title', e.target.value)}
          />
        </div>

        {/* Category */}
        <div className="space-y-2">
          <Label htmlFor="category" className="text-sm font-medium">
            Category*
          </Label>
          <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
            <SelectTrigger>
              <SelectValue placeholder="Select category" />
            </SelectTrigger>
            <SelectContent>
              {categories.map((category) => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Date */}
        <div className="space-y-2">
          <Label htmlFor="dateOfOccurrence" className="text-sm font-medium">
            Date of Occurrence
          </Label>
          <Input
            id="dateOfOccurrence"
            type="date"
            value={formData.dateOfOccurrence}
            onChange={(e) => handleInputChange('dateOfOccurrence', e.target.value)}
          />
        </div>

        {/* Location */}
        <div className="space-y-2">
          <Label htmlFor="location" className="text-sm font-medium">
            Location
          </Label>
          <Input
            id="location"
            type="text"
            placeholder="Test Location"
            value={formData.location}
            onChange={(e) => handleInputChange('location', e.target.value)}
          />
        </div>

        {/* Description */}
        <div className="space-y-2">
          <Label htmlFor="description" className="text-sm font-medium">
            Description*
          </Label>
          <Textarea
            id="description"
            placeholder="Test report description..."
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            className="min-h-[100px]"
          />
        </div>

        {/* Evidence Description */}
        <div className="space-y-2">
          <Label htmlFor="evidenceDescription" className="text-sm font-medium">
            Evidence Description
          </Label>
          <Textarea
            id="evidenceDescription"
            placeholder="Test evidence description..."
            value={formData.evidenceDescription}
            onChange={(e) => handleInputChange('evidenceDescription', e.target.value)}
          />
        </div>

        {/* Privacy Consent */}
        <div className="flex items-start space-x-3 p-4 border border-gray-200 rounded-lg">
          <Checkbox
            id="privacyConsent"
            checked={formData.privacyConsent}
            onCheckedChange={(checked) => handleInputChange('privacyConsent', checked as boolean)}
          />
          <div className="flex-1">
            <Label htmlFor="privacyConsent" className="text-sm font-medium cursor-pointer">
              I consent to the processing of this test report data*
            </Label>
          </div>
        </div>

        {/* Submit Button */}
        <Button
          onClick={handleSubmit}
          disabled={!isFormValid() || isSubmitting}
          className="w-full bg-[#1E4841] hover:bg-[#1E4841]/90"
        >
          {isSubmitting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Submitting Test Report...
            </>
          ) : (
            <>
              <Send className="h-4 w-4 mr-2" />
              Submit Test Report
            </>
          )}
        </Button>

        <p className="text-xs text-gray-500 text-center">
          Session ID: {sessionId}
        </p>
      </CardContent>
    </Card>
  );
}
