import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Footer, CardHeader } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { ChevronUp, ChevronDown, AlertTriangle } from "lucide-react";
import { FileText, Clock, CheckCircle } from "lucide-react";
import { UI_CONSTANTS } from "@/lib/client";
import { DashboardStat } from "@/lib/types";

// Minimal stats shape accepted by this component (works with both hooks)
interface StatsLike {
    totalReports: number;
    newReports: number;
    underReviewReports: number;
    awaitingResponseReports: number;
    resolvedReports: number;
    highPriorityReports: number;
    periodComparison: {
        totalReportsChange: number;
        newReportsChange: number;
        resolvedReportsChange: number;
        period: string;
    };
    chartData?: {
        overTime: Array<{ month: string; reports: number; cases: number }>;
        statusDistribution: Array<{ name: string; value: number; fill: string }>;
    };
    lastCalculated?: Date;
}

interface StatisticsCardsProps {
    className?: string;
    stats?: StatsLike | null;
    isLoading?: boolean;
}

export default function StatisticsCards({ className, stats: propStats, isLoading: propIsLoading }: StatisticsCardsProps) {
    // Use provided props
    const stats = propStats;
    const isLoading = propIsLoading;

    if (isLoading) {
        return (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 md:gap-6">
                {[...Array(4)].map((_, index) => (
                    <Card key={index} className="p-3 sm:p-4 md:p-6">
                        <CardContent className="p-0">
                            <div className="flex justify-between">
                                <div className="w-full">
                                    <Skeleton className="h-4 mb-2" />
                                    <Skeleton className="h-8" />
                                </div>
                                <Skeleton className="w-10 h-10 rounded-2xl" />
                            </div>
                            <Skeleton className="mt-4 h-4 w-3/4" />
                        </CardContent>
                    </Card>
                ))}
            </div>
        );
    }

    console.log('StatisticsCards: Received stats prop:', stats);
    console.log('StatisticsCards: isLoading prop:', isLoading);

    if (!stats) {
        console.log('StatisticsCards: No stats available, showing fallback');
        return (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 md:gap-6">
                <Card className="p-3 sm:p-4 md:p-6 col-span-full">
                    <CardContent className="p-0 text-center">
                        <p className="text-gray-500">No statistics available</p>
                    </CardContent>
                </Card>
            </div>
        );
    }
    const periodComparison = stats.periodComparison || {
        totalReportsChange: 0,
        resolvedReportsChange: 0,
        newReportsChange: 0,
        period: 'month'
    };

    // Helper function to get dynamic time text based on lastCalculated
    const getLastUpdatedText = () => {
        if (!stats.lastCalculated) return "Updated recently";

        const now = new Date();
        const lastCalc = new Date(stats.lastCalculated);
        const diffInHours = Math.floor((now.getTime() - lastCalc.getTime()) / (1000 * 60 * 60));
        const diffInDays = Math.floor(diffInHours / 24);

        if (diffInHours < 1) return "Updated recently";
        if (diffInHours < 24) return `Updated ${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
        if (diffInDays === 1) return "Updated 1 day ago";
        return `Updated ${diffInDays} days ago`;
    };

    // Helper function to get dynamic period text
    const getPeriodText = () => {
        const period = periodComparison.period || 'month';
        switch (period) {
            case 'quarter': return 'last quarter';
            case 'year': return 'last year';
            default: return 'last month';
        }
    };

    const dashboardStats: DashboardStat[] = [
        {
            title: "Total Reports Submitted",
            value: (stats.totalReports ?? 0).toString(),
            subtitle: `${periodComparison.totalReportsChange >= 0 ? '+' : ''}${Math.abs(periodComparison.totalReportsChange)} new since ${getPeriodText()}`,
            icon: FileText,
            color: "#1E4841",
            subcolor: "#ECF4E9",
            indicator: {
                type: periodComparison.totalReportsChange >= 0 ? "increase" : "decrease",
                text: `${Math.abs(periodComparison.totalReportsChange)} ${periodComparison.totalReportsChange >= 0 ? 'new' : 'less'}`,
                color: periodComparison.totalReportsChange >= 0 ? "#22C55E" : "#EF4444"
            }
        },
        {
            title: "Reports Under Review",
            value: (stats.underReviewReports ?? 0).toString(),
            subtitle: getLastUpdatedText(),
            icon: Clock,
            color: "#F97316",
            subcolor: "#F973161A",
            indicator: {
                type: "neutral",
                text: getLastUpdatedText(),
                color: "#6B7280"
            }
        },
        {
            title: "Awaiting Your Response",
            value: (stats.awaitingResponseReports ?? 0).toString(),
            subtitle: stats.awaitingResponseReports > 0 ? "Action required" : "All up to date",
            icon: AlertTriangle,
            color: "#EF4444",
            subcolor: "#EF44441A",
            indicator: {
                type: stats.awaitingResponseReports > 0 ? "warning" : "neutral",
                text: stats.awaitingResponseReports > 0 ? "Action required" : "All up to date",
                color: stats.awaitingResponseReports > 0 ? "#EF4444" : "#6B7280"
            }
        },
        {
            title: "Resolved Cases",
            value: (stats.resolvedReports ?? 0).toString(),
            subtitle: `${periodComparison.resolvedReportsChange >= 0 ? '+' : ''}${Math.abs(periodComparison.resolvedReportsChange)} more than ${getPeriodText().replace('last ', 'last ')}`,
            icon: CheckCircle,
            color: "#22C55E",
            subcolor: "#22C55E1A",
            indicator: {
                type: periodComparison.resolvedReportsChange >= 0 ? "increase" : "decrease",
                text: `${Math.abs(periodComparison.resolvedReportsChange)} ${periodComparison.resolvedReportsChange >= 0 ? 'more' : 'less'}`,
                color: periodComparison.resolvedReportsChange >= 0 ? "#22C55E" : "#EF4444"
            }
        },
    ];

    return (
        <div className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 md:gap-6 ${className || ''}`}>
            {dashboardStats.map((stat, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow duration-300 p-3 sm:p-4 md:p-6">
                    <CardContent className="flex flex-col justify-between p-0">
                        <div className="flex justify-between">
                            <CardHeader className="w-full p-0">
                                <p className="text-xs sm:text-sm font-medium text-[#242E2C]">{stat.title}</p>
                                <p className={`text-2xl sm:text-3xl xl:text-3xl font-semibold`} style={{ color: stat.color }}>{stat.value}</p>
                            </CardHeader>
                            <div className={`p-2 sm:p-2.5 rounded-2xl w-fit h-fit`} style={{ backgroundColor: stat.subcolor, color: stat.color }}>
                                <stat.icon className="w-4 h-4 sm:w-5 sm:h-5" />
                            </div>
                        </div>
                        <CardFooter className="p-0 mt-3 sm:mt-4">
                            <p className={`text-xs sm:text-sm font-normal flex items-center gap-1/2`} style={{ color: stat.indicator.color }}>
                                {stat.indicator.type === "increase" && <ChevronUp className="h-3 w-3 sm:h-4 sm:w-4" />}
                                {stat.indicator.type === "decrease" && <ChevronDown className="h-3 w-3 sm:h-4 sm:w-4" />}
                                {stat.indicator.type === "warning" && <AlertTriangle className="h-3 w-3 sm:h-4 sm:w-4" />}
                                {stat.indicator.text}
                                {stat.indicator.type === "increase" || stat.indicator.type === "decrease" ?
                                    <span className="ml-1 inline" style={{ color: UI_CONSTANTS.colors.textSecondary }}>{stat.title === "Total Reports Submitted" ? "since last month" : "than last quarter"}</span> :
                                    null}
                            </p>
                        </CardFooter>
                    </CardContent>
                </Card>
            ))}
        </div>
    );
}