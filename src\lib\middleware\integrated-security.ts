/**
 * Integrated Security Middleware
 * Combines all security features into a unified middleware system
 */

import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { withSecurityHeaders, withRateLimit, withCSRFProtection, enforceHTTPS } from './security';
import { validateSessionToken } from '../auth/session-manager';
import { SecurityAuditLogger } from '../security/audit-logger';
import { validateFile } from './validation';

export interface SecurityConfig {
  enforceHTTPS?: boolean;
  rateLimit?: {
    enabled: boolean;
    limit: number;
    windowMs: number;
  };
  csrf?: {
    enabled: boolean;
    exemptPaths?: string[];
  };
  sessionValidation?: {
    enabled: boolean;
    exemptPaths?: string[];
  };
  auditLogging?: {
    enabled: boolean;
    logAllRequests?: boolean;
  };
}

const DEFAULT_CONFIG: SecurityConfig = {
  enforceHTTPS: process.env.NODE_ENV === 'production',
  rateLimit: {
    enabled: true,
    limit: 100,
    windowMs: 15 * 60 * 1000 // 15 minutes
  },
  csrf: {
    enabled: true,
    exemptPaths: ['/api/auth/callback', '/api/webhooks']
  },
  sessionValidation: {
    enabled: true,
    exemptPaths: [
      '/api/auth/login',
      '/api/auth/signup',
      '/api/auth/anonymous',
      '/api/auth/callback',
      '/api/health',
      '/api/testimonials',
      '/api/contact'
    ]
  },
  auditLogging: {
    enabled: true,
    logAllRequests: false
  }
};

/**
 * Comprehensive security middleware
 */
export function withIntegratedSecurity(
  handler: (request: NextRequest) => Promise<NextResponse>,
  config: Partial<SecurityConfig> = {}
) {
  const securityConfig = { ...DEFAULT_CONFIG, ...config };

  return async (request: NextRequest): Promise<NextResponse> => {
    const startTime = Date.now();
    let response: NextResponse;

    try {
      // 1. HTTPS Enforcement
      if (securityConfig.enforceHTTPS) {
        const httpsRedirect = enforceHTTPS(request);
        if (httpsRedirect) {
          return httpsRedirect;
        }
      }

      // 2. Rate Limiting
      if (securityConfig.rateLimit?.enabled) {
        const rateLimitPassed = withRateLimit(
          request,
          securityConfig.rateLimit.limit,
          securityConfig.rateLimit.windowMs
        );

        if (!rateLimitPassed) {
          await SecurityAuditLogger.logRateLimitExceeded(
            request,
            securityConfig.rateLimit.limit,
            securityConfig.rateLimit.windowMs
          );

          return NextResponse.json(
            { success: false, error: 'Rate limit exceeded' },
            { status: 429 }
          );
        }
      }

      // 3. CSRF Protection
      if (securityConfig.csrf?.enabled) {
        const path = request.nextUrl.pathname;
        const isExempt = securityConfig.csrf.exemptPaths?.some(exemptPath =>
          path.startsWith(exemptPath)
        );

        if (!isExempt && !withCSRFProtection(request)) {
          await SecurityAuditLogger.logCSRFAttempt(request);

          return NextResponse.json(
            { success: false, error: 'CSRF validation failed' },
            { status: 403 }
          );
        }
      }

      // 4. Session Validation (for protected routes)
      if (securityConfig.sessionValidation?.enabled) {
        const path = request.nextUrl.pathname;
        const isExempt = securityConfig.sessionValidation.exemptPaths?.some(exemptPath =>
          path.startsWith(exemptPath)
        );

        if (!isExempt && path.startsWith('/api/')) {
          const authHeader = request.headers.get('authorization');
          
          if (authHeader && authHeader.startsWith('Bearer ')) {
            const token = authHeader.substring(7);
            const sessionValidation = await validateSessionToken(token);

            if (!sessionValidation.valid) {
              await SecurityAuditLogger.logSuspiciousActivity(
                request,
                'Invalid session token used',
                { reason: sessionValidation.reason }
              );

              return NextResponse.json(
                { success: false, error: 'Session invalid' },
                { status: 401 }
              );
            }

            // Add user info to request for downstream handlers
            (request as NextRequest & { user?: jwt.JwtPayload | string }).user = sessionValidation.decoded;
          }
        }
      }

      // 5. Execute the main handler
      response = await handler(request);

      // 6. Apply security headers
      response = withSecurityHeaders(response);

      // 7. Audit logging (if enabled)
      if (securityConfig.auditLogging?.enabled) {
        const shouldLog = securityConfig.auditLogging.logAllRequests ||
          response.status >= 400 ||
          request.nextUrl.pathname.includes('/auth/') ||
          request.nextUrl.pathname.includes('/admin/');

        if (shouldLog) {
          // Log significant requests
          const processingTime = Date.now() - startTime;
          
          if (response.status >= 400) {
            await SecurityAuditLogger.logSuspiciousActivity(
              request,
              `HTTP ${response.status} response`,
              {
                path: request.nextUrl.pathname,
                method: request.method,
                processingTime,
                statusCode: response.status
              }
            );
          }
        }
      }

      return response;

    } catch (error) {
      // Log security middleware errors
      await SecurityAuditLogger.logSuspiciousActivity(
        request,
        'Security middleware error',
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          path: request.nextUrl.pathname,
          method: request.method
        }
      );

      return NextResponse.json(
        { success: false, error: 'Security validation failed' },
        { status: 500 }
      );
    }
  };
}

/**
 * Security middleware for file upload endpoints
 */
export function withFileUploadSecurity(
  handler: (request: NextRequest, validatedFiles: File[]) => Promise<NextResponse>,
  options: {
    maxFiles?: number;
    maxFileSize?: number;
    allowedTypes?: string[];
    requireSignatureCheck?: boolean;
  } = {}
) {
  return withIntegratedSecurity(async (request: NextRequest) => {
    try {
      // Only process multipart/form-data requests
      const contentType = request.headers.get('content-type');
      if (!contentType?.includes('multipart/form-data')) {
        return NextResponse.json(
          { success: false, error: 'Invalid content type for file upload' },
          { status: 400 }
        );
      }

      const formData = await request.formData();
      const files: File[] = [];

      // Extract and validate files
      for (const [, value] of formData.entries()) {
        if (value instanceof File) {
          files.push(value);
        }
      }

      // Check file count
      if (options.maxFiles && files.length > options.maxFiles) {
        return NextResponse.json(
          { success: false, error: `Too many files. Maximum ${options.maxFiles} allowed.` },
          { status: 400 }
        );
      }

      // Validate each file
      const validatedFiles: File[] = [];
      for (const file of files) {
        const validation = validateFile(file);

        if (!validation.valid) {
          await SecurityAuditLogger.logSuspiciousActivity(
            request,
            'Malicious file upload attempt',
            {
              fileName: file.name,
              fileType: file.type,
              fileSize: file.size,
              error: validation.error
            }
          );

          return NextResponse.json(
            {
              success: false,
              error: 'File validation failed',
              details: validation.error
            },
            { status: 400 }
          );
        }

        validatedFiles.push(file);
      }

      return handler(request, validatedFiles);

    } catch (error) {
      await SecurityAuditLogger.logSuspiciousActivity(
        request,
        'File upload security error',
        { error: error instanceof Error ? error.message : 'Unknown error' }
      );

      return NextResponse.json(
        { success: false, error: 'File upload validation failed' },
        { status: 500 }
      );
    }
  });
}

/**
 * Security middleware for admin endpoints
 */
export function withAdminSecurity(
  handler: (request: NextRequest) => Promise<NextResponse>
) {
  return withIntegratedSecurity(handler, {
    rateLimit: {
      enabled: true,
      limit: 50, // Stricter rate limiting for admin endpoints
      windowMs: 15 * 60 * 1000
    },
    auditLogging: {
      enabled: true,
      logAllRequests: true // Log all admin requests
    }
  });
}

/**
 * Security middleware for authentication endpoints
 */
export function withAuthSecurity(
  handler: (request: NextRequest) => Promise<NextResponse>
) {
  return withIntegratedSecurity(handler, {
    rateLimit: {
      enabled: true,
      limit: 10, // Very strict rate limiting for auth endpoints
      windowMs: 15 * 60 * 1000
    },
    sessionValidation: {
      enabled: false // Auth endpoints don't need session validation
    },
    auditLogging: {
      enabled: true,
      logAllRequests: true // Log all auth attempts
    }
  });
}

/**
 * Security middleware for public endpoints
 */
export function withPublicSecurity(
  handler: (request: NextRequest) => Promise<NextResponse>
) {
  return withIntegratedSecurity(handler, {
    sessionValidation: {
      enabled: false // Public endpoints don't need session validation
    },
    csrf: {
      enabled: false // Public GET endpoints don't need CSRF protection
    },
    rateLimit: {
      enabled: true,
      limit: 200, // More lenient for public endpoints
      windowMs: 15 * 60 * 1000
    }
  });
}
