"use client";

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, Copy, ExternalLink, Shield, AlertCircle } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface ReportFormData {
  title: string;
  category: string;
  dateOfOccurrence: string;
  location: string;
  description: string;
  evidenceFiles: File[];
  evidenceDescription: string;
  privacyConsent: boolean;
  reportToken?: string;
  submissionId?: string;
}

interface AnonymousSession {
  companyName: string;
  sessionId: string;
  expiresAt: string;
}

interface ConfirmationStepProps {
  formData: ReportFormData;
  session: AnonymousSession;
}

export default function ConfirmationStep({ formData, session }: ConfirmationStepProps) {
  const [tokenCopied, setTokenCopied] = useState(false);

  // Generate a mock report token for demonstration (in real implementation, this comes from the API)
  const reportToken = formData.reportToken || `RPT-${Date.now().toString().slice(-8)}X${Math.random().toString(36).substr(2, 4).toUpperCase()}`;

  const handleCopyToken = async () => {
    try {
      await navigator.clipboard.writeText(reportToken);
      setTokenCopied(true);
      toast({
        title: "Token Copied",
        description: "Your report token has been copied to clipboard."
      });
      setTimeout(() => setTokenCopied(false), 3000);
    } catch {
      toast({
        title: "Copy Failed",
        description: "Unable to copy token. Please select and copy manually.",
        variant: "destructive"
      });
    }
  };

  const handlePrintToken = () => {
    const printContent = `
      <div style="font-family: Arial, sans-serif; padding: 20px; max-width: 600px;">
        <h1 style="color: #1E4841; margin-bottom: 20px;">Report Submission Confirmation</h1>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <h2 style="margin-top: 0;">Your Unique Report Token</h2>
          <div style="font-size: 24px; font-weight: bold; color: #1E4841; background: white; padding: 15px; border-radius: 4px; text-align: center; letter-spacing: 2px;">
            ${reportToken}
          </div>
        </div>
        <div style="margin-bottom: 20px;">
          <h3>Important Information:</h3>
          <ul style="line-height: 1.6;">
            <li>Save this token to track your report status and access secure communications</li>
            <li>Your report will be reviewed by our team</li>
            <li>You can check for updates in the Secure Inbox using your token</li>
            <li>Keep this token confidential and secure</li>
          </ul>
        </div>
        <div style="border-top: 1px solid #ddd; padding-top: 20px; font-size: 12px; color: #666;">
          <p>Report submitted on: ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}</p>
          <p>Company: ${session.companyName}</p>
          <p>© 2025 Secure Reporting System. All rights reserved.</p>
        </div>
      </div>
    `;

    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Report Token - ${reportToken}</title>
            <style>
              @media print {
                body { margin: 0; }
                .no-print { display: none; }
              }
            </style>
          </head>
          <body>
            ${printContent}
            <div class="no-print" style="text-align: center; margin-top: 30px;">
              <button onclick="window.print()" style="background: #1E4841; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">Print</button>
              <button onclick="window.close()" style="background: #6b7280; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin-left: 10px;">Close</button>
            </div>
          </body>
        </html>
      `);
      printWindow.document.close();
    }
  };

  const handleAccessSecureInbox = () => {
    // Store the token for secure inbox access
    localStorage.setItem('reportToken', reportToken);
    window.open('/dashboard/anonymous/secure-inbox', '_blank');
  };

  const handleExit = () => {
    // Clear anonymous session
    localStorage.removeItem('anonymousSession');
    window.location.href = '/';
  };

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      {/* Success Message */}
      <Card className="border-green-200 bg-green-50">
        <CardContent className="pt-6">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Your report has been securely submitted
            </h2>
            <p className="text-gray-600">
              Thank you for your submission. Your report is now being processed
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Report Token */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-gray-900">
            Your Unique Report Token
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <div className="text-3xl font-mono font-bold text-[#1E4841] mb-4 tracking-wider">
              {reportToken}
            </div>
            <div className="flex justify-center gap-3">
              <Button
                onClick={handleCopyToken}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                <Copy className="h-4 w-4" />
                {tokenCopied ? 'Copied!' : 'Copy'}
              </Button>
              <Button
                onClick={handlePrintToken}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                <ExternalLink className="h-4 w-4" />
                Print
              </Button>
            </div>
          </div>
          
          <p className="text-sm text-gray-600 text-center">
            Save this token to track your report status and access secure communications
          </p>
        </CardContent>
      </Card>

      {/* Next Steps */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg font-semibold text-gray-900">
            <AlertCircle className="h-5 w-5 text-green-600" />
            Next Steps
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
            <p className="text-sm text-green-800 mb-3">
              Your report will be reviewed by our team. You can check for updates in the Secure Inbox using your token.
            </p>
            <Button
              onClick={handleAccessSecureInbox}
              className="bg-[#1E4841] hover:bg-[#1E4841]/90 text-white flex items-center gap-2"
            >
              <Shield className="h-4 w-4" />
              Access Secure Inbox
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Important Notice */}
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          <strong>Important:</strong> Keep your report token safe and confidential. You will need it to access updates and communicate securely about your report.
        </AlertDescription>
      </Alert>

      {/* Exit Button */}
      <div className="text-center pt-6">
        <Button
          onClick={handleExit}
          variant="outline"
          className="px-8 py-2"
        >
          Exit
        </Button>
      </div>
    </div>
  );
}
