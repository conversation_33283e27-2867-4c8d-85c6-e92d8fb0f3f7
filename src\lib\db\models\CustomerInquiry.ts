import mongoose, { Schema, Document } from 'mongoose';

export interface ICustomerInquiry extends Document {
  subject: 'account_setup' | 'feature_training' | 'best_practices' | 'integration_support' | 'performance_review' | 'renewal_discussion' | 'other';
  message: string;
  preferredContactMethod: 'email' | 'phone' | 'video_call' | 'in_person';
  preferredTimeSlot: 'morning' | 'afternoon' | 'evening' | 'flexible';
  isUrgent: boolean;
  status: 'open' | 'contacted' | 'in_progress' | 'resolved' | 'closed';
  assignedTo?: Schema.Types.ObjectId;
  customerEmail?: string;
  customerName?: string;
  companyId?: Schema.Types.ObjectId;
  inquiryNumber: string;
  contactedAt?: Date;
  resolvedAt?: Date;
  notes?: {
    content: string;
    addedBy: Schema.Types.ObjectId;
    addedAt: Date;
  }[];
  metadata: {
    userAgent?: string;
    ipAddress?: string;
    source: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

const CustomerInquirySchema = new Schema<ICustomerInquiry>({
  subject: {
    type: String,
    enum: ['account_setup', 'feature_training', 'best_practices', 'integration_support', 'performance_review', 'renewal_discussion', 'other'],
    required: true
  },
  message: {
    type: String,
    required: true,
    minlength: 10,
    maxlength: 2000
  },
  preferredContactMethod: {
    type: String,
    enum: ['email', 'phone', 'video_call', 'in_person'],
    required: true
  },
  preferredTimeSlot: {
    type: String,
    enum: ['morning', 'afternoon', 'evening', 'flexible'],
    required: true
  },
  isUrgent: {
    type: Boolean,
    default: false
  },
  status: {
    type: String,
    enum: ['open', 'contacted', 'in_progress', 'resolved', 'closed'],
    default: 'open'
  },
  assignedTo: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  customerEmail: {
    type: String,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  customerName: {
    type: String,
    maxlength: 100
  },
  companyId: {
    type: Schema.Types.ObjectId,
    ref: 'Company'
  },
  inquiryNumber: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  contactedAt: {
    type: Date
  },
  resolvedAt: {
    type: Date
  },
  notes: [{
    content: {
      type: String,
      required: true,
      maxlength: 1000
    },
    addedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    addedAt: {
      type: Date,
      default: Date.now
    }
  }],
  metadata: {
    userAgent: String,
    ipAddress: String,
    source: {
      type: String,
      default: 'customer_success_form'
    }
  }
}, {
  timestamps: true
});

// Indexes for efficient queries
CustomerInquirySchema.index({ customerEmail: 1, createdAt: -1 });
CustomerInquirySchema.index({ status: 1, isUrgent: -1 });
// inquiryNumber index is already created by unique: true, index: true in field definition
CustomerInquirySchema.index({ assignedTo: 1, status: 1 });
CustomerInquirySchema.index({ companyId: 1, status: 1 });

// Generate inquiry number before saving
CustomerInquirySchema.pre('save', async function(next) {
  if (this.isNew) {
    const count = await mongoose.model('CustomerInquiry').countDocuments();
    this.inquiryNumber = `CSM-${new Date().getFullYear()}-${String(count + 1).padStart(6, '0')}`;
  }
  next();
});

// Virtual for formatted inquiry display
CustomerInquirySchema.virtual('formattedInquiry').get(function() {
  return this.inquiryNumber;
});

// Method to check if inquiry is overdue
CustomerInquirySchema.methods.isOverdue = function() {
  if (this.status === 'resolved' || this.status === 'closed') return false;
  
  const now = new Date();
  const createdAt = new Date(this.createdAt);
  const hoursDiff = (now.getTime() - createdAt.getTime()) / (1000 * 60 * 60);
  
  // SLA: 24 hours for urgent, 48 hours for normal
  const slaHours = this.isUrgent ? 24 : 48;
  
  return hoursDiff > slaHours;
};

// Method to add note
CustomerInquirySchema.methods.addNote = function(content: string, addedBy: Schema.Types.ObjectId) {
  this.notes = this.notes || [];
  this.notes.push({
    content,
    addedBy,
    addedAt: new Date()
  });
  return this.save();
};

// Static method to get statistics
CustomerInquirySchema.statics.getStats = async function() {
  const stats = await this.aggregate([
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 }
      }
    }
  ]);
  
  const urgentCount = await this.countDocuments({ 
    isUrgent: true, 
    status: { $in: ['open', 'contacted', 'in_progress'] } 
  });
  
  const subjectStats = await this.aggregate([
    {
      $match: { status: { $in: ['open', 'contacted', 'in_progress'] } }
    },
    {
      $group: {
        _id: '$subject',
        count: { $sum: 1 }
      }
    }
  ]);
  
  return { statusStats: stats, urgentCount, subjectStats };
};

// Static method to find overdue inquiries
CustomerInquirySchema.statics.findOverdue = async function() {
  const now = new Date();
  const urgentCutoff = new Date(now.getTime() - 24 * 60 * 60 * 1000); // 24 hours ago
  const normalCutoff = new Date(now.getTime() - 48 * 60 * 60 * 1000); // 48 hours ago
  
  return this.find({
    status: { $in: ['open', 'contacted', 'in_progress'] },
    $or: [
      { isUrgent: true, createdAt: { $lt: urgentCutoff } },
      { isUrgent: false, createdAt: { $lt: normalCutoff } }
    ]
  }).populate('assignedTo', 'firstName lastName email');
};

export default mongoose.models.CustomerInquiry || mongoose.model<ICustomerInquiry>('CustomerInquiry', CustomerInquirySchema);
