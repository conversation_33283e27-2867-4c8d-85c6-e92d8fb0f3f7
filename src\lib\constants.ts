// Centralized constants and configuration values

// Application Configuration
export const APP_CONFIG = {
  name: "7IRIS Whistleblower Platform",
  version: "1.0.0",
  supportEmail: "<EMAIL>",
  maxFileSize: 50 * 1024 * 1024, // 50MB in bytes
  allowedFileTypes: [
    "image/jpeg",
    "image/png", 
    "image/gif",
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
  ],
  sessionTimeout: 30 * 60 * 1000, // 30 minutes in milliseconds
  reportIdPrefix: "WB-",
  defaultLanguage: "en"
} as const;

// UI Constants
export const UI_CONSTANTS = {
  colors: {
    primary: "#1E4841",
    secondary: "#BBF49C", 
    accent: "#ECF4E9",
    text: "#242E2C",
    textSecondary: "#6B7271",
    success: "#22C55E",
    warning: "#F97316",
    error: "#EF4444",
    info: "#3B82F6"
  },
  breakpoints: {
    sm: "640px",
    md: "768px", 
    lg: "1024px",
    xl: "1280px",
    "2xl": "1536px"
  },
  animations: {
    duration: {
      fast: "150ms",
      normal: "300ms",
      slow: "500ms"
    },
    easing: {
      default: "cubic-bezier(0.4, 0, 0.2, 1)",
      in: "cubic-bezier(0.4, 0, 1, 1)",
      out: "cubic-bezier(0, 0, 0.2, 1)"
    }
  }
} as const;

// Report Status Configuration
export const REPORT_STATUS_CONFIG = {
  "New": {
    color: "bg-blue-100 text-blue-800",
    priority: 1,
    description: "Report has been submitted and is awaiting initial review"
  },
  "Under Review": {
    color: "bg-[#F973161A] text-[#F97316]",
    priority: 2,
    description: "Report is currently being investigated"
  },
  "Awaiting Response": {
    color: "bg-[#EF44441A] text-[#EF4444]",
    priority: 3,
    description: "Additional information is required from the reporter"
  },
  "Resolved": {
    color: "bg-[#22C55E1A] text-[#22C55E]",
    priority: 4,
    description: "Investigation has been completed and case is closed"
  },
  "Closed": {
    color: "bg-gray-100 text-gray-800",
    priority: 5,
    description: "Case has been closed without resolution"
  }
} as const;

// Priority Configuration
export const PRIORITY_CONFIG = {
  "Low": {
    color: "bg-gray-100 text-gray-800",
    level: 1,
    sla: 30 // days
  },
  "Medium": {
    color: "bg-blue-100 text-blue-800",
    level: 2,
    sla: 14 // days
  },
  "High": {
    color: "bg-orange-100 text-orange-800", 
    level: 3,
    sla: 7 // days
  },
  "Critical": {
    color: "bg-red-100 text-red-800",
    level: 4,
    sla: 2 // days
  }
} as const;

// Category Configuration
export const CATEGORY_CONFIG = {
  "Financial": {
    icon: "DollarSign",
    color: "#22C55E",
    description: "Financial fraud, embezzlement, accounting irregularities"
  },
  "Workplace Safety": {
    icon: "Shield",
    color: "#F97316",
    description: "Safety violations, environmental concerns, health hazards"
  },
  "Ethics Violation": {
    icon: "AlertTriangle",
    color: "#EF4444",
    description: "Code of conduct violations, conflicts of interest"
  },
  "Data Privacy": {
    icon: "Lock",
    color: "#3B82F6",
    description: "Data breaches, privacy violations, unauthorized access"
  },
  "Harassment": {
    icon: "UserX",
    color: "#8B5CF6",
    description: "Workplace harassment, discrimination, bullying"
  },
  "Discrimination": {
    icon: "Users",
    color: "#EC4899",
    description: "Unfair treatment based on protected characteristics"
  },
  "Other": {
    icon: "HelpCircle",
    color: "#6B7280",
    description: "Other compliance or ethical concerns"
  }
} as const;

// Notification Configuration
export const NOTIFICATION_CONFIG = {
  types: {
    system: {
      icon: "Settings",
      color: "#6B7280",
      defaultPriority: "low"
    },
    report_update: {
      icon: "FileText",
      color: "#3B82F6",
      defaultPriority: "medium"
    },
    message: {
      icon: "MessageSquare",
      color: "#22C55E",
      defaultPriority: "high"
    },
    alert: {
      icon: "AlertTriangle",
      color: "#EF4444",
      defaultPriority: "urgent"
    }
  },
  priorities: {
    low: {
      color: "bg-gray-100 text-gray-800",
      urgency: 1
    },
    medium: {
      color: "bg-blue-100 text-blue-800",
      urgency: 2
    },
    high: {
      color: "bg-orange-100 text-orange-800",
      urgency: 3
    },
    urgent: {
      color: "bg-red-100 text-red-800",
      urgency: 4
    }
  }
} as const;

// API Endpoints
export const API_ENDPOINTS = {
  auth: {
    login: "/api/auth/login",
    logout: "/api/auth/logout", 
    register: "/api/auth/register",
    refresh: "/api/auth/refresh"
  },
  reports: {
    list: "/api/reports",
    create: "/api/reports",
    update: "/api/reports",
    delete: "/api/reports"
  },
  messages: {
    list: "/api/messages",
    send: "/api/messages",
    conversations: "/api/conversations"
  },
  notifications: {
    list: "/api/notifications",
    markRead: "/api/notifications/read",
    markAllRead: "/api/notifications/read-all"
  },
  users: {
    profile: "/api/users/profile",
    settings: "/api/users/settings"
  }
} as const;

// Validation Rules
export const VALIDATION_RULES = {
  email: {
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message: "Please enter a valid email address"
  },
  password: {
    minLength: 8,
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    message: "Password must be at least 8 characters with uppercase, lowercase, number, and special character"
  },
  reportTitle: {
    minLength: 10,
    maxLength: 200,
    message: "Report title must be between 10 and 200 characters"
  },
  reportDescription: {
    minLength: 50,
    maxLength: 5000,
    message: "Report description must be between 50 and 5000 characters"
  }
} as const;

// Feature Flags
export const FEATURE_FLAGS = {
  enableAnonymousReporting: true,
  enableFileUploads: true,
  enableRealTimeNotifications: true,
  enableAdvancedAnalytics: false,
  enableMultiLanguage: true,
  enableTwoFactorAuth: true,
  enableAuditLogs: true
} as const;

// Default Values
export const DEFAULTS = {
  pagination: {
    pageSize: 10,
    maxPageSize: 100
  },
  dateFormat: "MMM dd, yyyy",
  timeFormat: "HH:mm",
  timezone: "UTC",
  language: "en",
  theme: "light" as const,
  notifications: {
    email: true,
    push: true,
    sms: false
  }
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  generic: "An unexpected error occurred. Please try again.",
  network: "Network error. Please check your connection and try again.",
  unauthorized: "You are not authorized to perform this action.",
  forbidden: "Access denied. You don't have permission to access this resource.",
  notFound: "The requested resource was not found.",
  validation: "Please check your input and try again.",
  fileUpload: "File upload failed. Please check file size and format.",
  sessionExpired: "Your session has expired. Please log in again."
} as const;

// Success Messages  
export const SUCCESS_MESSAGES = {
  reportSubmitted: "Your report has been submitted successfully.",
  profileUpdated: "Your profile has been updated successfully.",
  passwordChanged: "Your password has been changed successfully.",
  notificationRead: "Notification marked as read.",
  fileUploaded: "File uploaded successfully.",
  messageSent: "Message sent successfully."
} as const;