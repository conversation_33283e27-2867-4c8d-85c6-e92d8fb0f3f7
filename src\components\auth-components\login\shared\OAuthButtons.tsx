"use client";

import { But<PERSON> } from "@/components/ui/button";
import { signIn } from "next-auth/react";
import Image from "next/image";
import { useState } from "react";

interface OAuthButtonsProps {
  callbackUrl?: string;
  role?: 'admin' | 'whistleblower';
}

export default function OAuthButtons({ callbackUrl = "/dashboard" }: OAuthButtonsProps) {
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const [isMicrosoftLoading, setIsMicrosoftLoading] = useState(false);

  // Note: role parameter is accepted for API compatibility but not used in OAuth flow
  // Role assignment is handled by NextAuth configuration based on user's domain/email

  const handleGoogleSignIn = async () => {
    try {
      setIsGoogleLoading(true);
      await signIn("google", { callbackUrl });
    } catch (error) {
      console.error("Google sign in error:", error);
    }
  };

  const handleMicrosoftSignIn = async () => {
    try {
      setIsMicrosoftLoading(true);
      await signIn("microsoft", { callbackUrl });
    } catch (error) {
      console.error("Microsoft sign in error:", error);
    }
  };

  return (
    <div className="flex space-x-4 my-4">
      <Button
        onClick={handleGoogleSignIn}
        disabled={isGoogleLoading}
        className="flex-1 flex items-center justify-center gap-2 bg-transparent h-12"
        variant="outline"
        type="button"
      >
        {isGoogleLoading ? (
          <div className="h-5 w-5 animate-spin rounded-full border-2 border-gray-300 border-t-black" />
        ) : (
          <Image
            src="/(auth)/login/google.svg"
            alt="Google"
            width={25}
            height={24}
            className="h-5 w-auto"
          />
        )}
        Continue with Google
      </Button>
      <Button
        onClick={handleMicrosoftSignIn}
        disabled={isMicrosoftLoading}
        className="flex-1 flex items-center justify-center gap-2 bg-transparent h-12"
        variant="outline"
        type="button"
      >
        {isMicrosoftLoading ? (
          <div className="h-5 w-5 animate-spin rounded-full border-2 border-gray-300 border-t-black" />
        ) : (
          <Image
            src="/(auth)/login/microsoft.svg"
            alt="Microsoft"
            width={24}
            height={24}
            className="h-5 w-auto"
          />
        )}
        Continue with Microsoft
      </Button>
    </div>
  );
}