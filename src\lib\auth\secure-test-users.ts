/**
 * Secure test user management system
 * This replaces hardcoded users with a more secure approach
 */

import bcrypt from 'bcryptjs';
import crypto from 'crypto';
import { DataService } from '@/lib/db/dataService';
import connectDB from '@/lib/db/mongodb';

export interface TestUser {
  email: string;
  firstName: string;
  lastName: string;
  role: 'admin' | 'investigator' | 'whistleblower';
  companyName: string;
  isActive: boolean;
}

// Test users configuration (passwords are generated securely)
const TEST_USERS: TestUser[] = [
  {
    email: '<EMAIL>',
    firstName: 'Sarah',
    lastName: '<PERSON>',
    role: 'admin',
    companyName: 'TechCorp Industries',
    isActive: true
  },
  {
    email: '<EMAIL>',
    firstName: 'Michael',
    lastName: 'Chen',
    role: 'investigator',
    companyName: 'TechCorp Industries',
    isActive: true
  },
  {
    email: '<EMAIL>',
    firstName: '<PERSON>',
    lastName: '<PERSON><PERSON>',
    role: 'whistleblower',
    companyName: 'TechCorp Industries',
    isActive: true
  },
  {
    email: '<EMAIL>',
    firstName: 'Emma',
    lastName: '<PERSON>',
    role: 'admin',
    companyName: 'Global Manufacturing Ltd',
    isActive: true
  },
  {
    email: '<EMAIL>',
    firstName: 'David',
    lastName: 'Brown',
    role: 'investigator',
    companyName: 'Global Manufacturing Ltd',
    isActive: true
  }
];

/**
 * Generate secure random password
 */
function generateSecurePassword(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
  let password = '';
  
  for (let i = 0; i < 16; i++) {
    password += chars.charAt(crypto.randomInt(0, chars.length));
  }
  
  return password;
}

/**
 * Create test users in database with secure passwords
 * Only works in development mode
 */
export async function createSecureTestUsers(): Promise<{ success: boolean; users?: Array<{ email: string; password: string; role: string }> }> {
  if (process.env.NODE_ENV === 'production') {
    throw new Error('Test users cannot be created in production mode');
  }

  try {
    await connectDB();
    const createdUsers: Array<{ email: string; password: string; role: string }> = [];

    for (const testUser of TEST_USERS) {
      // Check if user already exists
      const existingUser = await DataService.getUserByEmail(testUser.email);
      if (existingUser) {
        console.log(`Test user ${testUser.email} already exists, skipping...`);
        continue;
      }

      // Generate secure password
      const password = generateSecurePassword();
      
      // Find or create company
      let company = await DataService.getCompanyByName(testUser.companyName);
      if (!company) {
        company = await DataService.createCompany({
          name: testUser.companyName,
          contactEmail: `contact@${testUser.companyName.toLowerCase().replace(/\s+/g, '')}.com`,
          website: `https://${testUser.companyName.toLowerCase().replace(/\s+/g, '')}.com`,
          isActive: true
        });
      }

      // Create user with hashed password
      await DataService.createUser({
        email: testUser.email,
        firstName: testUser.firstName,
        lastName: testUser.lastName,
        password,
        role: testUser.role,
        companyId: company._id.toString(),
        isActive: testUser.isActive
      });

      createdUsers.push({
        email: testUser.email,
        password,
        role: testUser.role
      });

      console.log(`✅ Created test user: ${testUser.email} (${testUser.role})`);
    }

    return { success: true, users: createdUsers };
  } catch (error) {
    console.error('Failed to create test users:', error);
    return { success: false };
  }
}

/**
 * Remove all test users from database
 * Only works in development mode
 */
export async function removeTestUsers(): Promise<boolean> {
  if (process.env.NODE_ENV === 'production') {
    throw new Error('Test users cannot be removed in production mode');
  }

  try {
    await connectDB();
    
    for (const testUser of TEST_USERS) {
      const user = await DataService.getUserByEmail(testUser.email);
      if (user) {
        await DataService.deleteUser(user._id.toString());
        console.log(`🗑️ Removed test user: ${testUser.email}`);
      }
    }

    return true;
  } catch (error) {
    console.error('Failed to remove test users:', error);
    return false;
  }
}

/**
 * Get test user credentials for development
 * Only works in development mode
 */
export function getTestUserInfo(): Array<{ email: string; role: string; company: string }> {
  if (process.env.NODE_ENV === 'production') {
    return [];
  }

  return TEST_USERS.map(user => ({
    email: user.email,
    role: user.role,
    company: user.companyName
  }));
}

/**
 * Check if email is a test user
 */
export function isTestUser(email: string): boolean {
  if (process.env.NODE_ENV === 'production') {
    return false;
  }
  
  return TEST_USERS.some(user => user.email.toLowerCase() === email.toLowerCase());
}

/**
 * Validate test user credentials (legacy support)
 * This is a secure replacement for the old hardcoded validation
 */
export async function validateTestUserCredentials(email: string, password: string): Promise<{ user: TestUser; isValid: boolean } | null> {
  if (process.env.NODE_ENV === 'production') {
    return null;
  }

  if (!isTestUser(email)) {
    return null;
  }

  // Use database authentication instead of hardcoded passwords
  const authResult = await DataService.authenticateUser(email, password);
  if (authResult.user) {
    // Find the corresponding test user data
    const testUser = TEST_USERS.find(user => user.email.toLowerCase() === email.toLowerCase());
    if (testUser) {
      return { user: testUser, isValid: true };
    }
  }
  return null;
}

/**
 * Development utility to reset test user passwords
 */
export async function resetTestUserPasswords(): Promise<{ success: boolean; users?: Array<{ email: string; password: string }> }> {
  if (process.env.NODE_ENV === 'production') {
    throw new Error('Cannot reset passwords in production mode');
  }

  try {
    await connectDB();
    const updatedUsers: Array<{ email: string; password: string }> = [];

    for (const testUser of TEST_USERS) {
      const user = await DataService.getUserByEmail(testUser.email);
      if (user) {
        const newPassword = generateSecurePassword();
        const hashedPassword = await bcrypt.hash(newPassword, 12);
        
        await DataService.updateUser(user._id.toString(), {
          hashedPassword,
          passwordNeedsMigration: false,
          passwordHashAlgorithm: 'bcrypt'
        });

        updatedUsers.push({
          email: testUser.email,
          password: newPassword
        });

        console.log(`🔄 Reset password for test user: ${testUser.email}`);
      }
    }

    return { success: true, users: updatedUsers };
  } catch (error) {
    console.error('Failed to reset test user passwords:', error);
    return { success: false };
  }
}
