import { NextResponse } from 'next/server';
import { withCompanyIsolation, AuthenticatedRequest } from '@/lib/middleware/auth';
import connectDB from '@/lib/db/mongodb';
import User from '@/lib/db/models/User';

export const runtime = 'nodejs';

// GET admin profile
export const GET = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    if (!request.user) {
      return NextResponse.json(
        { success: false, error: 'User not authenticated' },
        { status: 401 }
      );
    }
    
    // Check if user is admin
    if (request.user.role !== 'admin' && request.user.role !== 'investigator') {
      return NextResponse.json(
        { success: false, error: 'Access denied. Admin privileges required.' },
        { status: 403 }
      );
    }
    
    const user = await User.findById(request.user.id)
      .select('-hashedPassword -passwordResetToken -emailVerificationToken -unlockToken -twoFactor.secret -twoFactor.backupCodes')
      .populate('companyId', 'name');
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }
    
    // Transform user data for admin frontend
    const profileData = {
      firstName: user.firstName || '',
      lastName: user.lastName || '',
      email: user.email,
      phone: user.phoneNumber || '',
      department: '', // This would come from a separate department field
      jobTitle: '', // This would come from a separate jobTitle field
      employeeId: '', // This would come from a separate employeeId field
      adminLevel: user.role === 'admin' ? 'Super Admin' : 'Standard',
      permissions: getPermissionsForRole(user.role),
      preferredLanguage: user.preferences?.language || 'English',
      timezone: 'UTC', // This would come from user preferences
      notificationPreferences: {
        emailNotifications: user.preferences?.notifications?.email ?? true,
        smsNotifications: user.preferences?.notifications?.sms ?? false,
        reportUpdates: true,
        systemAlerts: true,
        urgentReports: true,
        weeklyDigest: true,
      },
      systemSettings: {
        autoAssignReports: false,
        enableRealTimeNotifications: true,
        showAdvancedAnalytics: true,
        allowBulkActions: user.role === 'admin',
      },
      securitySettings: {
        twoFactorAuth: user.twoFactor?.enabled ?? false,
        sessionTimeout: 30,
        ipRestriction: false,
        auditLogging: true,
      },
    };
    
    return NextResponse.json({
      success: true,
      data: profileData
    });
  } catch (error) {
    console.error('Get admin profile API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});

// PUT update admin profile
export const PUT = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    if (!request.user) {
      return NextResponse.json(
        { success: false, error: 'User not authenticated' },
        { status: 401 }
      );
    }
    
    // Check if user is admin
    if (request.user.role !== 'admin' && request.user.role !== 'investigator') {
      return NextResponse.json(
        { success: false, error: 'Access denied. Admin privileges required.' },
        { status: 403 }
      );
    }
    
    const profileData = await request.json();
    
    // Validate required fields
    if (!profileData.firstName || !profileData.lastName || !profileData.email) {
      return NextResponse.json(
        { success: false, error: 'First name, last name, and email are required' },
        { status: 400 }
      );
    }
    
    // Check if email is already taken by another user
    if (profileData.email !== request.user.email) {
      const existingUser = await User.findOne({ 
        email: profileData.email, 
        _id: { $ne: request.user.id } 
      });
      
      if (existingUser) {
        return NextResponse.json(
          { success: false, error: 'Email is already in use' },
          { status: 400 }
        );
      }
    }
    
    // Update admin profile
    const updateData = {
      firstName: profileData.firstName,
      lastName: profileData.lastName,
      email: profileData.email,
      phoneNumber: profileData.phone,
      'preferences.language': profileData.preferredLanguage || 'English',
      'preferences.notifications.email': profileData.notificationPreferences?.emailNotifications ?? true,
      'preferences.notifications.sms': profileData.notificationPreferences?.smsNotifications ?? false,
      'twoFactor.enabled': profileData.securitySettings?.twoFactorAuth ?? false,
    };
    
    const updatedUser = await User.findByIdAndUpdate(
      request.user.id,
      { $set: updateData },
      { new: true, runValidators: true }
    ).select('-hashedPassword -passwordResetToken -emailVerificationToken -unlockToken -twoFactor.secret -twoFactor.backupCodes');
    
    if (!updatedUser) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: updatedUser,
      message: 'Admin profile updated successfully'
    });
  } catch (error) {
    console.error('Update admin profile API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});

// Helper function to get permissions based on role
function getPermissionsForRole(role: string): string[] {
  const basePermissions = [
    "View Reports",
    "Edit Reports",
    "Assign Reports",
    "Analytics Access"
  ];
  
  const adminPermissions = [
    ...basePermissions,
    "Delete Reports",
    "Manage Users",
    "System Configuration",
    "Audit Logs",
    "Export Data",
    "Bulk Operations"
  ];
  
  return role === 'admin' ? adminPermissions : basePermissions;
}
