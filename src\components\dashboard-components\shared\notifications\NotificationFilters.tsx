"use client";

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Search } from "lucide-react";

interface NotificationFiltersProps {
    searchTerm: string;
    statusFilter: string;
    typeFilter: string;
    uniqueTypes: string[];
    onSearchChange: (value: string) => void;
    onStatusChange: (value: string) => void;
    onTypeChange: (value: string) => void;
    onClearFilters: () => void;
    hasActiveFilters: boolean;
}

export default function NotificationFilters({
    searchTerm,
    statusFilter,
    typeFilter,
    uniqueTypes,
    onSearchChange,
    onStatusChange,
    onTypeChange,
    onClearFilters,
    hasActiveFilters
}: NotificationFiltersProps) {
    return (
        <Card>
            <CardHeader className="pb-3 sm:pb-6">
                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 sm:gap-0">
                    <CardTitle className="text-base sm:text-lg">Filter Notifications</CardTitle>
                    {hasActiveFilters && (
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={onClearFilters}
                            className="text-xs sm:text-sm w-full sm:w-auto"
                        >
                            Clear Filters
                        </Button>
                    )}
                </div>
            </CardHeader>
            <CardContent className="pt-0">
                <div className="flex flex-col gap-3 sm:gap-4" role="group" aria-labelledby="filters-section">
                    <div className="w-full">
                        <label htmlFor="search-notifications" className="sr-only">
                            Search notifications by title or message
                        </label>
                        <div className="relative">
                            <Search
                                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"
                                aria-hidden="true"
                            />
                            <Input
                                id="search-notifications"
                                name="searchNotifications"
                                placeholder="Search notifications..."
                                className="pl-10 text-sm sm:text-base h-10 sm:h-11"
                                value={searchTerm}
                                onChange={(e) => onSearchChange(e.target.value)}
                                aria-describedby="search-help"
                            />
                        </div>
                        <div id="search-help" className="sr-only">
                            Search through notification titles and messages
                        </div>
                    </div>

                    <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
                        <div>
                            <label htmlFor="status-filter" className="sr-only">Filter by notification status</label>
                            <Select
                                name="statusFilter"
                                value={statusFilter}
                                onValueChange={onStatusChange}
                                aria-label="Filter notifications by read status"
                            >
                                <SelectTrigger
                                    id="status-filter"
                                    className="w-full sm:w-40 md:w-48 h-10 sm:h-11 text-sm sm:text-base"
                                >
                                    <SelectValue placeholder="All Status" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Status</SelectItem>
                                    <SelectItem value="unread">Unread</SelectItem>
                                    <SelectItem value="read">Read</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div>
                            <label htmlFor="type-filter" className="sr-only">Filter by notification type</label>
                            <Select
                                name="typeFilter"
                                value={typeFilter}
                                onValueChange={onTypeChange}
                                aria-label="Filter notifications by type"
                            >
                                <SelectTrigger
                                    id="type-filter"
                                    className="w-full sm:w-40 md:w-48 h-10 sm:h-11 text-sm sm:text-base"
                                >
                                    <SelectValue placeholder="All Types" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Types</SelectItem>
                                    {uniqueTypes.map(type => (
                                        <SelectItem key={type} value={type}>
                                            {type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}