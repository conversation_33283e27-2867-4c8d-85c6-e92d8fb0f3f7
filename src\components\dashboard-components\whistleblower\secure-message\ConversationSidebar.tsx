"use client";

import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ConversationData } from "@/lib/types";
import ConversationItem from "./ConversationItem";

interface ConversationSidebarProps {
  conversations: ConversationData[];
  searchQuery: string;
  onSearchChange: (query: string) => void;
  activeConversationId: string;
  onConversationSelect: (id: string) => void;
}

export default function ConversationSidebar({
  conversations,
  searchQuery,
  onSearchChange,
  activeConversationId,
  onConversationSelect
}: ConversationSidebarProps) {
  const filteredConversations = conversations.filter(conv =>
    conv.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    conv.caseId.toLowerCase().includes(searchQuery.toLowerCase()) ||
    conv.lastMessage.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="w-full lg:w-1/3 xl:w-1/4 border-r border-gray-200 flex flex-col h-full">
      <div className="p-4 border-b border-gray-100">
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#6B7271] w-4 h-4" />
          <Input
            name="searchMessages"
            aria-label="Search conversations"
            placeholder="Search conversations..."
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10 py-3 border-2 border-[#D1D5DB] text-[#6B7271] text-sm font-medium focus:border-[#1E4841] focus:ring-[#1E4841]"
          />
        </div>

        <div className="flex items-center justify-between">
          <h2 className="text-sm font-medium text-[#111827]">Conversations</h2>
          <Badge variant="secondary" className="bg-[#ECF4E9] text-[#1E4841] text-xs">
            {filteredConversations.filter(c => c.isUnread).length} unread
          </Badge>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto">
        {filteredConversations.length === 0 ? (
          <div className="p-4 text-center">
            <p className="text-sm text-[#6B7280]">No conversations found</p>
          </div>
        ) : filteredConversations.map(conversation => (
          <ConversationItem
            key={conversation.id}
            conversation={conversation}
            isActive={conversation.id === activeConversationId}
            onClick={() => onConversationSelect(conversation.id)}
          />
        ))}
      </div>
    </div>
  );
}