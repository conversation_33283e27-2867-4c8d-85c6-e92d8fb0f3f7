// Shared types for the whistleblower application

// API Response Types
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface ApiErrorResponse {
  success: false;
  error: string;
  message?: string;
}

export interface ApiSuccessResponse<T = unknown> {
  success: true;
  data: T;
  message?: string;
}

// Database-ready base interface
export interface OAuthProfile {
  email: string;
  name?: string;
  given_name?: string;
  family_name?: string;
  picture?: string;
  sub?: string;
}

export interface BaseDocument {
  _id?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

// User and Authentication types
export interface UserInput {
  email: string;
  password?: string;
  firstName?: string;
  lastName?: string;
  companyId?: string;
  role?: 'whistleblower' | 'investigator' | 'admin';
  isActive?: boolean;
  phoneNumber?: string;
}

export interface User extends BaseDocument {
  email: string;
  hashedPassword?: string;
  firstName?: string;
  lastName?: string;
  companyId?: string; // Reference to Company._id
  role: 'whistleblower' | 'investigator' | 'admin';
  isActive: boolean;
  lastLogin?: Date;
  lastActive?: Date;
  preferences?: UserPreferences;
  sessionToken?: string;
  passwordNeedsMigration?: boolean;
  passwordHashAlgorithm?: string;
  accountLocked?: boolean;
  accountLockedUntil?: Date;
  failedLoginAttempts?: number;
  passwordResetToken?: string;
  passwordResetExpires?: Date;
  oauthProvider?: string;
  oauthId?: string;
  securitySettings?: {
    lastPasswordChange?: Date;
  };
  twoFactor?: {
    enabled: boolean;
    method: string;
    secret?: string;
    backupCodes?: string[];
    verificationCode?: string;
    verificationCodeExpires?: Date;
    attempts?: number;
  };
}

export interface UserPreferences {
  language: string;
  notifications: {
    email: boolean;
    push: boolean;
    sms: boolean;
  };
  theme: 'light' | 'dark' | 'system';
}

export interface UserSession {
  userId: string;
  email: string;
  role: string;
  firstName?: string;
  lastName?: string;
  isActive: boolean;
}

export interface WorkflowStep {
  step: number;
  title: string;
  description: string;
  icon: string;
}

export interface PricingPlan {
  name: string;
  description: string;
  monthlyPrice: string;
  yearlyPrice: string;
  isHighlighted?: boolean;
  access: {
    whistleblower: string;
    investigator: string;
    admin: string;
  };
  features: string[];
  cta?: string;
}

// Report related types - Database-ready
export type ReportStatus = "New" | "Under Review" | "Awaiting Response" | "Resolved" | "Closed";
export type ReportPriority = "Low" | "Medium" | "High" | "Critical";
export type ReportCategory = "Financial" | "Workplace Safety" | "Ethics Violation" | "Data Privacy" | "Harassment" | "Discrimination" | "Other";

export interface Report extends BaseDocument {
  updatedBy?: string; // Optional - may not be set initially
  reportId: string; // Auto-generated unique ID like "WB-2025-0012"
  userId: string; // Reference to User._id
  companyId?: string; // Optional - Reference to Company._id
  title: string;
  description: string;
  category: ReportCategory;
  priority: ReportPriority;
  status: ReportStatus;
  isAnonymous: boolean;
  incidentDate?: Date;
  location?: string;
  evidence?: EvidenceFile[];
  assignedInvestigator?: string; // Reference to User._id
  progress: number; // 0-100
  estimatedCompletion?: Date;
  tags?: string[];
  metadata?: {
    ipAddress?: string;
    userAgent?: string;
    submissionMethod: 'web' | 'mobile' | 'api';
  };
}

export interface EvidenceFile extends BaseDocument {
  reportId: string; // Reference to Report._id
  fileName: string;
  originalName: string;
  fileSize: number;
  mimeType: string;
  fileUrl: string;
  uploadedAt: Date;
  uploadedBy: string; // Reference to User._id
  description?: string;
  isEncrypted: boolean;
}

export interface ReportStep {
  title: string;
  date: string;
  icon: string;
  status: "complete" | "current" | "upcoming";
}

export interface TrackingReportData {
  report_id: string;
  updateddate: string;
  steps: ReportStep[];
}



// Navigation and UI types
export interface NavItem {
  href: string;
  title: string;
  description?: string;
}

export interface ProductItem extends NavItem {
  cta: string;
}

export interface FooterLink {
  href: string;
  text: string;
}

export interface SocialLink {
  icon: string;
  name: string;
  href: string;
}

// Content types
export interface TestimonialItem {
  quote: string;
  author: string;
  role: string;
  avatar: string;
}

export interface CompanyLogo {
  src: string;
  alt: string;
  height: number;
  width: number;
}

export interface BenefitItem {
  icon: string;
  text?: string;
  text1?: string;
  text2?: string;
}

export interface FeatureItem {
  text: string;
}

export interface TestimonialCardProps {
  name: string;
  role: string;
  avatarSrc: string;
  content: string;
}

export interface ProvisionCardData {
  icon: string;
  title: string;
  description: string;
}

// Form and input types (legacy - use EnhancedInputFieldProps for new components)
export interface InputFieldProps {
  name?: string;
  image?: string;
  label?: string;
  type: string;
  placeholder: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

// Image props
export interface ImageProps {
  src: string;
  alt: string;
  height: number;
  width: number;
  className?: string;
}

// Font props
export interface FontProps {
  variable: string;
}

// Messaging types
export interface Message extends BaseDocument {
  conversationId: string;
  senderId: string;
  content: string;
  htmlContent?: string;
  messageType: 'text' | 'file' | 'system';
  attachments?: Array<{
    fileName: string;
    fileUrl: string;
    fileSize: number;
    mimeType: string;
  }>;
  readBy: Array<{
    userId: string;
    readAt: Date;
  }>;
  reactions?: Array<{
    userId: string;
    emoji: string;
    createdAt: Date;
  }>;
  replyTo?: string; // Reference to another message
  isDeleted?: boolean;
  deletedAt?: Date;
}

export interface Conversation extends BaseDocument {
  reportId: string;
  participants: string[]; // Array of user IDs
  lastMessage?: {
    content: string;
    senderId: string;
    createdAt: Date;
  };
  unreadCount?: number;
}

// Auth types
export interface AuthTabsProps {
  activeTab?: string;
  onTabChange?: (tab: string) => void;
}

// Enhanced AuthTabs Props (from react-components)
export interface EnhancedAuthTabsProps {
  activeTab: "login" | "anonymous";
  setActiveTab: (tab: "login" | "anonymous") => void;
  className?: string;
}



export interface OtpInputProps {
  value?: string;
  onChange?: (value: string) => void;
  disabled?: boolean;
}

export interface TwoFactorAuthProps {
  onClose?: () => void;
}

export interface TimerProps {
  onExpire?: () => void;
  initialTime?: number;
}

// OTP Expiry Timer Props (from Time.tsx)
export interface OtpExpiryTimerProps {
  onExpire?: () => void;
  initialTime?: number; // in seconds
}

// Login Form Props
export interface LoginFormProps {
  onLogin?: () => void;
}



// Button props
export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'outline' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg';
  className?: string;
  children: React.ReactNode;
}

// Notification types - Database-ready
export type NotificationType = 'system' | 'report_update' | 'message' | 'alert';
export type NotificationStatus = 'read' | 'unread';
export type NotificationPriority = 'low' | 'medium' | 'high' | 'urgent';

export interface Notification extends BaseDocument {
  userId: string; // Reference to User._id
  type: NotificationType;
  title: string;
  message: string;
  status: NotificationStatus;
  priority: NotificationPriority;
  actionUrl?: string;
  reportId?: string; // Reference to Report._id
  readAt?: Date;
  expiresAt?: Date;
  metadata?: {
    source: string;
    category: string;
  };
}

// Message/Conversation types - Database-ready
export interface Conversation extends BaseDocument {
  reportId: string; // Reference to Report._id
  participants: string[]; // Array of User._id references
  status: 'active' | 'closed' | 'archived';
  lastMessageAt?: Date;
  isEncrypted: boolean;
}

export interface Message extends BaseDocument {
  conversationId: string; // Reference to Conversation._id
  senderId: string; // Reference to User._id
  content: string;
  htmlContent?: string; // For rich text formatting
  messageType: 'text' | 'file' | 'system';
  isEncrypted: boolean;
  readBy: Array<{
    userId: string;
    readAt: Date;
  }>;
  attachments?: Array<{
    fileName: string;
    fileUrl: string;
    fileSize: number;
    mimeType: string;
  }>;
  editedAt?: Date;
  isDeleted?: boolean;
  replyTo?: string; // Reference to Message._id for replies
  reactions?: Array<{
    userId: string;
    emoji: string;
    createdAt: Date;
  }>;
}

// Language types
export interface Language {
  code: string;
  name: string;
  nativeName: string;
  flag: string;
}

// Header and UI component types
export interface ListItemProps {
  href: string;
  title: string;
  children?: React.ReactNode;
  onClick?: () => void;
}

// Dashboard types
export interface DashboardStat {
  title: string;
  value: string;
  subtitle: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  subcolor: string;
  indicator: {
    type: "increase" | "decrease" | "warning" | "neutral";
    text: string;
    color: string;
  };
}



export interface FormStepProps {
  currentStep: number;
  totalSteps: number;
  onNext: () => void;
  onPrevious: () => void;
  onSubmit?: () => void;
}

export interface CircleStepProps {
  index: number;
  title: string;
  isActive?: boolean;
  isCompleted?: boolean;
}

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  icon?: React.ReactNode;
}

export interface TextAreaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  error?: string;
}

export interface SelectProps {
  label?: string;
  options: Array<{ value: string; label: string }>;
  value?: string;
  onChange?: (value: string) => void;
  error?: string;
}

// Tracking System Types
export interface TrackingData {
  reportId: string;
  status: string;
  lastUpdated: string;
  progress: number;
  timeline: TimelineEvent[];
}

export interface TimelineEvent {
  id: string;
  title: string;
  description: string;
  date: string;
  status: 'completed' | 'current' | 'pending';
  icon?: string;
}

// Evidence Upload Types - using database-ready version defined above

export interface FileUploadProps {
  onFileUpload: (files: File[]) => void;
  acceptedTypes?: string[];
  maxSize?: number;
  multiple?: boolean;
}

// Component Props Types
export interface CardProps {
  image: string;
  text: string;
  ariaLabel?: string;
  onClick?: () => void;
}

// Tracking System Card Props (different from general CardProps)
export interface TrackingCardProps {
  image: string;
  text: string;
  ariaLabel?: string;
}

export interface StatusComponentProps {
  status: string;
  color?: string;
  icon?: string;
}

// Tracking Status Component Props (different from general StatusComponentProps)
export interface TrackingStatusComponentProps {
  text1: string;
  text2: string;
}

export interface MessageBubbleProps {
  message: string;
  timestamp: string;
  sender: 'user' | 'investigator';
  isRead?: boolean;
}

// Tracking System Message Props (different from MessageBubbleProps)
export interface TrackingMessageProps {
  message: string;
  timestamp: string;
  sender: string;
  isUser: boolean;
}

export interface ProgressStepProps {
  step: ReportStep;
  isLast?: boolean;
}

// Dashboard Statistics - Database-ready
export interface DashboardStats {
  userId: string; // Reference to User._id
  totalReports: number;
  newReports: number;
  underReviewReports: number;
  awaitingResponseReports: number;
  resolvedReports: number;
  lastCalculated: Date;
  periodComparison: {
    totalReportsChange: number;
    newReportsChange: number;
    resolvedReportsChange: number;
    period: 'month' | 'quarter' | 'year';
  };
}

// DashboardStat interface already defined above - using that one

// Legacy interface for backward compatibility - will be replaced
export interface ReportData {
  id: string;
  title: string;
  status: string;
  statusColor: string;
  dateSubmitted: string;
  lastUpdated: string;
  priority: string;
  category: string;
  progress: string;
  progressPercentage?: number;
}

export interface RecentMessage {
  id: string;
  conversationId?: string;
  investigator: string;
  message: string;
  time: string;
  lastMessageAt?: string;
  avatar: string;
  isUnread: boolean;
}

export interface ActivityItem {
  id: string;
  type: 'message' | 'status_update' | 'resolution';
  title: string;
  description?: string;
  time: string;
  reportId: string;
  icon: 'message' | 'status' | 'check';
  iconColor?: string; // Optional - will be overridden by dynamic color calculation
  metadata?: {
    previousStatus?: string;
    currentStatus?: string;
    finalStatus?: string;
  };
  actionButton?: {
    text: string;
    action: string;
  };
}

export interface ChartDataPoint {
  month: string;
  reports: number;
}

export interface StatusDataPoint {
  name: string;
  value: number;
  fill: string;
}





export interface ReportFilters {
  status?: ReportStatus[];
  priority?: ReportPriority[];
  category?: ReportCategory[];
  dateFrom?: Date;
  dateTo?: Date;
  assignedInvestigator?: string;
  searchTerm?: string;
  limit?: number;
  offset?: number;
  sortBy?: 'createdAt' | 'updatedAt' | 'priority' | 'status';
  sortOrder?: 'asc' | 'desc';
}



export interface NotificationFilters {
  status?: NotificationStatus[];
  type?: NotificationType[];
  priority?: string[];
  dateFrom?: Date;
  dateTo?: Date;
  limit?: number;
  offset?: number;
}





// Product-specific interfaces
export interface ProductFeature {
  icon: React.ElementType | string;
  alt?: string;
  width?: number;
  height?: number;
  title: string;
  description: string;
}

export interface ProductStep {
  icon: React.ElementType | string | number;
  alt?: string;
  width?: number;
  height?: number;
  title: string;
  description: string | {
    p1: string;
    p2: string;
    p3: string;
  };
  image?: string;
  features?: {
    p1: string;
    p2: string;
    p3: string;
  };
}

export interface ProductSecurity {
  icon: React.ElementType | string;
  alt?: string;
  width?: number;
  height?: number;
  title: string;
  description: string;
}

export interface ProductTestimonial {
  quote: string;
  role: string;
  name?: string;
  company?: string;
  avatar: string;
  width?: number;
  height?: number;
}

export interface ProductResponsibility {
  icon: string;
  alt: string;
  width: number;
  height: number;
  title: string;
  description: string;
}

export interface ProductUser {
  icon: React.ElementType;
  title: string;
  description: string;
}

export interface ProductCase {
  icon: React.ElementType;
  title: string;
  description: string;
}

export interface ProductInsight {
  icon: React.ElementType;
  title: string;
  description: string;
}

export interface ProductSpace {
  icon: React.ElementType;
  title: string;
  description: string;
}

export interface ProductReport {
  icon: React.ElementType;
  title: string;
  description: string;
}

// Component-specific interfaces
export interface StepsProps {
  ind: number;
}

// Consent and Privacy Props
export interface ConsentProps {
  onAnonymousChange?: (isAnonymous: boolean) => void;
}

export interface ConsentPageProps {
  setIndex: React.Dispatch<React.SetStateAction<number>>;
}

// Report Card Props (from confirmation)
export interface ReportCardProps {
  report: string;
}

// Report Issue Props
export interface IssueProps {
  setIndex: React.Dispatch<React.SetStateAction<number>>;
  formData: ReportIssueFormData;
  setData: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
}

// Line Props (for progress indicators)
export interface LineProps {
  index: number;
  num: number;
}

// Form Data interfaces (unified for report issue and evidence)
export interface ReportIssueFormData {
  Title: string;
  Description: string;
  IncidentDate: string;
  Category: string;
  EvidenceFile: File | null;
  Evidence: string;
  [key: string]: string | File | null;
}

export interface EvidencePageProps {
  setIndex: React.Dispatch<React.SetStateAction<number>>;
  formData: ReportIssueFormData;
  setData: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  setFormData: React.Dispatch<React.SetStateAction<ReportIssueFormData>>;
}

export interface EvidenceFileUploadProps {
  name?: string;
  setFormData: React.Dispatch<React.SetStateAction<ReportIssueFormData>>;
  acceptedTypes?: string[];
  maxSize?: number; // in MB
}

// Enhanced InputField Props (moved from components)
export interface EnhancedInputFieldProps {
  require?: boolean;
  name: string;
  image?: string;
  label: string;
  type?: string;
  placeholder?: string;
  className?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  disabled?: boolean;
  "aria-describedby"?: string;
  "aria-invalid"?: boolean;
}

// Report Issue Form Data interface is defined above (unified with Evidence form data)

// TextArea Form Data interface (moved from TextArea component)
export interface TextAreaFormData {
  Title: string;
  Description: string;
  IncidentDate: string;
  Category: string;
  EvidenceFile: File | null;
  Evidence: string;
  [key: string]: string | File | null;
}

// TextArea Props interface (moved from TextArea component)
export interface TextAreaComponentProps {
  name: string;
  desc: string;
  text: string;
  image?: string;
  formData: TextAreaFormData;
  setData: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  required?: boolean;
  "aria-describedby"?: string;
}

// Report Issue Input Props (moved from new-report components)
export interface ReportIssueInputProps {
  name: string;
  desc: string;
  text: string;
  type: string;
  formData: ReportIssueFormData;
  setData: (e: React.ChangeEvent<HTMLInputElement>) => void;
  required?: boolean;
  "aria-describedby"?: string;
}

export interface TrackingCircleProps {
  step?: ReportStep;
  index?: number;
}

export interface ReportIssueCircleProps extends Omit<CircleStepProps, 'isActive' | 'isCompleted'> {
  num: number;
}

// Table configuration interfaces
export interface TableColumn {
  key: string;
  label: string;
  width?: string;
  className?: string;
  sortable?: boolean;
  type?: 'text' | 'badge' | 'progress' | 'date' | 'actions' | 'checkbox';
  align?: 'left' | 'center' | 'right';
}

export interface TableConfig {
  columns: TableColumn[];
  sortable?: boolean;
  selectable?: boolean;
  actions?: boolean;
}

// Tracking System Interfaces
export interface TrackingHeaderProps {
  lastUpdated?: string;
  isAnonymous?: boolean;
  userName?: string;
}

export interface TrackingReportProps {
  report: TrackingReportData;
}

// Secure Messaging Types
export interface ConversationData {
  id: string;
  name: string;
  caseId: string;
  time: string;
  lastMessage: string;
  isUnread: boolean;
  isActive: boolean;
  isOnline: boolean;
  isTyping: boolean;
  avatarBg: string;
  participantType: "team" | "committee" | "investigator" | "officer" | "admin";
}

export interface MessageData {
  id: string;
  conversationId: string;
  senderId: string;
  senderName: string;
  content: string;
  timestamp: string;
  isFromUser: boolean;
  isEncrypted: boolean;
  isRead: boolean;
  messageType: "text" | "file" | "system";
  attachments?: {
    fileName: string;
    fileSize: string;
    fileType: string;
  }[];
}

// Extended conversation data with report details for UI display
export interface ConversationWithDetails {
  id: string;
  reportId: string;
  reportTitle: string;
  lastMessage: string;
  lastMessageTime: string;
  unreadCount: number;
  status: "Active" | "Closed";
  investigator: {
    name: string;
    role: string;
    avatar: string;
  };
}

// Type for notifications that might have legacy id field
export type NotificationWithLegacyId = Notification & { id?: string };

// UI Component Interfaces
export interface HydrationSafeProps {
  children: React.ReactNode;
  className?: string;
}

// Unified Input Props (comprehensive input component)
export interface UnifiedInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  // Label and identification
  name: string;
  label?: string;

  // Icon support (from InputField)
  icon?: string;

  // Tooltip support (from new-report Input)
  tooltip?: string;
  helpText?: string;

  // Styling options
  variant?: "default" | "enhanced" | "report";

  // Validation
  required?: boolean;
  error?: string;

  // Accessibility
  "aria-describedby"?: string;
  "aria-invalid"?: boolean;
}

// Add Company interface
export interface Company extends BaseDocument {
  name: string;
  domain?: string;
  logo?: string;
  settings: {
    allowAnonymousReports: boolean;
    customReportCategories?: string[];
    adminUsers: string[]; // References to User._id
  };
}

// Blog data types
export interface Author {
  image: string;
  name: string;
  initials: string;
}

export interface BlogCard {
  id: string;
  image: string;
  category: string;
  date: string;
  title: string;
  description: string;
  author: Author;
  readTime: string;
  slug: string;
  featured?: boolean;
  content?: string;
  tags?: string[];
}

// Contact data types
export interface Office {
  title: string;
  address: string;
  phone: string;
  email: string;
  hours: string;
  mapImage: string;
}

export interface Department {
  icon: string;
  title: string;
  description: string;
  email: string;
  phone: string;
  hours: string;
  cta: string;
}

export interface FAQ {
  question: string;
  answer: string;
}

// Content data types
export interface TestimonialItem {
  quote: string;
  author: string;
  role: string;
  avatar: string;
}

export interface HeroTestimonial {
  name: string;
  role: string;
  avatar: string;
  quote: string;
}

export interface HelpCategory {
  title: string;
  icon: string;
  href: string;
}

export interface QuickLink {
  title: string;
  icon: string;
  href: string;
}

export interface PopularHelpTopic {
  title: string;
  description: string;
  icon: string;
}