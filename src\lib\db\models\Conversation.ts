import { Schema } from 'mongoose';
import { createModel } from '../utils';
import { IConversation } from './interfaces';

const ConversationSchema = new Schema({
  reportId: { type: Schema.Types.ObjectId, ref: 'Report', required: true },
  participants: [{ type: Schema.Types.ObjectId, ref: 'User' }],
  status: { 
    type: String, 
    enum: ['active', 'closed', 'archived'],
    default: 'active'
  },
  lastMessageAt: { type: Date },
  isEncrypted: { type: Boolean, default: true },
  readBy: [{
    userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    readAt: { type: Date, default: Date.now },
    lastMessageId: { type: Schema.Types.ObjectId, ref: 'Message' }
  }]
}, {
  timestamps: true
});

// Use the utility function to create the model safely
const Conversation = createModel<IConversation>('Conversation', ConversationSchema);

export default Conversation;