"use client";

import { useState, useEffect, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { sessionManager, SessionData } from '@/lib/utils/sessionManager';

interface User {
  id: string;
  email: string;
  role: 'admin' | 'investigator' | 'whistleblower';
  name: string;
  companyId?: string;
  firstName?: string;
  lastName?: string;
  lastLogin?: Date;
  currentLoginTime?: Date;
  previousLoginTime?: Date;
}

export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [sessionData, setSessionData] = useState<SessionData | null>(null);
  const [isClient, setIsClient] = useState(false);
  const router = useRouter();

  // Set client-side flag
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Check if user is logged in on mount and listen for session changes
  useEffect(() => {
    if (!isClient) {
      return; // Don't run on server side
    }

    const checkAuth = () => {
      console.log('useAuth: Checking authentication');
      const session = sessionManager.getCurrentSession();
      const token = localStorage.getItem('auth_token');

      console.log('useAuth: Session and token check', {
        hasSession: !!session,
        hasToken: !!token,
        isSessionValid: session ? sessionManager.isSessionValid() : false
      });

      if (session && sessionManager.isSessionValid() && token) {
        const previousLogin = sessionManager.getPreviousLoginTime(session.userId);

        const userData: User = {
          id: session.userId,
          email: session.email,
          role: session.role,
          name: session.name,
          companyId: session.companyId,
          firstName: session.firstName,
          lastName: session.lastName,
          lastLogin: previousLogin,
          currentLoginTime: session.loginTime,
          previousLoginTime: previousLogin
        };

        console.log('useAuth: Setting user data', userData);
        setUser(userData);
        setSessionData(session);
      } else {
        console.log('useAuth: Invalid session or token, clearing');
        // Clear invalid session and token
        sessionManager.clearSession();
        localStorage.removeItem('auth_token');
        setUser(null);
        setSessionData(null);
      }
      setIsLoading(false);
    };

    // Initial check
    checkAuth();

    // Listen for storage changes (session cleared in another tab)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'user_session' || e.key === 'auth_token') {
        console.log('useAuth: Storage change detected, rechecking auth');
        checkAuth();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [isClient]);

  const login = async (email: string, password: string, loginType?: 'admin' | 'whistleblower', rememberMe?: boolean): Promise<boolean> => {
    try {
      console.log('useAuth.login: Starting login process', { loginType });

      // Use the general login API endpoint - it handles all user types
      const apiEndpoint = '/api/auth/login';
      console.log('useAuth.login: Using API endpoint:', apiEndpoint);

      // Try the authentication API
      const response = await fetch(apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password, remember: rememberMe }),
      });

      const result = await response.json();
      console.log('useAuth.login: API response:', { success: result.success, role: result.user?.role, error: result.error });

      if (response.ok && result.success) {
        // Store JWT token in localStorage and cookie
        if (result.token && typeof window !== 'undefined') {
          localStorage.setItem('auth_token', result.token);
          // Set cookie for middleware
          document.cookie = `auth_token=${result.token}; path=/; max-age=${rememberMe ? 30 * 24 * 60 * 60 : 24 * 60 * 60}; secure; samesite=strict`;
        }

        // Create session using session manager
        const session = sessionManager.createSession({
          id: result.user.id,
          email: result.user.email,
          role: result.user.role,
          name: result.user.fullName || `${result.user.firstName} ${result.user.lastName}`.trim(),
          companyId: result.user.companyId,
          firstName: result.user.firstName,
          lastName: result.user.lastName
        }, rememberMe);

        // Get previous login time
        const previousLogin = sessionManager.getPreviousLoginTime(session.userId);
        
        // Create user object
        const user: User = {
          id: session.userId,
          email: session.email,
          role: session.role,
          name: session.name,
          companyId: result.user.companyId,
          firstName: result.user.firstName,
          lastName: result.user.lastName,
          lastLogin: previousLogin,
          currentLoginTime: session.loginTime,
          previousLoginTime: previousLogin
        };

        console.log('useAuth.login: Setting user and session data', { userId: user.id, role: user.role });
        setUser(user);
        setSessionData(session);

        // Don't redirect here - let the login page handle the redirect
        // This prevents competing redirects that can cause navigation issues

        console.log('useAuth.login: Login successful, returning true');
        return true;
      } else {
        // If API returns an error, throw it to be caught below
        throw new Error(result.error || 'Authentication failed');
      }
    } catch (error) {
      console.error('Login error:', error);
      throw error; // Re-throw to let the calling component handle the error
    }
  };

  const logout = () => {
    // Clear JWT token and cookies
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_token');
      // Clear auth cookie
      document.cookie = 'auth_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; secure; samesite=strict';
    }

    sessionManager.clearSession();
    setUser(null);
    setSessionData(null);
    router.push('/');
  };

  const isAdmin = () => {
    return user?.role === 'admin';
  };

  const isWhistleblower = () => {
    return user?.role === 'whistleblower';
  };

  const getLoginTimeFormatted = () => {
    if (!user?.currentLoginTime) return 'No login time available';
    return sessionManager.getFormattedLoginTime(user.currentLoginTime);
  };

  const getSessionDuration = () => {
    return sessionManager.getSessionDuration();
  };

  const getPreviousLoginFormatted = () => {
    if (!user?.previousLoginTime) return 'First time login';
    return sessionManager.getFormattedLoginTime(user.previousLoginTime);
  };

  // Memoize the user object to prevent unnecessary re-renders
  const memoizedUser = useMemo(() => user, [user]);

  // Memoize isAuthenticated to prevent unnecessary re-renders
  const isAuthenticated = useMemo(() => !!user, [user]);

  return {
    user: memoizedUser,
    sessionData,
    isLoading,
    login,
    logout,
    isAdmin,
    isWhistleblower,
    isAuthenticated,
    getLoginTimeFormatted,
    getSessionDuration,
    getPreviousLoginFormatted
  };
}