"use client";

import React, { useState, useEffect, useRef } from 'react';
import { Check, ChevronDown, Building2, Plus } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';

interface Company {
  _id: string;
  name: string;
  industry?: string;
  contactEmail?: string;
}

interface CompanySelectorProps {
  value?: string;
  onValueChange: (value: string) => void;
  onCompanySelect?: (company: Company | null) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  error?: string;
}

export function CompanySelector({
  value = '',
  onValueChange,
  onCompanySelect,
  placeholder = "Search or enter company name...",
  className,
  disabled = false,
  error
}: CompanySelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [companies, setCompanies] = useState<Company[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState(value);
  const [selectedCompany, setSelectedCompany] = useState<Company | null>(null);
  
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Search companies when query changes
  useEffect(() => {
    const searchCompanies = async () => {
      if (searchQuery.trim().length < 2) {
        setCompanies([]);
        return;
      }

      setLoading(true);
      try {
        const response = await fetch(`/api/companies/search?q=${encodeURIComponent(searchQuery.trim())}&limit=10`);
        const data = await response.json();
        
        if (data.success) {
          setCompanies(data.data || []);
        }
      } catch (error) {
        console.error('Error searching companies:', error);
        setCompanies([]);
      } finally {
        setLoading(false);
      }
    };

    const debounceTimer = setTimeout(searchCompanies, 300);
    return () => clearTimeout(debounceTimer);
  }, [searchQuery]);

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setSearchQuery(newValue);
    onValueChange(newValue);
    
    // Clear selected company if input doesn't match
    if (selectedCompany && selectedCompany.name !== newValue) {
      setSelectedCompany(null);
      onCompanySelect?.(null);
    }
    
    setIsOpen(true);
  };

  // Handle company selection
  const handleCompanySelect = (company: Company) => {
    setSelectedCompany(company);
    setSearchQuery(company.name);
    onValueChange(company.name);
    onCompanySelect?.(company);
    setIsOpen(false);
  };

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const showCreateOption = searchQuery.trim().length >= 2 && 
    !companies.some(c => c.name.toLowerCase() === searchQuery.trim().toLowerCase());

  return (
    <div className={cn("relative", className)}>
      <div className="relative">
        <Input
          ref={inputRef}
          type="text"
          value={searchQuery}
          onChange={handleInputChange}
          onFocus={() => setIsOpen(true)}
          placeholder={placeholder}
          disabled={disabled}
          className={cn(
            "pr-10",
            error && "border-red-500 focus:border-red-500"
          )}
        />
        <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
          <ChevronDown className="h-4 w-4 text-gray-400" />
        </div>
      </div>

      {error && (
        <p className="text-sm text-red-500 mt-1">{error}</p>
      )}

      {isOpen && (searchQuery.trim().length >= 2 || companies.length > 0) && (
        <div
          ref={dropdownRef}
          className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto"
        >
          {loading && (
            <div className="px-3 py-2 text-sm text-gray-500">
              Searching companies...
            </div>
          )}

          {!loading && companies.length === 0 && searchQuery.trim().length >= 2 && (
            <div className="px-3 py-2 text-sm text-gray-500">
              No companies found
            </div>
          )}

          {!loading && companies.map((company) => (
            <button
              key={company._id}
              onClick={() => handleCompanySelect(company)}
              className="w-full px-3 py-2 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none flex items-center justify-between"
            >
              <div className="flex items-center space-x-2">
                <Building2 className="h-4 w-4 text-gray-400" />
                <div>
                  <div className="text-sm font-medium text-gray-900">
                    {company.name}
                  </div>
                  {company.industry && (
                    <div className="text-xs text-gray-500">
                      {company.industry}
                    </div>
                  )}
                </div>
              </div>
              {selectedCompany?._id === company._id && (
                <Check className="h-4 w-4 text-green-600" />
              )}
            </button>
          ))}

          {!loading && showCreateOption && (
            <>
              <div className="border-t border-gray-100" />
              <button
                onClick={() => {
                  // Create new company option
                  const newCompany: Company = {
                    _id: 'new',
                    name: searchQuery.trim(),
                  };
                  handleCompanySelect(newCompany);
                }}
                className="w-full px-3 py-2 text-left hover:bg-blue-50 focus:bg-blue-50 focus:outline-none flex items-center space-x-2 text-blue-600"
              >
                <Plus className="h-4 w-4" />
                <span className="text-sm font-medium">
                  Create &quot;{searchQuery.trim()}&quot;
                </span>
              </button>
            </>
          )}
        </div>
      )}
    </div>
  );
}
