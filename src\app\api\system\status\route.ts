import { NextResponse } from 'next/server';
import { withCompanyIsolation, AuthenticatedRequest } from '@/lib/middleware/auth';
import { SystemStatus } from '@/lib/db/models';
import { ISystemStatus } from '@/lib/db/models/interfaces';
import connectDB from '@/lib/db/mongodb';

export const runtime = 'nodejs';

export const GET = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    if (!request.user) {
      return NextResponse.json(
        { success: false, error: 'User not authenticated' },
        { status: 401 }
      );
    }

    // Only admins can view system status
    if (request.user.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Access denied. Admin role required.' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const includeMetrics = searchParams.get('includeMetrics') === 'true';

    // Get all monitored services
    const services = await SystemStatus.find({ isMonitored: true })
      .sort({ serviceName: 1 });

    // Transform data for frontend
    const transformedServices = services.map(service => {
      const s = service as unknown as ISystemStatus;
      const baseData = {
        id: s._id.toString(),
        serviceName: s.serviceName,
        serviceType: s.serviceType,
        status: s.status,
        uptime: s.uptime,
        uptimePercentage: s.uptimePercentage,
        responseTime: s.responseTime,
        cpuUsage: s.cpuUsage,
        memoryUsage: s.memoryUsage,
        diskUsage: s.diskUsage,
        lastHealthCheck: s.lastHealthCheck?.toISOString() || new Date().toISOString(),
        version: s.version,
        environment: s.environment,
        errorCount: s.errorCount,
        lastError: s.lastError ? {
          message: s.lastError.message,
          timestamp: s.lastError.timestamp?.toISOString() || new Date().toISOString(),
          severity: s.lastError.severity
        } : null,
        // Virtual fields
        statusColor: (s as any).statusColor,
        statusIcon: (s as any).statusIcon
      };

      // Include metrics if requested
      if (includeMetrics) {
        return {
          ...baseData,
          metrics: s.metrics.slice(-24).map(metric => ({
            timestamp: metric.timestamp.toISOString(),
            status: metric.status,
            responseTime: metric.responseTime,
            cpuUsage: metric.cpuUsage,
            memoryUsage: metric.memoryUsage,
            diskUsage: metric.diskUsage
          }))
        };
      }

      return baseData;
    });

    // Calculate system overview
    const overview = {
      totalServices: services.length,
      runningServices: services.filter(s => (s as unknown as ISystemStatus).status === 'Running').length,
      errorServices: services.filter(s => ['Error', 'Stopped'].includes((s as unknown as ISystemStatus).status)).length,
      degradedServices: services.filter(s => (s as unknown as ISystemStatus).status === 'Degraded').length,
      maintenanceServices: services.filter(s => (s as unknown as ISystemStatus).status === 'Maintenance').length,
      averageUptime: services.length > 0
        ? services.reduce((sum, s) => sum + (s as unknown as ISystemStatus).uptimePercentage, 0) / services.length
        : 0,
      averageResponseTime: services.length > 0
        ? services.reduce((sum, s) => sum + ((s as unknown as ISystemStatus).responseTime || 0), 0) / services.length
        : 0,
      totalErrors: services.reduce((sum, s) => sum + (s as unknown as ISystemStatus).errorCount, 0),
      lastUpdated: new Date().toISOString()
    };

    // System health score (0-100)
    const healthScore = Math.round(
      (overview.runningServices / overview.totalServices) * 100 * 
      (overview.averageUptime / 100)
    );

    return NextResponse.json({
      success: true,
      data: {
        services: transformedServices,
        overview: {
          ...overview,
          healthScore,
          status: healthScore >= 95 ? 'Healthy' : 
                 healthScore >= 80 ? 'Degraded' : 'Critical'
        }
      }
    });
  } catch (error) {
    console.error('System status API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});

export const POST = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    if (!request.user) {
      return NextResponse.json(
        { success: false, error: 'User not authenticated' },
        { status: 401 }
      );
    }

    // Only admins can update system status
    if (request.user.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Access denied. Admin role required.' },
        { status: 403 }
      );
    }

    const { serviceName, status, metrics, error } = await request.json();
    
    if (!serviceName || !status) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: serviceName and status' },
        { status: 400 }
      );
    }

    // Find the service
    const service = await SystemStatus.findOne({ serviceName });
    
    if (!service) {
      return NextResponse.json(
        { success: false, error: 'Service not found' },
        { status: 404 }
      );
    }

    // Update service status
    (service as any).status = status;
    (service as any).lastHealthCheck = new Date();

    // Update metrics if provided
    if (metrics) {
      if (metrics.responseTime !== undefined) (service as any).responseTime = metrics.responseTime;
      if (metrics.cpuUsage !== undefined) (service as any).cpuUsage = metrics.cpuUsage;
      if (metrics.memoryUsage !== undefined) (service as any).memoryUsage = metrics.memoryUsage;
      if (metrics.diskUsage !== undefined) (service as any).diskUsage = metrics.diskUsage;

      // Add to metrics history
      (service as any).metrics.push({
        timestamp: new Date(),
        status,
        responseTime: metrics.responseTime,
        cpuUsage: metrics.cpuUsage,
        memoryUsage: metrics.memoryUsage,
        diskUsage: metrics.diskUsage
      });

      // Keep only last 288 entries (24 hours with 5-minute intervals)
      if ((service as any).metrics.length > 288) {
        (service as any).metrics = (service as any).metrics.slice(-288);
      }
    }

    // Update error information if provided
    if (error) {
      (service as any).lastError = {
        message: error.message,
        timestamp: new Date(),
        severity: error.severity || 'medium'
      };
      (service as any).errorCount += 1;
    } else if (status === 'Running' && (service as any).status !== 'Running') {
      // Clear error when service recovers
      (service as any).lastError = undefined;
    }

    // Recalculate uptime percentage (simplified calculation)
    const totalMetrics = (service as any).metrics.length;
    if (totalMetrics > 0) {
      const runningMetrics = (service as any).metrics.filter((m: any) => m.status === 'Running').length;
      (service as any).uptimePercentage = (runningMetrics / totalMetrics) * 100;
      (service as any).uptime = `${(service as any).uptimePercentage.toFixed(1)}%`;
    }

    await service.save();

    const updatedService = service as unknown as ISystemStatus;
    return NextResponse.json({
      success: true,
      data: {
        serviceName: updatedService.serviceName,
        status: updatedService.status,
        uptime: updatedService.uptime,
        lastHealthCheck: updatedService.lastHealthCheck.toISOString()
      },
      message: 'Service status updated successfully'
    });
  } catch (error) {
    console.error('Update system status API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});
