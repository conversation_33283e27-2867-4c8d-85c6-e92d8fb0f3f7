import mongoose, { Schema, Document } from 'mongoose';

export interface ISupportRequest extends Document {
  priorityLevel: 'low' | 'medium' | 'high' | 'urgent';
  issueCategory: 'technical' | 'account' | 'billing' | 'feature' | 'bug' | 'other';
  issueDescription: string;
  fullName: string;
  emailAddress: string;
  attachments?: {
    filename: string;
    originalName: string;
    mimeType: string;
    size: number;
    path: string;
  }[];
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  assignedTo?: Schema.Types.ObjectId;
  resolution?: string;
  resolvedAt?: Date;
  ticketNumber: string;
  companyId?: Schema.Types.ObjectId;
  metadata: {
    userAgent?: string;
    ipAddress?: string;
    source: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

const SupportRequestSchema = new Schema<ISupportRequest>({
  priorityLevel: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    required: true
  },
  issueCategory: {
    type: String,
    enum: ['technical', 'account', 'billing', 'feature', 'bug', 'other'],
    required: true
  },
  issueDescription: {
    type: String,
    required: true,
    minlength: 10,
    maxlength: 2000
  },
  fullName: {
    type: String,
    required: true,
    minlength: 2,
    maxlength: 100
  },
  emailAddress: {
    type: String,
    required: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  attachments: [{
    filename: { type: String, required: true },
    originalName: { type: String, required: true },
    mimeType: { type: String, required: true },
    size: { type: Number, required: true },
    path: { type: String, required: true }
  }],
  status: {
    type: String,
    enum: ['open', 'in_progress', 'resolved', 'closed'],
    default: 'open'
  },
  assignedTo: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  resolution: {
    type: String,
    maxlength: 1000
  },
  resolvedAt: {
    type: Date
  },
  ticketNumber: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  companyId: {
    type: Schema.Types.ObjectId,
    ref: 'Company'
  },
  metadata: {
    userAgent: String,
    ipAddress: String,
    source: {
      type: String,
      default: 'contact_form'
    }
  }
}, {
  timestamps: true
});

// Index for efficient queries
SupportRequestSchema.index({ emailAddress: 1, createdAt: -1 });
SupportRequestSchema.index({ status: 1, priorityLevel: 1 });
// ticketNumber index is already created by unique: true, index: true in field definition
SupportRequestSchema.index({ assignedTo: 1, status: 1 });

// Generate ticket number before saving
SupportRequestSchema.pre('save', async function(next) {
  if (this.isNew) {
    const count = await mongoose.model('SupportRequest').countDocuments();
    this.ticketNumber = `SUP-${new Date().getFullYear()}-${String(count + 1).padStart(6, '0')}`;
  }
  next();
});

// Virtual for formatted ticket display
SupportRequestSchema.virtual('formattedTicket').get(function() {
  return this.ticketNumber;
});

// Method to check if request is overdue
SupportRequestSchema.methods.isOverdue = function() {
  if (this.status === 'resolved' || this.status === 'closed') return false;
  
  const now = new Date();
  const createdAt = new Date(this.createdAt);
  const hoursDiff = (now.getTime() - createdAt.getTime()) / (1000 * 60 * 60);
  
  // SLA based on priority
  const slaHours = {
    urgent: 4,
    high: 24,
    medium: 48,
    low: 72
  };
  
  return hoursDiff > slaHours[this.priorityLevel];
};

// Static method to get statistics
SupportRequestSchema.statics.getStats = async function() {
  const stats = await this.aggregate([
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 }
      }
    }
  ]);
  
  const priorityStats = await this.aggregate([
    {
      $match: { status: { $in: ['open', 'in_progress'] } }
    },
    {
      $group: {
        _id: '$priorityLevel',
        count: { $sum: 1 }
      }
    }
  ]);
  
  return { statusStats: stats, priorityStats };
};

export default mongoose.models.SupportRequest || mongoose.model<ISupportRequest>('SupportRequest', SupportRequestSchema);
