"use client";

import { useRef } from "react";
import { Send, Timer, LogOut } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";

interface MobileMessageInputProps {
  newMessage: string;
  onMessageChange: (message: string) => void;
  onSendMessage: () => void;
  isLoading: boolean;
  showAutoLogout: boolean;
  autoLogoutTime: number;
  onLogout: () => void;
  onKeyDown: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void;
  resetAutoLogoutTimer: () => void;
}

export default function MobileMessageInput({
  newMessage,
  onMessageChange,
  onSendMessage,
  isLoading,
  showAutoLogout,
  autoLogoutTime,
  onLogout,
  onKeyDown,
  resetAutoLogoutTimer
}: MobileMessageInputProps) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const formatAutoLogoutTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="p-4 border-t bg-gray-50">
      {showAutoLogout && (
        <div className="flex items-center gap-2 text-xs text-[#FF2121] mb-3">
          <Timer className="w-4 h-4" />
          <span>Auto-logout in {formatAutoLogoutTime(autoLogoutTime)}</span>
          <Button
            variant="ghost"
            size="sm"
            onClick={onLogout}
            className="ml-auto p-1 text-[#FF2121] hover:bg-red-50"
            aria-label="Logout now"
          >
            <LogOut className="w-3 h-3" />
          </Button>
        </div>
      )}
      <div className="flex gap-2">
        <Textarea
          ref={textareaRef}
          name="message"
          aria-label="Type a message"
          placeholder="Type your message... (Ctrl+Enter to send)"
          value={newMessage}
          onChange={(e) => {
            onMessageChange(e.target.value);
            resetAutoLogoutTimer();
          }}
          className="flex-1 min-h-[60px] resize-none border-2 border-[#D1D5DB] focus:border-[#1E4841] focus:ring-[#1E4841]"
          onKeyDown={onKeyDown}
        />
        <Button
          onClick={onSendMessage}
          disabled={!newMessage.trim() || isLoading}
          className="px-4 py-2 bg-[#1E4841] text-white hover:bg-[#1E4841]/90 disabled:opacity-50 self-end"
          aria-label="Send message"
        >
          {isLoading ? (
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
          ) : (
            <Send className="w-4 h-4" />
          )}
        </Button>
      </div>
      <p className="text-xs text-[#6B7280] mt-2">
        Press Ctrl+Enter to send • All messages are encrypted
      </p>
    </div>
  );
}