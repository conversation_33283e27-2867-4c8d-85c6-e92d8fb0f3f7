"use client";

import React, { useState, useEffect, useMemo, Suspense } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useSearchParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Shield,
  FileText,
  AlertCircle,
  Download,
  Printer,
  Upload,
  Paperclip,
  CheckCircle,
  ArrowLeft,
  Clock,
  Building,
  MapPin,
  Calendar,
  Share2,
  MessageSquare
} from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { <PERSON>roll<PERSON><PERSON> } from '@/components/ui/scroll-area';

// Add print styles to head
if (typeof window !== 'undefined') {
  const printStyles = `
    <style>
      @media print {
        .no-print { display: none !important; }
        .print-icon::before {
          content: "●";
          display: inline-block;
          width: 20px;
          height: 20px;
          border-radius: 50%;
          background: #1E4841;
          color: white;
          text-align: center;
          line-height: 20px;
          margin-right: 8px;
        }
        .print-icon.completed::before {
          content: "✓";
          background: #22c55e;
        }
        .print-icon.pending::before {
          content: "○";
          background: #d1d5db;
          color: #6b7280;
        }
        .print-progress-bar {
          width: 100%;
          height: 8px;
          background: #e5e7eb;
          border-radius: 4px;
          margin: 20px 0;
        }
        .print-progress-fill {
          height: 100%;
          background: #1E4841;
          border-radius: 4px;
        }
      }
    </style>
  `;

  if (!document.head.querySelector('#print-styles')) {
    const styleElement = document.createElement('div');
    styleElement.id = 'print-styles';
    styleElement.innerHTML = printStyles;
    document.head.appendChild(styleElement);
  }
}

interface ReportData {
  id: string;
  submittedDate: string;
  lastUpdated: string;
  status: string;
  progress: Record<string, { completed: boolean; current: boolean }>;
  currentStatus: {
    title: string;
    description: string;
    priority?: string;
    assignedTo: string;
    nextUpdate?: string;
    note?: string;
    finalStatus?: string;
    closureDate?: string;
  };
  timeline: {
    title: string;
    items: Array<{
      title: string;
      description: string;
      timestamp: string;
      status: string;
      badge: string;
    }>;
  };
  submissionDetails: {
    status: string;
    description: string;
    submissionComplete: boolean;
    reportVerified: {
      completed: boolean;
      description: string;
    };
    supportingDocuments: {
      uploaded: boolean;
      files: string[];
    };
  };
  category: {
    type: string;
    description: string;
    department: string;
    location: string;
    incidentDate: string;
  };
  messages: Array<{
    id: number;
    sender: string;
    timestamp: string;
    message: string;
  }>;
  additionalInfo: {
    description: string;
    referenceNumber: string;
    note?: string;
    additionalNote?: string;
  };
}

// Helper functions for dynamic data
const getStatusDetails = (status: string) => {
  switch (status) {
    case 'under_review':
      return {
        title: 'Under Review',
        description: 'Your report has been received and is currently under preliminary review by our internal administration team. The team will verify the completeness of the information received, the nature of the allegation, and determine the appropriate course of action.',
        priority: 'Not Set',
        assignedTo: 'Admin Team',
        nextUpdate: 'Within 48 hours',
        note: 'Your report is in the admin review stage. Once complete, an investigator will be assigned to begin the formal investigation.'
      };
    case 'investigation':
      return {
        title: 'Under Investigation',
        description: 'Your report is currently being investigated by our team. We are gathering evidence and conducting interviews to verify the information provided.',
        priority: 'Medium',
        assignedTo: 'Investigator #I-428',
        nextUpdate: 'May 6, 2025',
        note: 'Investigation timeframe may vary based on complexity. You will be notified of significant developments.'
      };
    case 'action_taken':
      return {
        title: 'Action Taken',
        description: 'Based on the investigator\'s findings, appropriate measures have been implemented. The case is currently in the resolution phase, and follow-up monitoring is scheduled to ensure sustained compliance.',
        priority: 'Medium',
        assignedTo: 'Admin Team',
        nextUpdate: 'May 6, 2025',
        note: 'Corrective actions have been implemented. A follow-up review will be conducted to ensure their effectiveness.'
      };
    case 'closed':
      return {
        title: 'Case Closed',
        description: 'All corrective actions have been successfully implemented and verified. The case has been reviewed and closed with satisfactory resolution.',
        finalStatus: 'Resolved',
        assignedTo: 'Admin Team',
        closureDate: 'May 6, 2025',
        note: 'All identified issues have been addressed. Follow-up assessments confirmed that all corrective actions are effective. The case is now formally closed with no outstanding concerns.'
      };
    default:
      return {
        title: 'Successfully Submitted',
        description: 'Your report has been successfully submitted and will be reviewed by our team.',
        priority: 'Not Set',
        assignedTo: 'Not Assigned Yet',
        nextUpdate: 'Within 48 hours',
        note: 'An investigator will be assigned within 48 hours to begin the formal investigation process.'
      };
  }
};

const getTimelineData = (status: string) => {
  switch (status) {
    case 'under_review':
      return {
        title: 'Review Details',
        items: [
          {
            title: 'Admin Review in Progress',
            description: 'Our admin team has started reviewing the submission. We are checking for accuracy, completeness, and compliance with internal policy standards.',
            timestamp: 'April 15, 2025',
            status: 'current',
            badge: 'Admin Review'
          },
          {
            title: 'Investigator Assignment in Progress',
            description: 'Based on the initial review, the admin team is identifying the most qualified investigator to take over this case for formal investigation.',
            timestamp: '',
            status: 'pending',
            badge: 'Evidence Collection'
          },
          {
            title: 'Report Queued for Admin Review',
            description: 'The report has entered our internal review queue. A reviewer will be assigned shortly to begin the initial assessment.',
            timestamp: '',
            status: 'pending',
            badge: ''
          }
        ]
      };
    case 'investigation':
      return {
        title: 'Investigation Updates',
        items: [
          {
            title: 'Documentation Review Complete',
            description: 'Our team has completed the review of all financial documentation provided. We have identified several areas that require further investigation.',
            timestamp: 'April 29, 2025',
            status: 'completed',
            badge: 'Investigation'
          },
          {
            title: 'Additional Evidence Received',
            description: 'Thank you for providing the additional documentation. This information will help us better understand the patterns of the irregularities.',
            timestamp: 'April 28, 2025',
            status: 'completed',
            badge: 'Evidence Collection'
          },
          {
            title: 'Interview Schedule Confirmed',
            description: 'Thank you for providing the additional expense reports. The information will help us understand the scope of the issue.',
            timestamp: '',
            status: 'current',
            badge: ''
          }
        ]
      };
    case 'action_taken':
      return {
        title: 'Action Summary',
        items: [
          {
            title: 'Corrective Actions Implemented',
            description: 'Following a detailed investigation by Investigator #I-428, corrective actions have been implemented. These include enhanced oversight procedures and additional training for relevant staff.',
            timestamp: 'April 30, 2025',
            status: 'completed',
            badge: 'Investigation Complete'
          },
          {
            title: 'Case Reassigned to Admin Team',
            description: 'Upon completion of the investigation, the case has been reassigned from Investigator #I-428 to the Admin Team for final resolution and monitoring of corrective actions.',
            timestamp: 'April 30, 2025',
            status: 'completed',
            badge: 'Reassignment'
          },
          {
            title: 'Resolution Phase Initiated',
            description: 'The case has entered the resolution phase. A follow-up review is scheduled to evaluate the effectiveness of the implemented actions. Additional financial oversight procedures introduced.',
            timestamp: 'April 29, 2025',
            status: 'completed',
            badge: 'Case Progression'
          }
        ]
      };
    case 'closed':
      return {
        title: 'Case Resolution Summary',
        items: [
          {
            title: 'Final Case Review & Closure',
            description: 'The follow-up review confirmed that all corrective actions were implemented and effective. Compliance checks were fully restored. Long-term controls were successfully validated. No further investigation is required.',
            timestamp: 'May 6, 2025',
            status: 'completed',
            badge: 'Case Verified'
          },
          {
            title: 'Resolution Phase Initiated',
            description: 'Following the completion of all corrective actions, the case has been officially closed. All actions were verified, no further steps are required, and the case has been archived. If new concerns arise, you may initiate a new case through the secure channel.',
            timestamp: 'May 2, 2025',
            status: 'completed',
            badge: 'Case Closed Securely'
          }
        ]
      };
    default:
      return {
        title: 'Review Details',
        items: []
      };
  }
};

function TrackReportPageContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const reportId = searchParams.get('id') || 'WB-20250429-1138';

  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [loading, setLoading] = useState(true);
  const [newMessage, setNewMessage] = useState('');
  const [additionalInfo, setAdditionalInfo] = useState('');
  const [referenceNumber, setReferenceNumber] = useState('');
  const [isSecureDialogOpen, setIsSecureDialogOpen] = useState(false);
  const [currentStatus, setCurrentStatus] = useState('under_review');

  // Mock data - in real app this would come from API based on report ID from URL params
  const mockReportData = useMemo(() => ({
    id: reportId,
    submittedDate: 'April 15, 2025',
    lastUpdated: 'April 29, 2025 at 20:23 AM',
    status: currentStatus === 'under_review' ? 'Under Review' :
      currentStatus === 'investigation' ? 'Under Investigation' :
        currentStatus === 'action_taken' ? 'Action Taken' : 'Case Closed',
    progress: {
      submitted: { completed: true, current: false },
      underReview: { completed: currentStatus !== 'under_review', current: currentStatus === 'under_review' },
      investigation: { completed: ['action_taken', 'closed'].includes(currentStatus), current: currentStatus === 'investigation' },
      actionTaken: { completed: currentStatus === 'closed', current: currentStatus === 'action_taken' },
      closed: { completed: false, current: currentStatus === 'closed' }
    } as Record<string, { completed: boolean; current: boolean }>,
    currentStatus: getStatusDetails(currentStatus),
    timeline: getTimelineData(currentStatus),
    submissionDetails: {
      status: 'Report Submitted Successfully',
      description: 'Your report has been securely submitted to our system. A unique reference number has been assigned.',
      submissionComplete: true,
      reportVerified: {
        completed: true,
        description: 'All required fields have been completed and the initial information has been verified for completeness. Supporting documents have been successfully uploaded.'
      },
      supportingDocuments: {
        uploaded: true,
        files: ['Expense_Claims_Q1.xlsx', 'Email_Evidence.pdf', 'Invoice_Discrepancies.pdf']
      }
    },
    category: {
      type: 'Financial Misconduct',
      description: 'Expense reporting irregularities',
      department: 'Finance',
      location: 'Headquarters',
      incidentDate: 'March 2025'
    },
    messages: [
      {
        id: 1,
        sender: 'You (Anonymous)',
        timestamp: 'Apr 28, 10:45 AM',
        message: 'How anonymous is all relevant documentation regarding the financial discrepancies I reported? I want to ensure full confidentiality as I continue to work at the company.'
      }
    ],
    additionalInfo: {
      description: 'If you have additional information that may assist our investigators, please provide it below. You can upload files or add text details.',
      referenceNumber: 'Please add reference information that may help the investigators.'
    }
  }), [reportId, currentStatus]);

  // Update report data when status changes
  useEffect(() => {
    if (reportData) {
      const updatedData = {
        ...mockReportData,
        id: reportId,
        submittedDate: 'April 15, 2025',
        lastUpdated: 'April 29, 2025 at 20:23 AM',
        status: currentStatus === 'under_review' ? 'Under Review' :
          currentStatus === 'investigation' ? 'Under Investigation' :
            currentStatus === 'action_taken' ? 'Action Taken' : 'Case Closed',
        progress: {
          submitted: { completed: true, current: false },
          underReview: { completed: currentStatus !== 'under_review', current: currentStatus === 'under_review' },
          investigation: { completed: ['action_taken', 'closed'].includes(currentStatus), current: currentStatus === 'investigation' },
          actionTaken: { completed: currentStatus === 'closed', current: currentStatus === 'action_taken' },
          closed: { completed: currentStatus === 'closed', current: false }
        } as Record<string, { completed: boolean; current: boolean }>,
        currentStatus: getStatusDetails(currentStatus),
        timeline: getTimelineData(currentStatus),
        category: {
          type: "Financial Misconduct",
          description: "Expense reporting irregularities",
          department: "Finance",
          location: "Headquarters",
          incidentDate: "March 2025"
        },
        messages: [
          {
            id: 1,
            sender: "You (Anonymous)",
            timestamp: "Apr 28, 11:35 AM",
            message: "First noticed the irregularities in early March when reviewing the quarterly expense reports. The amounts don't match what I saw in the original receipts, and some amounts don't match our vendor invoices."
          }
        ],
        additionalInfo: currentStatus === 'closed' ? {
          description: "This case is now closed.",
          note: "We are no longer accepting new evidence or updates for this investigation.",
          additionalNote: "If you have new concerns, please initiate a new case through the secure channel.",
          referenceNumber: "Case has been closed and archived."
        } : {
          description: "If you have additional information that may assist our investigation, please provide it below. You can upload files or add text details.",
          referenceNumber: "Please add additional information that may help the investigation."
        }
      };
      setReportData(updatedData);
    }
  }, [currentStatus, reportId, mockReportData, reportData]);

  useEffect(() => {
    const loadReportData = async () => {
      const reportId = searchParams.get('id');

      if (!reportId) {
        // Redirect back to dashboard if no report ID provided
        router.push('/dashboard/anonymous');
        return;
      }

      try {
        // Simulate API call to fetch report data
        await new Promise(resolve => setTimeout(resolve, 1000));

        // In real app, fetch data based on reportId
        setReportData(mockReportData);
      } catch {
        toast({
          title: "Error",
          description: "Failed to load report data. Please try again.",
          variant: "destructive",
        });
        router.push('/dashboard/anonymous');
      } finally {
        setLoading(false);
      }
    };

    loadReportData();
  }, [searchParams, router, mockReportData]);

  const handleSendMessage = () => {
    if (!newMessage.trim()) return;

    // Add message to the list (in real app, this would be an API call)
    const updatedMessages = [...reportData!.messages, {
      id: reportData!.messages.length + 1,
      sender: 'You (Anonymous)',
      timestamp: new Date().toLocaleString(),
      message: newMessage
    }];

    setReportData({ ...reportData!, messages: updatedMessages });
    setNewMessage('');

    toast({
      title: "Message Sent",
      description: "Your message has been sent securely to the investigation team.",
    });
  };

  const handleSubmitAdditionalInfo = () => {
    if (!additionalInfo.trim() && !referenceNumber.trim()) return;

    toast({
      title: "Information Submitted",
      description: "Additional information has been submitted successfully.",
    });
    setAdditionalInfo('');
    setReferenceNumber('');
  };

  const handleDownloadReport = () => {
    const reportContent = {
      reportId: reportData?.id,
      submittedDate: reportData?.submittedDate,
      status: reportData?.status,
      category: reportData?.category,
      currentStatus: reportData?.currentStatus,
      timeline: reportData?.timeline,
      messages: reportData?.messages,
      additionalInfo: reportData?.additionalInfo,
      generatedAt: new Date().toISOString()
    };

    const dataStr = JSON.stringify(reportContent, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);

    const exportFileDefaultName = `report-${reportData?.id}-${new Date().toISOString().split('T')[0]}.json`;

    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();

    toast({
      title: "Download Started",
      description: "Your report has been downloaded as JSON file.",
    });
  };

  const handlePrintTimeline = () => {
    const printContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Report Timeline - ${reportData?.id}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .timeline-item { margin-bottom: 20px; padding: 15px; border-left: 3px solid #1E4841; }
            .timeline-title { font-weight: bold; margin-bottom: 5px; }
            .timeline-desc { color: #666; margin-bottom: 5px; }
            .timeline-date { color: #999; font-size: 12px; }
            .badge { background: #f0f0f0; padding: 2px 8px; border-radius: 4px; font-size: 11px; }
            .progress-step { display: inline-block; margin: 10px; text-align: center; }
            .progress-circle { width: 30px; height: 30px; border-radius: 50%; background: #1E4841; color: white; display: flex; align-items: center; justify-content: center; margin: 0 auto 5px; }
            .progress-circle.pending { background: #ccc; }
            .progress-steps { text-align: center; margin: 30px 0; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>Report Timeline</h1>
            <h2>Report #${reportData?.id}</h2>
            <p>Submitted: ${reportData?.submittedDate}</p>
            <p>Status: ${reportData?.status}</p>
          </div>

          <div class="progress-steps">
            <h3>Progress Overview</h3>
            ${Object.entries(reportData?.progress || {}).map(([key, status]) => `
              <div class="progress-step">
                <div class="progress-circle ${status.completed ? '' : 'pending'}">
                  ${status.completed ? '✓' : status.current ? '●' : '○'}
                </div>
                <div>${key === 'underReview' ? 'Under Review' : key === 'actionTaken' ? 'Action Taken' : key.charAt(0).toUpperCase() + key.slice(1)}</div>
              </div>
            `).join('')}
          </div>

          <h3>${reportData?.timeline.title}</h3>
          <div style="margin-bottom: 30px;">
            ${reportData?.timeline.items.map((item, index) => `
              <div class="timeline-item" style="position: relative; padding-left: 25px;">
                <div style="position: absolute; left: 0; top: 5px; width: 12px; height: 12px; border-radius: 50%; background: ${item.status === 'completed' ? '#22c55e' :
        item.status === 'current' ? '#1E4841' : '#d1d5db'
      };"></div>
                ${index < reportData.timeline.items.length - 1 ? `
                  <div style="position: absolute; left: 5px; top: 17px; width: 2px; height: 25px; background: #e5e7eb;"></div>
                ` : ''}
                <div class="timeline-title" style="font-weight: bold; margin-bottom: 8px; display: flex; justify-content: space-between; align-items: center;">
                  <span>${item.title}</span>
                  ${item.badge ? `<span class="badge" style="margin-left: 10px;">${item.badge}</span>` : ''}
                </div>
                <div class="timeline-desc" style="color: #666; margin-bottom: 8px; line-height: 1.4;">
                  ${item.description}
                </div>
                ${item.timestamp ? `<div class="timeline-date" style="color: #999; font-size: 12px; margin-bottom: 15px;">${item.timestamp}</div>` : ''}
              </div>
            `).join('')}
          </div>

          <h3>Current Status Details</h3>
          <div style="background: #f9f9f9; padding: 15px; border-left: 3px solid #1E4841; margin-bottom: 20px;">
            <div style="font-weight: bold; margin-bottom: 10px;">${reportData?.currentStatus.title}</div>
            <div style="margin-bottom: 10px;">${reportData?.currentStatus.description}</div>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 14px;">
              ${reportData?.currentStatus.priority ? `<div><strong>Priority:</strong> ${reportData.currentStatus.priority}</div>` : ''}
              <div><strong>Assigned To:</strong> ${reportData?.currentStatus.assignedTo}</div>
              ${reportData?.currentStatus.nextUpdate ? `<div><strong>Next Update:</strong> ${reportData.currentStatus.nextUpdate}</div>` : ''}
              ${reportData?.currentStatus.closureDate ? `<div><strong>Closure Date:</strong> ${reportData.currentStatus.closureDate}</div>` : ''}
            </div>
            ${reportData?.currentStatus.note ? `<div style="margin-top: 10px; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px;"><strong>Note:</strong> ${reportData.currentStatus.note}</div>` : ''}
          </div>

          <div style="margin-top: 30px; text-align: center; color: #666; font-size: 12px;">
            Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}
          </div>
        </body>
      </html>
    `;

    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(printContent);
      printWindow.document.close();
      printWindow.focus();
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
      }, 250);
    }
  };

  const handleSaveAsPDF = () => {
    const pdfContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Report Summary - ${reportData?.id}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
            .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #1E4841; padding-bottom: 20px; }
            .section { margin-bottom: 25px; }
            .section-title { font-size: 18px; font-weight: bold; color: #1E4841; margin-bottom: 10px; }
            .info-row { display: flex; justify-content: space-between; margin-bottom: 8px; }
            .label { font-weight: bold; }
            .timeline-item { margin-bottom: 15px; padding: 10px; border-left: 3px solid #1E4841; background: #f9f9f9; }
            .message { background: #f5f5f5; padding: 10px; margin-bottom: 10px; border-radius: 5px; }
            .progress-overview { text-align: center; margin: 20px 0; }
            .status-badge { background: #1E4841; color: white; padding: 4px 12px; border-radius: 15px; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>Whistleblower Report Summary</h1>
            <h2>Report #${reportData?.id}</h2>
            <div class="status-badge">${reportData?.status}</div>
          </div>

          <div class="section">
            <div class="section-title">Report Information</div>
            <div class="info-row"><span class="label">Submitted:</span> <span>${reportData?.submittedDate}</span></div>
            <div class="info-row"><span class="label">Last Updated:</span> <span>${reportData?.lastUpdated}</span></div>
            <div class="info-row"><span class="label">Category:</span> <span>${reportData?.category.type}</span></div>
            <div class="info-row"><span class="label">Department:</span> <span>${reportData?.category.department}</span></div>
            <div class="info-row"><span class="label">Location:</span> <span>${reportData?.category.location}</span></div>
          </div>

          <div class="section">
            <div class="section-title">Current Status</div>
            <div class="info-row"><span class="label">Status:</span> <span>${reportData?.currentStatus.title}</span></div>
            <div class="info-row"><span class="label">Assigned To:</span> <span>${reportData?.currentStatus.assignedTo}</span></div>
            ${reportData?.currentStatus.priority ? `<div class="info-row"><span class="label">Priority:</span> <span>${reportData.currentStatus.priority}</span></div>` : ''}
            <p>${reportData?.currentStatus.description}</p>
          </div>

          <div class="section">
            <div class="section-title">${reportData?.timeline.title}</div>
            ${reportData?.timeline.items.map(item => `
              <div class="timeline-item">
                <div style="font-weight: bold;">${item.title}</div>
                <div>${item.description}</div>
                ${item.timestamp ? `<div style="color: #666; font-size: 12px; margin-top: 5px;">${item.timestamp}</div>` : ''}
              </div>
            `).join('')}
          </div>

          <div class="section">
            <div class="section-title">Communication History</div>
            ${reportData?.messages.map(msg => `
              <div class="message">
                <div style="font-weight: bold; margin-bottom: 5px;">${msg.sender} - ${msg.timestamp}</div>
                <div>${msg.message}</div>
              </div>
            `).join('')}
          </div>

          <div style="margin-top: 40px; text-align: center; color: #666; font-size: 12px; border-top: 1px solid #ccc; padding-top: 20px;">
            This report was generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}
            <br>All information is confidential and protected under whistleblower protection policies.
          </div>
        </body>
      </html>
    `;

    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(pdfContent);
      printWindow.document.close();
      printWindow.focus();

      // Wait for content to load then trigger print dialog
      setTimeout(() => {
        printWindow.print();
      }, 500);
    }

    toast({
      title: "PDF Generation",
      description: "Print dialog opened. Use 'Save as PDF' option in print settings.",
    });
  };

  const handleSecureShare = () => {
    // Generate a secure sharing link (in real app, this would be from backend)
    const secureToken = btoa(Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15));
    const secureLink = `${window.location.origin}/dashboard/anonymous/shared-report?token=${secureToken}&id=${reportData?.id}`;

    // Copy to clipboard
    navigator.clipboard.writeText(secureLink).then(() => {
      toast({
        title: "Secure Link Generated",
        description: "A secure sharing link has been generated and copied to clipboard. Link expires in 24 hours.",
      });
    }).catch(() => {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = secureLink;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);

      toast({
        title: "Secure Link Generated",
        description: "A secure sharing link has been generated and copied to clipboard. Link expires in 24 hours.",
      });
    });
  };

  const handleDownloadSummary = () => {
    const summaryContent = `
WHISTLEBLOWER REPORT SUMMARY
============================

Report ID: ${reportData?.id}
Submitted: ${reportData?.submittedDate}
Last Updated: ${reportData?.lastUpdated}
Current Status: ${reportData?.status}

CATEGORY INFORMATION
-------------------
Type: ${reportData?.category.type}
Description: ${reportData?.category.description}
Department: ${reportData?.category.department}
Location: ${reportData?.category.location}
Incident Date: ${reportData?.category.incidentDate}

CURRENT STATUS
--------------
Status: ${reportData?.currentStatus.title}
Assigned To: ${reportData?.currentStatus.assignedTo}
${reportData?.currentStatus.priority ? `Priority: ${reportData.currentStatus.priority}` : ''}
${reportData?.currentStatus.nextUpdate ? `Next Update: ${reportData.currentStatus.nextUpdate}` : ''}

Description: ${reportData?.currentStatus.description}

${reportData?.currentStatus.note ? `Note: ${reportData.currentStatus.note}` : ''}

TIMELINE SUMMARY
---------------
${reportData?.timeline.items.map(item => `
• ${item.title}
  ${item.description}
  ${item.timestamp ? `Date: ${item.timestamp}` : ''}
  ${item.badge ? `Status: ${item.badge}` : ''}
`).join('\n')}

COMMUNICATION HISTORY
--------------------
${reportData?.messages.map(msg => `
[${msg.timestamp}] ${msg.sender}:
${msg.message}
`).join('\n')}

---
Generated on: ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}
This summary is confidential and protected under whistleblower protection policies.
    `.trim();

    const dataUri = 'data:text/plain;charset=utf-8,' + encodeURIComponent(summaryContent);
    const exportFileDefaultName = `report-summary-${reportData?.id}-${new Date().toISOString().split('T')[0]}.txt`;

    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();

    toast({
      title: "Summary Downloaded",
      description: "Report summary has been downloaded as text file.",
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#1E4841] mx-auto mb-4"></div>
          <p className="text-gray-600">Loading report data...</p>
        </div>
      </div>
    );
  }

  if (!reportData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Report Not Found</h3>
          <p className="text-gray-600 mb-4">
            Unable to load report data. Please try again.
          </p>
          <Button onClick={() => router.push('/dashboard/anonymous')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="flex items-center mr-4">
                <Image
                  src="/logo.svg"
                  alt="7IRIS Logo"
                  width={83}
                  height={37}
                  className="h-8 w-auto hover:scale-105 transition-transform duration-300"
                  priority
                />
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">Last Updated: {reportData.lastUpdated}</span>
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                Anonymous Session
              </Badge>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-6">
          {/* Report Header */}
          <div className="flex justify-between items-start mb-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Report #{reportData.id}</h1>
              <p className="text-gray-600 mt-1">Submitted on {reportData.submittedDate}</p>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline" size='sm'
                onClick={() => router.push('/dashboard/anonymous')}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Button>
              <Button variant="outline" size="sm" onClick={handleDownloadSummary}>
                <Download className="h-4 w-4 mr-2" />
                Download Summary
              </Button>
              <Button variant="outline" size="sm" onClick={handlePrintTimeline}>
                <Printer className="h-4 w-4 mr-2" />
                Print
              </Button>
            </div>
          </div>

          {/* Status Testing Buttons - Remove in production */}
          <div className="flex gap-2 mb-4 p-2 bg-gray-100 rounded no-print">
            <span className="text-xs text-gray-600 mr-2">Test Status:</span>
            <Button size="sm" variant="outline" onClick={() => setCurrentStatus('under_review')}>
              Under Review
            </Button>
            <Button size="sm" variant="outline" onClick={() => setCurrentStatus('investigation')}>
              Investigation
            </Button>
            <Button size="sm" variant="outline" onClick={() => setCurrentStatus('action_taken')}>
              Action Taken
            </Button>
            <Button size="sm" variant="outline" onClick={() => setCurrentStatus('closed')}>
              Closed
            </Button>
          </div>

          {/* Progress Bar */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Report Progress</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between mb-4">
                {Object.entries(reportData.progress).map(([key, status]) => (
                  <div key={key} className="flex flex-col items-center">
                    <div className={`w-12 h-12 rounded-full flex items-center justify-center mb-2 print-icon ${status.completed ? 'completed bg-[#1E4841] text-white' :
                        status.current ? 'current bg-gray-300 text-gray-600' : 'pending bg-gray-200 text-gray-400'
                      }`}>
                      {status.completed ? <CheckCircle className="h-6 w-6" /> :
                        key === 'submitted' ? <FileText className="h-6 w-6" /> :
                          key === 'underReview' ? <FileText className="h-6 w-6" /> :
                            key === 'investigation' ? <AlertCircle className="h-6 w-6" /> :
                              key === 'actionTaken' ? <CheckCircle className="h-6 w-6" /> :
                                <CheckCircle className="h-6 w-6" />}
                    </div>
                    <span className="text-xs text-center capitalize">
                      {key === 'underReview' ? 'Under Review' :
                        key === 'actionTaken' ? 'Action Taken' :
                          key}
                    </span>
                    <span className="text-xs text-gray-500 text-center">
                      {status.current ? 'Current' : status.completed ? 'Complete' : 'Pending'}
                    </span>
                  </div>
                ))}
              </div>
              {/* Progress line */}
              <div className="relative">
                <div className="absolute top-6 left-6 right-6 h-0.5 bg-gray-200"></div>
                <div className="absolute top-6 left-6 h-0.5 bg-[#1E4841]" style={{
                  width: `${(() => {
                    const totalSteps = Object.keys(reportData.progress).length;
                    const completedSteps = Object.values(reportData.progress).filter(status => status.completed).length;
                    const currentStepIndex = Object.values(reportData.progress).findIndex(status => status.current);

                    // If closed is completed, show 100%
                    if (currentStatus === 'closed') {
                      return 95; // Full width between circles
                    }

                    if (currentStepIndex >= 0) {
                      return ((completedSteps + 0.5) / totalSteps) * 80; // 80% is the width between first and last circle
                    }
                    return (completedSteps / totalSteps) * 80;
                  })()}%`
                }}></div>
              </div>
            </CardContent>
          </Card>

          {/* Main Content Grid - Three Column Layout with Tighter Spacing */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 auto-rows-min">
            {/* First Column */}
            <div className="space-y-6">
              {/* Current Status */}
              <Card>
                <CardHeader>
                  <CardTitle>Current Status</CardTitle>
                </CardHeader>
                <CardContent>
                  <Badge className="mb-4 bg-green-100 text-green-800">
                    {reportData.currentStatus.title}
                  </Badge>
                  <p className="text-sm text-gray-600 mb-4">
                    {reportData.currentStatus.description}
                  </p>
                  <div className="space-y-2 text-sm">
                    {reportData.currentStatus.priority && (
                      <div className="flex justify-between">
                        <span className="text-gray-500">Priority:</span>
                        <span>{reportData.currentStatus.priority}</span>
                      </div>
                    )}
                    <div className="flex justify-between">
                      <span className="text-gray-500">Assigned To:</span>
                      <span>{reportData.currentStatus.assignedTo}</span>
                    </div>
                    {reportData.currentStatus.nextUpdate && (
                      <div className="flex justify-between">
                        <span className="text-gray-500">Next Update:</span>
                        <span>{reportData.currentStatus.nextUpdate}</span>
                      </div>
                    )}
                    {reportData.currentStatus.closureDate && (
                      <div className="flex justify-between">
                        <span className="text-gray-500">Closure Date:</span>
                        <span>{reportData.currentStatus.closureDate}</span>
                      </div>
                    )}
                  </div>
                  {reportData.currentStatus.note && (
                    <Alert className="mt-4 bg-yellow-50 border-yellow-200">
                      <AlertCircle className="h-4 w-4 text-yellow-600" />
                      <AlertDescription className="text-xs text-yellow-800">
                        {reportData.currentStatus.note}
                      </AlertDescription>
                    </Alert>
                  )}
                </CardContent>
              </Card>

              {/* Report Category */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Building className="h-4 w-4 mr-2" />
                    Report Category
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center">
                      <AlertCircle className="h-4 w-4 text-[#1E4841] mr-2" />
                      <span className="font-medium text-sm">{reportData.category.type}</span>
                    </div>
                    <p className="text-xs text-gray-600 ml-6">{reportData.category.description}</p>

                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-500 flex items-center">
                          <Building className="h-3 w-3 mr-1" />
                          Department:
                        </span>
                        <span>{reportData.category.department}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500 flex items-center">
                          <MapPin className="h-3 w-3 mr-1" />
                          Location:
                        </span>
                        <span>{reportData.category.location}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500 flex items-center">
                          <Calendar className="h-3 w-3 mr-1" />
                          Incident Date:
                        </span>
                        <span>{reportData.category.incidentDate}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Second Column */}
            <div className="space-y-6">
              {/* Timeline Details */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Clock className="h-4 w-4 mr-2" />
                    {reportData.timeline.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {reportData.timeline.items.map((item, index) => (
                      <div key={index} className="relative">
                        <div className="flex items-start">
                          <div className={`w-3 h-3 rounded-full mt-1 mr-3 ${item.status === 'completed' ? 'bg-green-500' :
                              item.status === 'current' ? 'bg-[#1E4841]' : 'bg-gray-300'
                            }`} />
                          <div className="flex-1">
                            <div className="flex items-center justify-between mb-1">
                              <h4 className="text-sm font-medium">{item.title}</h4>
                              {item.badge && (
                                <Badge variant="outline" className="text-xs">
                                  {item.badge}
                                </Badge>
                              )}
                            </div>
                            <p className="text-xs text-gray-600 mb-1">{item.description}</p>
                            {item.timestamp && (
                              <p className="text-xs text-gray-500">{item.timestamp}</p>
                            )}
                          </div>
                        </div>
                        {index < reportData.timeline.items.length - 1 && (
                          <div className="absolute left-1.5 top-4 w-0.5 h-6 bg-gray-200" />
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Additional Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Additional Information</CardTitle>
                </CardHeader>
                <CardContent>
                  {currentStatus === 'closed' ? (
                    <div className="space-y-4">
                      <p className="text-sm text-gray-800 font-medium">
                        {reportData.additionalInfo.description}
                      </p>

                      <p className="text-sm text-gray-600">
                        {reportData.additionalInfo.note}
                      </p>

                      <p className="text-sm text-gray-600">
                        {reportData.additionalInfo.additionalNote}
                      </p>
                    </div>
                  ) : (
                    <>
                      <p className="text-xs text-gray-600 mb-4">
                        {reportData.additionalInfo.description}
                      </p>

                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="reference-number" className="text-sm font-medium text-gray-700">
                            Reference Number
                          </Label>
                          <p className="text-xs text-gray-500 mb-2">
                            {reportData.additionalInfo.referenceNumber}
                          </p>
                          <Input
                            id="reference-number"
                            placeholder="Enter reference information..."
                            value={referenceNumber}
                            onChange={(e) => setReferenceNumber(e.target.value)}
                            className="text-sm"
                          />
                        </div>

                        <div>
                          <Label className="text-sm font-medium text-gray-700 block mb-2">
                            Evidence Files
                          </Label>
                          <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-400 transition-colors cursor-pointer">
                            <Upload className="h-6 w-6 text-gray-400 mx-auto mb-2" />
                            <p className="text-xs text-gray-500 mb-2">Upload Files (PDF, JPG, PNG, DOC)</p>
                            <p className="text-xs text-gray-400">Maximum File Size: 10MB</p>
                            <input
                              type="file"
                              multiple
                              accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                              className="hidden"
                              id="file-upload"
                            />
                          </div>
                        </div>

                        <Button
                          className="w-full bg-[#1E4841] hover:bg-[#2A5A52]"
                          onClick={handleSubmitAdditionalInfo}
                        >
                          Submit Additional Information
                        </Button>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Third Column */}
            <div className="space-y-6">
              {/* Secure Communication */}
              <Card>
                <CardHeader>
                  <CardTitle>Secure Communication</CardTitle>
                </CardHeader>
                <CardContent>
                  {currentStatus === 'closed' ? (
                    <Alert className="mb-4 bg-green-50 border-green-200">
                      <Shield className="h-4 w-4 text-green-600" />
                      <AlertDescription className="text-xs text-green-800">
                        <strong>Communication Channel Closed:</strong><br />
                        This communication channel has been archived. All messages are preserved and encrypted.
                      </AlertDescription>
                    </Alert>
                  ) : (
                    <Alert className="mb-4 bg-blue-50 border-blue-200">
                      <Shield className="h-4 w-4 text-blue-600" />
                      <AlertDescription className="text-xs text-blue-800">
                        All messages are end-to-end encrypted. Your identity remains anonymous throughout this conversation.
                      </AlertDescription>
                    </Alert>
                  )}

                  {/* Show only latest 2 messages */}
                  <div className="space-y-3 mb-4">
                    {(() => {
                      // Create all messages array including system/investigator messages
                      const allMessages = [];

                      // Add system notification based on status
                      if (currentStatus === 'under_review') {
                        allMessages.push({
                          id: 'system-1',
                          sender: 'System Notification',
                          timestamp: 'Apr 15, 2025',
                          message: 'Your report is currently under review by the Admin Team. All submitted information has been received and is being reviewed.',
                          type: 'system'
                        });
                      }

                      // Add investigator messages based on status
                      if (currentStatus === 'investigation') {
                        allMessages.push({
                          id: 'inv-1',
                          sender: 'Investigator #I-428',
                          timestamp: 'Apr 28, 11:35 AM',
                          message: 'Thank you for the detailed clarification. That information is very helpful. Could you also confirm whether these discrepancies were escalated internally before you submitted this report?',
                          type: 'investigator'
                        });
                      }

                      if (currentStatus === 'action_taken') {
                        allMessages.push({
                          id: 'inv-2',
                          sender: 'Investigator #I-428',
                          timestamp: 'Apr 30, 2:15 PM',
                          message: 'Following our investigation, we have implemented corrective actions. Enhanced oversight procedures and additional training have been put in place. Thank you for bringing this to our attention.',
                          type: 'investigator'
                        });
                      }

                      if (currentStatus === 'closed') {
                        allMessages.push({
                          id: 'admin-1',
                          sender: 'Admin Team',
                          timestamp: 'May 6, 10:00 AM',
                          message: 'This case has been successfully resolved and closed. All corrective actions have been verified as effective. Thank you for your contribution to maintaining our organizational integrity.',
                          type: 'admin'
                        });
                      }

                      // Add user messages
                      reportData.messages.forEach(msg => {
                        allMessages.push({
                          ...msg,
                          type: 'user'
                        });
                      });

                      // Sort by timestamp and get last 2
                      const lastTwoMessages = allMessages.slice(-2);

                      return lastTwoMessages.map((msg) => (
                        <div key={msg.id} className={`p-3 rounded-lg ${msg.type === 'system' ? 'bg-blue-50 border border-blue-200' : 'bg-gray-50'
                          }`}>
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-xs font-medium">{msg.sender}</span>
                            <span className="text-xs text-gray-500">{msg.timestamp}</span>
                          </div>
                          <p className={`text-xs ${msg.type === 'system' ? 'text-blue-800' : ''}`}>
                            {msg.message}
                          </p>
                        </div>
                      ));
                    })()}
                  </div>

                  {/* Message input or closed notification */}
                  {currentStatus === 'closed' ? (
                    <div className="mb-3">
                      <Alert className="bg-gray-50 border-gray-200">
                        <AlertDescription className="text-xs text-gray-600">
                          <strong>Communication Channel Locked:</strong><br />
                          This channel has been closed and archived. No new messages can be sent.
                        </AlertDescription>
                      </Alert>
                    </div>
                  ) : (
                    <div className="space-y-2 mb-3">
                      <Textarea
                        placeholder="Type your message here..."
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        className="text-sm"
                        rows={2}
                      />
                      <div className="flex justify-between items-center">
                        <Button variant="outline" size="sm">
                          <Paperclip className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          className="bg-[#1E4841] hover:bg-[#2A5A52]"
                          onClick={handleSendMessage}
                        >
                          Send Message
                        </Button>
                      </div>
                    </div>
                  )}

                  {/* Expand to full conversation or download history */}
                  {currentStatus === 'closed' ? (
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => {
                        // Handle download communication history
                        toast({
                          title: "Download Started",
                          description: "Communication history is being prepared for download.",
                        });
                      }}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Download Communication History
                    </Button>
                  ) : (
                    <Dialog open={isSecureDialogOpen} onOpenChange={setIsSecureDialogOpen}>
                      <DialogTrigger asChild>
                        <Button
                          variant="outline"
                          className="w-full"
                          onClick={() => setIsSecureDialogOpen(true)}
                        >
                          <MessageSquare className="h-4 w-4 mr-2" />
                          View Full Conversation
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-2xl max-h-[80vh] animate-in slide-in-from-bottom-4 duration-300">
                        <DialogHeader>
                          <DialogTitle>Secure Communication</DialogTitle>
                          <DialogDescription>
                            All messages are end-to-end encrypted. Your identity remains anonymous.
                          </DialogDescription>
                        </DialogHeader>

                        <ScrollArea className="h-96 w-full pr-4">
                          <div className="space-y-3">
                            <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
                              <div className="flex items-center justify-between mb-2">
                                <span className="text-xs font-medium">System Notification</span>
                              </div>
                              <p className="text-xs text-blue-800">
                                Your report is currently under review by the Admin Team. All submitted information has been received and is being reviewed to determine if further investigation is required.
                              </p>
                            </div>

                            <div className="bg-gray-50 p-3 rounded-lg">
                              <div className="flex items-center justify-between mb-2">
                                <span className="text-xs font-medium">Investigator #I-428</span>
                                <span className="text-xs text-gray-500">Apr 28, 11:35 AM</span>
                              </div>
                              <p className="text-xs">
                                Thank you for the detailed clarification. That information is very helpful. Could you also confirm whether these discrepancies were escalated internally before you submitted this report?
                              </p>
                            </div>

                            {reportData.messages.map((msg) => (
                              <div key={msg.id} className="bg-gray-50 p-3 rounded-lg">
                                <div className="flex items-center justify-between mb-2">
                                  <span className="text-xs font-medium">{msg.sender}</span>
                                  <span className="text-xs text-gray-500">{msg.timestamp}</span>
                                </div>
                                <p className="text-xs">{msg.message}</p>
                              </div>
                            ))}
                          </div>
                        </ScrollArea>

                        <div className="space-y-2 pt-4 border-t">
                          <Textarea
                            placeholder="Type Your Message Here..."
                            value={newMessage}
                            onChange={(e) => setNewMessage(e.target.value)}
                            className="text-sm"
                            rows={3}
                          />
                          <div className="flex justify-between items-center">
                            <Button variant="outline" size="sm">
                              <Paperclip className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              className="bg-[#1E4841] hover:bg-[#2A5A52]"
                              onClick={handleSendMessage}
                            >
                              Send Message
                            </Button>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                  )}
                </CardContent>
              </Card>

              {/* Actions */}
              <Card>
                <CardHeader>
                  <CardTitle>Actions</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center">
                      <Button
                        variant="outline"
                        className="w-full mb-2 h-12"
                        onClick={handleDownloadReport}
                      >
                        <Download className="h-5 w-5" />
                      </Button>
                      <p className="text-xs text-gray-600">Download Report</p>
                    </div>

                    <div className="text-center">
                      <Button
                        variant="outline"
                        className="w-full mb-2 h-12"
                        onClick={handlePrintTimeline}
                      >
                        <Printer className="h-5 w-5" />
                      </Button>
                      <p className="text-xs text-gray-600">Print Timeline</p>
                    </div>

                    <div className="text-center">
                      <Button
                        variant="outline"
                        className="w-full mb-2 h-12"
                        onClick={handleSaveAsPDF}
                      >
                        <FileText className="h-5 w-5" />
                      </Button>
                      <p className="text-xs text-gray-600">Save as PDF</p>
                    </div>

                    <div className="text-center">
                      <Button
                        variant="outline"
                        className="w-full mb-2 h-12"
                        onClick={handleSecureShare}
                      >
                        <Share2 className="h-5 w-5" />
                      </Button>
                      <p className="text-xs text-gray-600">Secure Share</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}

// Loading component for Suspense fallback
function TrackReportLoading() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/6 mb-8"></div>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
            <div className="space-y-3">
              <div className="h-64 bg-gray-200 rounded"></div>
              <div className="h-48 bg-gray-200 rounded"></div>
            </div>
            <div className="space-y-3">
              <div className="h-80 bg-gray-200 rounded"></div>
              <div className="h-32 bg-gray-200 rounded"></div>
            </div>
            <div className="space-y-3">
              <div className="h-96 bg-gray-200 rounded"></div>
              <div className="h-24 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Main export with Suspense wrapper
export default function TrackReportPage() {
  return (
    <Suspense fallback={<TrackReportLoading />}>
      <TrackReportPageContent />
    </Suspense>
  );
}
