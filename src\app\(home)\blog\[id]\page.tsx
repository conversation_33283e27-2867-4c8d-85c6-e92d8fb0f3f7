"use client";

// Blog post page component
import { useParams } from "next/navigation";
import { useState, useEffect } from "react";
import Link from "next/link";
import { Facebook, Twitter, Linkedin, Share2 } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { BlogPostLayout } from "@/components/home-components/blog";
import Header from "@/components/home-components/shared/Header";
import Footer from "@/components/home-components/shared/Footer";
import { BlogCard } from "@/lib/types";
import { useBlogPosts } from "@/lib/hooks/useData";

export default function BlogPostPage() {
  const params = useParams();
  const id = typeof params.id === 'string' ? params.id : '';

  const [blogPost, setBlogPost] = useState<BlogCard | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch all blog posts to find the current one and related posts
  const { posts: allPosts, loading: postsLoading, error: postsError } = useBlogPosts();

  useEffect(() => {
    if (postsLoading) return;

    if (postsError) {
      setError(postsError);
      setIsLoading(false);
      return;
    }

    // Handle invalid IDs
    if (!id || id === 'undefined' || id === 'unknown') {
      setError('Invalid blog post ID');
      setIsLoading(false);
      return;
    }

    // Find the blog post by ID or slug
    const post = allPosts.find(p => p.id === id || p.slug === id);
    setBlogPost(post || null);
    setIsLoading(false);
  }, [id, allPosts, postsLoading, postsError]);

  // Get related posts (same category, excluding current post)
  const relatedPosts = blogPost
    ? allPosts.filter(post =>
        post.id !== blogPost.id &&
        post.category === blogPost.category
      ).slice(0, 3)
    : [];

  if (isLoading || postsLoading) {
    return (
      <div className="flex min-h-screen flex-col">
        <Header />
        <main className="flex-1 pt-20 px-4 sm:px-8 md:px-12 lg:px-[180px]">
          <div className="space-y-8 py-12">
            <Skeleton className="h-8 w-3/4" />
            <Skeleton className="h-6 w-1/2" />
            <Skeleton className="h-64" />
            <div className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-5/6" />
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex min-h-screen flex-col">
        <Header />
        <main className="flex-1 pt-20 px-4 sm:px-8 md:px-12 lg:px-[180px]">
          <div className="py-12 text-center">
            <h1 className="text-2xl font-bold text-red-600 mb-4">Error Loading Blog Post</h1>
            <p className="text-gray-600 mb-8">{error}</p>
            <Link href="/blog">
              <Button variant="default" className="bg-[#1E4841] text-white hover:bg-[#132f2a]">
                Return to Blog
              </Button>
            </Link>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  if (!blogPost) {
    return (
      <div className="flex min-h-screen flex-col">
        <Header />
        <main className="flex-1 pt-20 px-4 sm:px-8 md:px-12 lg:px-[180px]">
          <div className="py-12 text-center">
            <h1 className="text-2xl font-bold text-[#1E4841] mb-4">Blog Post Not Found</h1>
            <p className="text-gray-600 mb-8">The blog post you&apos;re looking for doesn&apos;t exist or has been moved.</p>
            <Link href="/blog">
              <Button variant="default" className="bg-[#1E4841] text-white hover:bg-[#132f2a]">
                Return to Blog
              </Button>
            </Link>
          </div>
        </main>
        <Footer />
      </div>
    );
  }
  
  return (
    <BlogPostLayout blogPost={blogPost} relatedPosts={relatedPosts}>
      <div className="mt-8 sm:mt-10 md:mt-12 lg:block hidden">
        <h3 className="text-base sm:text-lg font-semibold text-[#1E4841] mb-3 sm:mb-4">Related Topics</h3>
        <div className="flex flex-wrap gap-2 gap-y-3 sm:gap-y-4">
          {(blogPost.tags || ['Whistleblower Protection', 'Compliance', 'Corporate Ethics']).map((tag) => (
            <Link href={`/blog/search?tag=${tag}`} key={tag}>
              <span className="px-3 py-1 sm:px-4 sm:py-2 bg-[#ECF4E9] text-[#1E4841] rounded-full text-xs sm:text-sm hover:bg-[#1E4841] hover:text-white transition-colors">
                {tag}
              </span>
            </Link>
          ))}
        </div>
      </div>

      <div className="mt-8 sm:mt-10 md:mt-12">
        <h3 className="text-base sm:text-lg font-semibold text-[#1E4841] mb-3 sm:mb-4">Share This Article</h3>
        <div className="flex gap-4">
          <Button variant="outline" size="icon" className="rounded-full">
            <Facebook className="h-5 w-5 text-[#1E4841]" />
          </Button>
          <Button variant="outline" size="icon" className="rounded-full">
            <Twitter className="h-5 w-5 text-[#1E4841]" />
          </Button>
          <Button variant="outline" size="icon" className="rounded-full">
            <Linkedin className="h-5 w-5 text-[#1E4841]" />
          </Button>
          <Button variant="outline" size="icon" className="rounded-full">
            <Share2 className="h-5 w-5 text-[#1E4841]" />
          </Button>
        </div>
      </div>
    </BlogPostLayout>
  );
}