import { NextResponse } from 'next/server';
import { DataService } from '@/lib/db/dataService';
import { withCompanyIsolation, AuthenticatedRequest } from '@/lib/middleware/auth';
import connectDB from '@/lib/db/mongodb';

export const runtime = 'nodejs';

export const POST = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();

    const reportData = await request.json();

    // Process draft data (can be partial)
    const processedDraftData = {
      // Step 1 data (may be partial)
      title: reportData.title || '',
      description: reportData.description || '',
      category: reportData.category || '',
      dateOfOccurrence: reportData.dateOfOccurrence || '',
      location: reportData.location || '',
      isAnonymous: reportData.isAnonymous || false,

      // Step 2 data (may be partial)
      incidentDate: reportData.incidentDate || '',
      incidentTime: reportData.incidentTime || '',
      specificLocation: reportData.specificLocation || '',
      departmentInvolved: reportData.departmentInvolved || '',
      peopleInvolved: reportData.peopleInvolved || '',

      // Witness information
      hasWitnesses: reportData.witnessInfo?.hasWitnesses || false,
      witnessDetails: reportData.witnessInfo?.witnessDetails || '',

      // Evidence information
      hasEvidence: reportData.evidenceInfo?.hasEvidence || false,
      evidenceDescription: reportData.evidenceInfo?.evidenceDescription || '',
      evidenceFiles: reportData.evidenceInfo?.evidenceFiles || [],

      // Impact assessment
      urgencyLevel: reportData.urgencyLevel || 'Medium',
      financialImpact: reportData.impactAssessment?.financialImpact || '',
      operationalImpact: reportData.impactAssessment?.operationalImpact || '',
      reputationalImpact: reportData.impactAssessment?.reputationalImpact || '',

      // Previous reports
      hasPreviousReports: reportData.previousReports?.hasPreviousReports || false,
      previousReportDetails: reportData.previousReports?.previousReportDetails || '',

      // Additional information
      additionalComments: reportData.additionalComments || '',

      // Communication preferences
      emailUpdates: reportData.reportingPreferences?.emailUpdates ?? true,
      smsUpdates: reportData.reportingPreferences?.smsUpdates ?? false,

      // Draft metadata
      draftStep: reportData.step || 1,
      lastSavedAt: new Date(),

      // System fields
      userId: new (await import('mongoose')).Types.ObjectId(request.user!.id),
      status: 'Draft',
      priority: reportData.urgencyLevel || 'Medium',
      isDraft: true
    };

    // Create or update draft report
    const draftReport = await DataService.createReport(processedDraftData);

    return NextResponse.json({
      success: true,
      data: draftReport,
      message: 'Draft saved successfully'
    }, { status: 201 });
  } catch (error) {
    console.error('Draft report API error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to save draft' },
      { status: 500 }
    );
  }
});