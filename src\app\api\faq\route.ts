import { NextRequest, NextResponse } from 'next/server';
import { DataService } from '@/lib/db/dataService';
import connectDB from '@/lib/db/mongodb';
import { faqData } from '@/lib/staticContent';

export const runtime = 'nodejs';

// Use static content FAQs as fallback when database is empty

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');

    // Try to get FAQs from database
    let faqs = await DataService.getFAQs(category || undefined);

    // If database is empty, use static content FAQs as fallback
    if (!faqs || faqs.length === 0) {
      let filteredFaqs = faqData.filter(_faq => true); // All static FAQs are active

      if (category) {
        filteredFaqs = filteredFaqs.filter(faq => faq.category === category);
      }

      // Transform static FAQ format to API format
      faqs = filteredFaqs.map((faq, index) => ({
        id: `static-faq-${faq.id}`,
        question: faq.question,
        answer: faq.answer,
        category: faq.category,
        order: index + 1,
        isActive: true,
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01')
      }));
    }

    return NextResponse.json({
      success: true,
      data: faqs
    });
  } catch (error) {
    console.error('FAQ API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}