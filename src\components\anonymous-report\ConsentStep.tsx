"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { ArrowLeft, Shield, AlertTriangle, Lock } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface ReportFormData {
  title: string;
  category: string;
  dateOfOccurrence: string;
  location: string;
  description: string;
  evidenceFiles: File[];
  evidenceDescription: string;
  privacyConsent: boolean;
  reportToken?: string;
  submissionId?: string;
}

interface ConsentStepProps {
  formData: ReportFormData;
  updateFormData: (updates: Partial<ReportFormData>) => void;
  onSubmit: () => void;
  onBack: () => void;
  isSubmitting: boolean;
}

export default function ConsentStep({ 
  formData, 
  updateFormData, 
  onSubmit, 
  onBack, 
  isSubmitting 
}: ConsentStepProps) {
  
  const handleConsentChange = (checked: boolean) => {
    updateFormData({ privacyConsent: checked });
  };

  const isFormValid = () => {
    return formData.privacyConsent;
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="text-xl font-semibold text-gray-900">
          Consent & Final Submission
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Anonymous Submission Notice */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <Shield className="h-5 w-5 text-green-600 mt-0.5" />
            <div>
              <h3 className="font-medium text-green-900 mb-1">
                You are submitting this report anonymously
              </h3>
              <p className="text-sm text-green-700">
                Your identity will not be disclosed in the report
              </p>
            </div>
          </div>
        </div>

        {/* Privacy Consent Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Privacy Consent</h3>
          
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 text-sm text-gray-700 space-y-3">
            <p>
              By submitting this report, you acknowledge that the information provided will be processed in accordance with our 
              privacy policy. Your data will be handled securely and confidentially.
            </p>
            
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Lock className="h-4 w-4 text-gray-500" />
                <span className="font-medium">Data Security:</span>
              </div>
              <ul className="list-disc list-inside ml-6 space-y-1 text-xs">
                <li>All data is encrypted in transit and at rest</li>
                <li>Access is restricted to authorized personnel only</li>
                <li>Your report will be assigned a unique tracking number</li>
                <li>No personally identifiable information is stored for anonymous reports</li>
              </ul>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Shield className="h-4 w-4 text-gray-500" />
                <span className="font-medium">Report Processing:</span>
              </div>
              <ul className="list-disc list-inside ml-6 space-y-1 text-xs">
                <li>Your report will be reviewed by our compliance team</li>
                <li>Investigation will be conducted according to company policies</li>
                <li>Updates will be available through the secure inbox using your tracking number</li>
                <li>All communications remain anonymous</li>
              </ul>
            </div>
          </div>

          {/* Consent Checkbox */}
          <div className="flex items-start space-x-3 p-4 border border-gray-200 rounded-lg">
            <Checkbox
              id="privacyConsent"
              checked={formData.privacyConsent}
              onCheckedChange={handleConsentChange}
              className="mt-1"
            />
            <div className="flex-1">
              <Label 
                htmlFor="privacyConsent" 
                className="text-sm font-medium text-gray-900 cursor-pointer"
              >
                I have read and agree to the Privacy Policy and consent to the processing of my report data*
              </Label>
              <p className="text-xs text-gray-600 mt-1">
                Required to submit your report
              </p>
            </div>
          </div>
        </div>

        {/* Important Notice */}
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription className="text-sm">
            <strong>Once submitted, you cannot edit your report.</strong> Please ensure all information is accurate.
          </AlertDescription>
        </Alert>

        {/* Navigation */}
        <div className="flex justify-between pt-6">
          <Button
            onClick={onBack}
            variant="outline"
            className="px-6 py-2 flex items-center gap-2"
            disabled={isSubmitting}
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
          <Button
            onClick={onSubmit}
            disabled={!isFormValid() || isSubmitting}
            className="bg-[#1E4841] hover:bg-[#1E4841]/90 text-white px-8 py-2 flex items-center gap-2"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Submitting...
              </>
            ) : (
              <>
                <Shield className="h-4 w-4" />
                Submit Report
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
