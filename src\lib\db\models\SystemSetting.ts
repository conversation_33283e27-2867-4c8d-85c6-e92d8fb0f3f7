import { Schema } from 'mongoose';
import { createModel } from '../utils';

const SystemSettingSchema = new Schema({
  companyId: { type: Schema.Types.ObjectId, ref: 'Company', required: true },
  
  // Setting identification
  key: { type: String, required: true }, // e.g., 'email_notifications', 'auto_assignment'
  category: { 
    type: String, 
    enum: ['system', 'security', 'notifications', 'workflow', 'integration', 'compliance'],
    required: true 
  },
  
  // Setting details
  name: { type: String, required: true }, // Human-readable name
  description: { type: String, required: true },
  
  // Value and type
  value: { type: Schema.Types.Mixed, required: true }, // The actual setting value
  valueType: { 
    type: String, 
    enum: ['boolean', 'string', 'number', 'object', 'array'],
    required: true 
  },
  defaultValue: { type: Schema.Types.Mixed, required: true },
  
  // Validation and constraints
  validation: {
    required: { type: Boolean, default: false },
    min: { type: Number }, // For numbers
    max: { type: Number }, // For numbers
    minLength: { type: Number }, // For strings
    maxLength: { type: Number }, // For strings
    pattern: { type: String }, // Regex pattern for strings
    allowedValues: [{ type: Schema.Types.Mixed }] // Enum-like values
  },
  
  // Access control
  isPublic: { type: Boolean, default: false }, // Can be read by non-admin users
  isReadOnly: { type: Boolean, default: false }, // Cannot be modified via UI
  requiresRestart: { type: Boolean, default: false }, // System restart needed after change
  
  // Metadata
  lastModifiedBy: { type: Schema.Types.ObjectId, ref: 'User' },
  lastModifiedAt: { type: Date },
  version: { type: Number, default: 1 },
  
  // History tracking
  changeHistory: [{
    previousValue: { type: Schema.Types.Mixed },
    newValue: { type: Schema.Types.Mixed },
    changedBy: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    changedAt: { type: Date, default: Date.now },
    reason: { type: String }
  }],
  
  // UI configuration
  uiConfig: {
    displayOrder: { type: Number, default: 0 },
    group: { type: String }, // Grouping in UI
    helpText: { type: String },
    placeholder: { type: String },
    isAdvanced: { type: Boolean, default: false }, // Show in advanced settings only
    isHidden: { type: Boolean, default: false } // Hide from UI completely
  }
}, {
  timestamps: true
});

// Compound index for company and key (unique per company)
SystemSettingSchema.index({ companyId: 1, key: 1 }, { unique: true });
SystemSettingSchema.index({ companyId: 1, category: 1 });
SystemSettingSchema.index({ companyId: 1, 'uiConfig.displayOrder': 1 });

// Pre-save middleware to track changes
SystemSettingSchema.pre('save', function(next) {
  if (this.isModified('value') && !this.isNew) {
    // Add to change history
    this.changeHistory.push({
      previousValue: this.get('value', null, { getters: false }),
      newValue: this.value,
      changedBy: this.lastModifiedBy,
      changedAt: new Date(),
      reason: 'Updated via system'
    });
    
    this.version += 1;
    this.lastModifiedAt = new Date();
  }
  next();
});

// Static method to get setting value
SystemSettingSchema.statics.getValue = async function(companyId: string, key: string, defaultValue?: any) {
  const setting = await this.findOne({ companyId, key });
  return setting ? setting.value : defaultValue;
};

// Static method to set setting value
SystemSettingSchema.statics.setValue = async function(
  companyId: string, 
  key: string, 
  value: any, 
  modifiedBy: string,
  reason?: string
) {
  const setting = await this.findOne({ companyId, key });
  if (setting) {
    setting.value = value;
    setting.lastModifiedBy = modifiedBy;
    if (reason && setting.changeHistory.length > 0) {
      setting.changeHistory[setting.changeHistory.length - 1].reason = reason;
    }
    return await setting.save();
  }
  return null;
};

// Static method to get settings by category
SystemSettingSchema.statics.getByCategory = function(companyId: string, category: string) {
  return this.find({ companyId, category }).sort({ 'uiConfig.displayOrder': 1 });
};

export default createModel('SystemSetting', SystemSettingSchema);
