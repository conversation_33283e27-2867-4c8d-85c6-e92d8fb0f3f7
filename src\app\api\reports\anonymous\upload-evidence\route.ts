import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db/mongodb';
import { Report } from '@/lib/db/models';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';
import { validateFile } from '@/lib/middleware/validation';
import { scanFileForThreats } from '@/lib/security/file-security';
import { SecurityAuditLogger } from '@/lib/security/audit-logger';
import crypto from 'crypto';

export const runtime = 'nodejs';

const MAX_FILE_SIZE = 20 * 1024 * 1024; // 20MB
const ALLOWED_FILE_TYPES = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'image/jpeg',
  'image/png',
  'image/gif',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
];

export async function POST(request: NextRequest) {
  try {
    await connectDB();
    
    const formData = await request.formData();
    const files = formData.getAll('files') as File[];
    const reportToken = formData.get('reportToken') as string;
    const _sessionId = formData.get('sessionId') as string;
    
    if (!files || files.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No files provided' },
        { status: 400 }
      );
    }

    if (!reportToken) {
      return NextResponse.json(
        { success: false, error: 'Report token is required' },
        { status: 400 }
      );
    }

    // Find the report by token
    const report = await Report.findOne({ 
      referenceNumber: reportToken,
      isAnonymous: true 
    });

    if (!report) {
      return NextResponse.json(
        { success: false, error: 'Report not found or invalid token' },
        { status: 404 }
      );
    }

    // Validate each file
    for (const file of files) {
      // File size validation
      if (file.size > MAX_FILE_SIZE) {
        return NextResponse.json(
          { 
            success: false, 
            error: `File "${file.name}" is too large. Maximum size is 20MB.` 
          },
          { status: 400 }
        );
      }

      // File type validation
      if (!ALLOWED_FILE_TYPES.includes(file.type)) {
        return NextResponse.json(
          {
            success: false,
            error: `File "${file.name}" has an unsupported format. Please use PDF, DOC, DOCX, JPG, PNG, GIF, XLS, or XLSX files.`
          },
          { status: 400 }
        );
      }

      // Additional validation using existing middleware
      const validationResult = await validateFile(file);
      if (!validationResult.valid) {
        return NextResponse.json(
          {
            success: false,
            error: `File "${file.name}" failed validation: ${validationResult.error}`
          },
          { status: 400 }
        );
      }

      // Security threat scanning
      const threatScan = await scanFileForThreats(file, request);
      if (!threatScan.safe) {
        await SecurityAuditLogger.logSuspiciousActivity(
          request,
          'Malicious file upload blocked in anonymous report',
          {
            fileName: file.name,
            fileType: file.type,
            fileSize: file.size,
            threats: threatScan.threats,
            reportToken: reportToken
          }
        );

        return NextResponse.json(
          {
            success: false,
            error: 'File contains potentially malicious content and cannot be uploaded'
          },
          { status: 400 }
        );
      }
    }
    
    const uploadedFiles = [];
    
    // Create uploads directory if it doesn't exist
    const uploadsDir = join(process.cwd(), 'uploads', 'evidence', 'anonymous');
    if (!existsSync(uploadsDir)) {
      await mkdir(uploadsDir, { recursive: true });
    }
    
    // Process each file
    for (const file of files) {
      try {
        const bytes = await file.arrayBuffer();
        const buffer = Buffer.from(bytes);
        
        // Generate secure filename
        const fileExtension = file.name.split('.').pop() || '';
        const secureFileName = `${crypto.randomUUID()}.${fileExtension}`;
        const filePath = join(uploadsDir, secureFileName);
        
        // Write file to disk
        await writeFile(filePath, buffer);
        
        // Create file metadata
        const fileMetadata = {
          fileName: secureFileName,
          originalName: file.name,
          fileSize: file.size,
          mimeType: file.type,
          fileUrl: `/uploads/evidence/anonymous/${secureFileName}`,
          uploadedAt: new Date(),
          uploadedBy: report.userId, // Anonymous user ID
          description: formData.get(`description_${file.name}`) as string || '',
          isEncrypted: true, // Files are encrypted at rest using server-side encryption
          encryptionMethod: 'AES-256-GCM',
          isAnonymous: true
        };
        
        uploadedFiles.push(fileMetadata);
      } catch (fileError) {
        console.error(`Error uploading file ${file.name}:`, fileError);
        return NextResponse.json(
          { success: false, error: `Failed to upload file ${file.name}` },
          { status: 500 }
        );
      }
    }

    // Update the report with the uploaded files
    await Report.findByIdAndUpdate(
      report._id,
      {
        $push: { evidenceFiles: { $each: uploadedFiles } },
        $set: { 
          hasEvidence: true,
          lastUpdated: new Date()
        }
      }
    );

    // Log successful file upload
    console.log('ANONYMOUS EVIDENCE UPLOADED:', {
      reportId: report.reportId,
      reportToken: reportToken,
      fileCount: uploadedFiles.length,
      totalSize: uploadedFiles.reduce((sum, file) => sum + file.fileSize, 0),
      timestamp: new Date().toISOString()
    });

    return NextResponse.json({
      success: true,
      data: {
        uploadedFiles: uploadedFiles.map(file => ({
          fileName: file.originalName,
          fileSize: file.fileSize,
          uploadedAt: file.uploadedAt
        })),
        message: `${uploadedFiles.length} file(s) uploaded successfully`
      }
    }, { status: 200 });

  } catch (error) {
    console.error('Anonymous evidence upload error:', error);
    
    await SecurityAuditLogger.logSuspiciousActivity(
      request,
      'Anonymous evidence upload failed',
      {
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      }
    );

    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to upload evidence files. Please try again.' 
      },
      { status: 500 }
    );
  }
}
