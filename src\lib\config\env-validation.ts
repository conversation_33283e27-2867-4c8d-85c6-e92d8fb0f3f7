/**
 * Environment variable validation and configuration
 */

import crypto from 'crypto';

interface RequiredEnvVars {
  JWT_SECRET: string;
  MONGODB_URI: string;
  NEXTAUTH_SECRET: string;
  NEXTAUTH_URL: string;
}

interface OptionalEnvVars {
  MESSAGE_ENCRYPTION_KEY?: string;
  SERVER_SECRET?: string;
  EMAIL_SERVER_HOST?: string;
  EMAIL_SERVER_PORT?: string;
  EMAIL_SERVER_USER?: string;
  EMAIL_SERVER_PASSWORD?: string;
  EMAIL_FROM?: string;
  GOOGLE_CLIENT_ID?: string;
  GOOGLE_CLIENT_SECRET?: string;
  MICROSOFT_CLIENT_ID?: string;
  MICROSOFT_CLIENT_SECRET?: string;
  MICROSOFT_TENANT_ID?: string;
}

type EnvVars = RequiredEnvVars & OptionalEnvVars;

/**
 * Validate required environment variables
 */
export function validateEnvironmentVariables(): { valid: boolean; missing: string[]; warnings: string[] } {
  const missing: string[] = [];
  const warnings: string[] = [];

  // Required variables
  const required: (keyof RequiredEnvVars)[] = [
    'JWT_SECRET',
    'MONGODB_URI',
    'NEXTAUTH_SECRET',
    'NEXTAUTH_URL'
  ];

  for (const key of required) {
    if (!process.env[key]) {
      missing.push(key);
    }
  }

  // Check JWT_SECRET strength
  if (process.env.JWT_SECRET) {
    if (process.env.JWT_SECRET.length < 32) {
      warnings.push('JWT_SECRET should be at least 32 characters long');
    }
    if (process.env.JWT_SECRET === 'your-secret-key-change-in-production') {
      missing.push('JWT_SECRET (using default value)');
    }
  }

  // Check NEXTAUTH_SECRET strength
  if (process.env.NEXTAUTH_SECRET && process.env.NEXTAUTH_SECRET.length < 32) {
    warnings.push('NEXTAUTH_SECRET should be at least 32 characters long');
  }

  // Check MongoDB URI format
  if (process.env.MONGODB_URI && !process.env.MONGODB_URI.startsWith('mongodb')) {
    warnings.push('MONGODB_URI should start with mongodb:// or mongodb+srv://');
  }

  // Check NEXTAUTH_URL format
  if (process.env.NEXTAUTH_URL && !process.env.NEXTAUTH_URL.startsWith('http')) {
    warnings.push('NEXTAUTH_URL should be a valid HTTP/HTTPS URL');
  }

  // Production-specific checks
  if (process.env.NODE_ENV === 'production') {
    // Message encryption key is highly recommended for production
    if (!process.env.MESSAGE_ENCRYPTION_KEY) {
      warnings.push('MESSAGE_ENCRYPTION_KEY is recommended for production');
    }

    // Email configuration for production
    if (!process.env.EMAIL_SERVER_HOST) {
      warnings.push('EMAIL_SERVER_HOST is recommended for production notifications');
    }

    // HTTPS enforcement
    if (process.env.NEXTAUTH_URL && !process.env.NEXTAUTH_URL.startsWith('https')) {
      warnings.push('NEXTAUTH_URL should use HTTPS in production');
    }
  }

  return {
    valid: missing.length === 0,
    missing,
    warnings
  };
}

/**
 * Get validated environment configuration
 */
export function getEnvConfig(): EnvVars {
  const validation = validateEnvironmentVariables();
  
  if (!validation.valid) {
    throw new Error(`Missing required environment variables: ${validation.missing.join(', ')}`);
  }

  if (validation.warnings.length > 0 && process.env.NODE_ENV === 'production') {
    console.warn('Environment configuration warnings:', validation.warnings);
  }

  return {
    JWT_SECRET: process.env.JWT_SECRET!,
    MONGODB_URI: process.env.MONGODB_URI!,
    NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET!,
    NEXTAUTH_URL: process.env.NEXTAUTH_URL!,
    MESSAGE_ENCRYPTION_KEY: process.env.MESSAGE_ENCRYPTION_KEY,
    SERVER_SECRET: process.env.SERVER_SECRET,
    EMAIL_SERVER_HOST: process.env.EMAIL_SERVER_HOST,
    EMAIL_SERVER_PORT: process.env.EMAIL_SERVER_PORT,
    EMAIL_SERVER_USER: process.env.EMAIL_SERVER_USER,
    EMAIL_SERVER_PASSWORD: process.env.EMAIL_SERVER_PASSWORD,
    EMAIL_FROM: process.env.EMAIL_FROM,
    GOOGLE_CLIENT_ID: process.env.GOOGLE_CLIENT_ID,
    GOOGLE_CLIENT_SECRET: process.env.GOOGLE_CLIENT_SECRET,
    MICROSOFT_CLIENT_ID: process.env.MICROSOFT_CLIENT_ID,
    MICROSOFT_CLIENT_SECRET: process.env.MICROSOFT_CLIENT_SECRET,
    MICROSOFT_TENANT_ID: process.env.MICROSOFT_TENANT_ID
  };
}

/**
 * Generate secure random secrets for development
 */
export function generateSecrets(): { jwtSecret: string; nextAuthSecret: string; encryptionKey: string } {
  
  return {
    jwtSecret: crypto.randomBytes(64).toString('hex'),
    nextAuthSecret: crypto.randomBytes(64).toString('hex'),
    encryptionKey: crypto.randomBytes(32).toString('hex')
  };
}

/**
 * Check if running in production
 */
export function isProduction(): boolean {
  return process.env.NODE_ENV === 'production';
}

/**
 * Check if running in development
 */
export function isDevelopment(): boolean {
  return process.env.NODE_ENV === 'development';
}

/**
 * Get database configuration
 */
export function getDatabaseConfig() {
  return {
    uri: process.env.MONGODB_URI!,
    options: {
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      bufferMaxEntries: 0,
      bufferCommands: false,
      ...(isProduction() && {
        ssl: true,
        sslValidate: true
      })
    }
  };
}

/**
 * Get email configuration
 */
export function getEmailConfig() {
  return {
    host: process.env.EMAIL_SERVER_HOST,
    port: process.env.EMAIL_SERVER_PORT ? parseInt(process.env.EMAIL_SERVER_PORT) : 587,
    secure: process.env.EMAIL_SERVER_PORT === '465',
    auth: process.env.EMAIL_SERVER_USER && process.env.EMAIL_SERVER_PASSWORD ? {
      user: process.env.EMAIL_SERVER_USER,
      pass: process.env.EMAIL_SERVER_PASSWORD
    } : undefined,
    from: process.env.EMAIL_FROM || '<EMAIL>'
  };
}

// Validate environment on module load
if (typeof window === 'undefined') {
  try {
    validateEnvironmentVariables();
  } catch (error) {
    if (process.env.NODE_ENV === 'production') {
      console.error('Environment validation failed:', error);
      process.exit(1);
    }
  }
}
