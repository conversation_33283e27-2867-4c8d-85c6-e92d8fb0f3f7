import { NextRequest, NextResponse } from 'next/server';
import { connectDB } from '@/lib/db/connection';
import SupportRequest from '@/lib/db/models/SupportRequest';
import { emailService } from '@/lib/email/emailService';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { z } from 'zod';

export const runtime = 'nodejs';

const supportRequestSchema = z.object({
  priorityLevel: z.enum(['low', 'medium', 'high', 'urgent']),
  issueCategory: z.enum(['technical', 'account', 'billing', 'feature', 'bug', 'other']),
  issueDescription: z.string().min(10).max(2000),
  fullName: z.string().min(2).max(100),
  emailAddress: z.email(),
});

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const formData = await request.formData();
    
    // Extract form fields
    const data = {
      priorityLevel: formData.get('priorityLevel') as string,
      issueCategory: formData.get('issueCategory') as string,
      issueDescription: formData.get('issueDescription') as string,
      fullName: formData.get('fullName') as string,
      emailAddress: formData.get('emailAddress') as string,
    };

    // Validate the form data
    const validationResult = supportRequestSchema.safeParse(data);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid form data',
          details: validationResult.error.issues
        },
        { status: 400 }
      );
    }

    const validatedData = validationResult.data;

    // Handle file uploads
    const attachments: any[] = [];
    const files = formData.getAll('attachments') as File[];
    
    if (files && files.length > 0) {
      // Create uploads directory if it doesn't exist
      const uploadsDir = join(process.cwd(), 'uploads', 'support');
      await mkdir(uploadsDir, { recursive: true });

      for (const file of files) {
        if (file.size > 0) {
          const bytes = await file.arrayBuffer();
          const buffer = Buffer.from(bytes);
          
          // Generate unique filename
          const timestamp = Date.now();
          const filename = `${timestamp}-${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`;
          const filepath = join(uploadsDir, filename);
          
          await writeFile(filepath, buffer);
          
          attachments.push({
            filename,
            originalName: file.name,
            mimeType: file.type,
            size: file.size,
            path: filepath
          });
        }
      }
    }

    // Get client metadata
    const userAgent = request.headers.get('user-agent') || undefined;
    const forwarded = request.headers.get('x-forwarded-for');
    const ipAddress = forwarded ? forwarded.split(',')[0] : request.headers.get('x-real-ip') || undefined;

    // Create support request
    const supportRequest = new SupportRequest({
      ...validatedData,
      attachments: attachments.length > 0 ? attachments : undefined,
      metadata: {
        userAgent,
        ipAddress,
        source: 'contact_form'
      }
    });

    await supportRequest.save();

    // Send notification email to support team
    try {
      const supportEmail = process.env.SUPPORT_EMAIL || process.env.CONTACT_FORM_RECIPIENT || process.env.EMAIL_SERVER_USER;
      
      if (supportEmail) {
        const emailSent = await emailService.sendSupportRequestNotification({
          ticketNumber: supportRequest.ticketNumber,
          priorityLevel: supportRequest.priorityLevel,
          issueCategory: supportRequest.issueCategory,
          issueDescription: supportRequest.issueDescription,
          fullName: supportRequest.fullName,
          emailAddress: supportRequest.emailAddress,
          attachmentCount: attachments.length,
          supportEmail
        });

        if (emailSent) {
          // Send auto-reply to customer
          await emailService.sendSupportRequestAutoReply({
            ticketNumber: supportRequest.ticketNumber,
            fullName: supportRequest.fullName,
            emailAddress: supportRequest.emailAddress,
            priorityLevel: supportRequest.priorityLevel
          });
        }
      }
    } catch (emailError) {
      console.error('Failed to send support request emails:', emailError);
      // Don't fail the request if email fails
    }

    return NextResponse.json({
      success: true,
      data: {
        ticketNumber: supportRequest.ticketNumber,
        message: 'Your support request has been submitted successfully. We will get back to you soon!',
        estimatedResponseTime: getSLAResponseTime(supportRequest.priorityLevel)
      }
    });

  } catch (error) {
    console.error('Support request submission error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'An unexpected error occurred. Please try again later.' 
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const ticketNumber = searchParams.get('ticket');
    const email = searchParams.get('email');

    if (ticketNumber && email) {
      // Get specific support request
      const supportRequest = await (SupportRequest as any).findOne({
        ticketNumber,
        emailAddress: email.toLowerCase()
      }).select('-metadata -attachments.path');

      if (!supportRequest) {
        return NextResponse.json(
          { success: false, error: 'Support request not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        data: supportRequest
      });
    }

    // Get statistics (admin only - would need auth check in real app)
    const stats = await (SupportRequest as any).getStats();
    
    return NextResponse.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('Support request retrieval error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

function getSLAResponseTime(priority: string): string {
  const slaMap = {
    urgent: '4 hours',
    high: '24 hours',
    medium: '48 hours',
    low: '72 hours'
  };
  return slaMap[priority as keyof typeof slaMap] || '48 hours';
}

// Handle preflight requests for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
