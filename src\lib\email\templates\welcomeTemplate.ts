import { EmailTemplate } from './baseTemplate';

export interface WelcomeEmailData {
  firstName: string;
  email: string;
  role: string;
  accountId: string;
  dashboardUrl?: string;
}

export function createWelcomeEmailTemplate(data: WelcomeEmailData): { html: string; text: string } {
  const { firstName, email, role, accountId, dashboardUrl = `${process.env.NEXT_PUBLIC_APP_URL}/dashboard` } = data;
  
  const roleDisplayName = role.charAt(0).toUpperCase() + role.slice(1);
  
  const content = `
    <h2 style="color: #1E4841; margin-bottom: 20px;">Hello ${firstName}!</h2>
    
    <p style="margin-bottom: 20px;">
      Welcome to the Whistleblower System! Your account has been successfully created and you're now part of our secure reporting platform.
    </p>
    
    ${EmailTemplate.createCard(`
      <strong>Account Details:</strong><br>
      <strong>Email:</strong> ${email}<br>
      <strong>Role:</strong> ${roleDisplayName}<br>
      <strong>Account ID:</strong> ${accountId}
    `, 'Your Account Information')}
    
    <h3 style="color: #1E4841; margin: 30px 0 15px 0;">What's Next?</h3>
    <ul style="padding-left: 20px; margin-bottom: 30px;">
      <li>Complete your profile setup</li>
      <li>Familiarize yourself with our security features</li>
      <li>Review our privacy policy and terms of service</li>
      ${role === 'whistleblower' ? '<li>Learn how to submit secure reports</li>' : '<li>Set up your dashboard preferences</li>'}
    </ul>
    
    ${EmailTemplate.createButton('Access Your Dashboard', dashboardUrl)}
    
    ${EmailTemplate.createAlert(`
      Your account is protected with enterprise-grade security. All communications and reports are encrypted and stored securely.
    `, 'info')}
    
    <p style="margin-top: 30px;">
      If you have any questions or need assistance getting started, please don't hesitate to contact our support team.
    </p>
  `;

  const html = EmailTemplate.createBaseTemplate(content, {
    title: 'Welcome to Whistleblower System',
    preheader: `Welcome ${firstName}! Your secure account has been created successfully.`,
    headerIcon: '🎉',
    recipientEmail: email
  });

  const text = `
Welcome to Whistleblower System!

Hello ${firstName},

Your account has been successfully created and you're now part of our secure reporting platform.

Account Details:
- Email: ${email}
- Role: ${roleDisplayName}
- Account ID: ${accountId}

What's Next?
- Complete your profile setup
- Familiarize yourself with our security features
- Review our privacy policy and terms of service
${role === 'whistleblower' ? '- Learn how to submit secure reports' : '- Set up your dashboard preferences'}

Access your dashboard: ${dashboardUrl}

Your account is protected with enterprise-grade security. All communications and reports are encrypted and stored securely.

If you have any questions or need assistance getting started, please don't hesitate to contact our support team.

Best regards,
Whistleblower System Team
  `.trim();

  return { html, text };
}
