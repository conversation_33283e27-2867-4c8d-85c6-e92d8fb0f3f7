"use client";
import { memo, useState, useRef, useCallback, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { ChevronDown } from "lucide-react";
import { NavItem, ProductItem } from "@/lib/types";
import NavContent from "./NavContent";

interface NavDropdownProps {
    title: string;
    items: NavItem[] | ProductItem[];
    width: string;
    isActive: boolean;
}

const NavDropdown = memo(({ title, items, width, isActive }: NavDropdownProps) => {
    const [isOpen, setIsOpen] = useState(false);
    const timeoutRef = useRef<NodeJS.Timeout | null>(null);

    const handleMouseEnter = useCallback(() => {
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
            timeoutRef.current = null;
        }
        setIsOpen(true);
    }, []);

    const handleMouseLeave = useCallback(() => {
        timeoutRef.current = setTimeout(() => {
            setIsOpen(false);
        }, 100);
    }, []);

    useEffect(() => {
        return () => {
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
        };
    }, []);

    return (
        <div
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
            className="relative"
        >
            <Button
                variant="ghost"
                className="hover:bg-[#1E4841] hover:text-white px-3 py-5 transition-all duration-300 ease-in-out group text-[#6B7271] flex items-center"
            >
                <span className={isActive ? 'font-bold text-base' : 'text-base font-light'}>{title}</span>
                <ChevronDown className={`ml-1 transition-transform duration-300 ease-in-out ${isOpen ? 'rotate-180' : ''}`} size={16} />
            </Button>

            <div
                className={`absolute top-full left-0 mt-3 ${width} bg-white rounded-lg shadow-lg p-0 z-50
                    transition-all duration-300 ease-in-out origin-top
                    ${isOpen
                        ? 'opacity-100 scale-100 translate-y-0'
                        : 'opacity-0 scale-95 -translate-y-2 pointer-events-none'
                    }`}
            >
                <div className={`absolute -top-2 left-8 w-0 h-0 border-l-[8px] border-r-[8px] border-b-[8px] border-l-transparent border-r-transparent border-b-white
                    transition-all duration-300 ease-in-out
                    ${isOpen ? 'opacity-100' : 'opacity-0'}`}
                />
                <NavContent items={items} />
            </div>
        </div>
    );
});

NavDropdown.displayName = 'NavDropdown';

export default NavDropdown;