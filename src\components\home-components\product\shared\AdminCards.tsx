import React from "react";
import Image from "next/image";
import { Card, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  ProductResponsibility,
  ProductUser,
  ProductCase,
  ProductInsight,
  ProductSecurity,
  ProductTestimonial
} from "@/lib/types";

// Responsibility Card Component
export const ResponsibilityCard: React.FC<ProductResponsibility> = ({ icon, alt, width, height, title, description }) => (
  <Card className="w-full max-w-4xl flex p-2 border-none hover:border shadow-md hover:shadow-lg transition-shadow duration-300 gap-0">
    <CardHeader className="flex flex-col items-start justify-between gap-2 p-2">
      <div className="w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 rounded-full bg-[#ECF4E9] flex items-center justify-center">
        <Image src={icon} alt={alt} width={width} height={height} />
      </div>
      <CardTitle className="font-semibold text-left text-lg sm:text-xl xl:pr-2 text-[#242E2C]">{title}</CardTitle>
      <CardDescription className="text-sm sm:text-base text-left font-normal text-[#6B7271] mb-6">{description}</CardDescription>
    </CardHeader>
  </Card>
);

// User Card Component
export const UserCard: React.FC<ProductUser> = ({ icon: Icon, title, description }) => (
  <Card className="w-full max-w-4xl flex p-2 shadow-none border-none hover:border hover:shadow-md transition-shadow duration-300 gap-0">
    <CardHeader className="flex items-start gap-2 p-2">
      <div className="w-7 h-7 sm:w-8 sm:h-8 md:w-10 md:h-10 rounded-full bg-[#ECF4E9] p-2 sm:p-3 flex items-center justify-center">
        <Icon className="w-3 h-3 sm:w-4 sm:h-4" />
      </div>
      <div className="flex flex-col gap-1.5 sm:gap-2 mt-1 sm:mt-1.5">
        <CardTitle className="font-semibold text-left text-lg sm:text-xl text-[#242E2C]">{title}</CardTitle>
        <CardDescription className="text-sm sm:text-base text-left font-normal text-[#6B7271]">{description}</CardDescription>
      </div>
    </CardHeader>
  </Card>
);

// Case Card Component
export const CaseCard: React.FC<ProductCase> = ({ icon: Icon, title, description }) => (
  <Card className="w-full max-w-4xl flex p-2 shadow-none border-none hover:border hover:shadow-md transition-shadow duration-300 gap-0">
    <CardHeader className="flex items-start gap-2 p-2">
      <div className="w-7 h-7 sm:w-8 sm:h-8 md:w-10 md:h-10 rounded-full bg-[#ECF4E9] p-2 sm:p-3 flex items-center justify-center">
        <Icon className="w-3 h-3 sm:w-4 sm:h-4" />
      </div>
      <div className="flex flex-col gap-1.5 sm:gap-2 mt-1 sm:mt-1.5">
        <CardTitle className="font-semibold text-left text-lg sm:text-xl text-[#242E2C]">{title}</CardTitle>
        <CardDescription className="text-sm sm:text-base text-left font-normal text-[#6B7271]">{description}</CardDescription>
      </div>
    </CardHeader>
  </Card>
);

// Insight Card Component
export const InsightCard: React.FC<ProductInsight> = ({ icon: Icon, title, description }) => (
  <Card className="w-full max-w-4xl flex p-2 shadow-none border-none hover:border hover:shadow-md transition-shadow duration-300 gap-0">
    <CardHeader className="flex items-start gap-2 p-2">
      <div className="w-7 h-7 sm:w-8 sm:h-8 md:w-10 md:h-10 rounded-full bg-[#ECF4E9] p-2 sm:p-3 flex items-center justify-center">
        <Icon className="w-3 h-3 sm:w-4 sm:h-4" />
      </div>
      <div className="flex flex-col gap-1.5 sm:gap-2 mt-1 sm:mt-1.5">
        <CardTitle className="font-semibold text-left text-lg sm:text-xl text-[#242E2C]">{title}</CardTitle>
        <CardDescription className="text-sm sm:text-base text-left font-normal text-[#6B7271]">{description}</CardDescription>
      </div>
    </CardHeader>
  </Card>
);

// Admin Security Card Component
export const AdminSecurityCard: React.FC<ProductSecurity> = ({ icon: Icon, alt, width, height, title, description }) => (
  <Card className="w-full max-w-4xl bg-transparent flex p-3 sm:p-4 md:p-5 border-none rounded-2xl shadow-none hover:shadow-lg transition-shadow duration-300 gap-0">
    <CardHeader className="flex items-start justify-between gap-6 p-1 sm:p-2">
      <div className="w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 min-h-12 min-w-12 rounded-lg bg-[#ECF4E9] mb-1 sm:mb-2 flex items-center justify-center">
        {typeof Icon === 'string' ? (
          <Image src={Icon} alt={alt || title} width={width || 24} height={height || 24} className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6" />
        ) : (
          <Icon className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6" />
        )}
      </div>
      <div className="flex flex-col gap-1.5 sm:gap-2">
        <CardTitle className="font-semibold text-left text-base sm:text-lg text-white">{title}</CardTitle>
        <CardDescription className="text-sm sm:text-base text-left font-normal text-[#D1D5DB]">{description}</CardDescription>
      </div>
    </CardHeader>
  </Card>
);

// Admin Testimonial Card Component
export const AdminTestimonialCard: React.FC<ProductTestimonial> = ({ quote, role, name, avatar }) => (
  <div className="flex gap-8">
    <Avatar className="h-6 w-6 sm:h-7 sm:w-7 md:h-22 md:w-22 rounded-full">
      <AvatarImage src={avatar} alt={`Testimonial by ${role}${name ? `, ${name}` : ''}`} />
      <AvatarFallback>User</AvatarFallback>
    </Avatar>
    <div className="flex flex-col items-start gap-3 sm:gap-4">
      <Image
        src="/desktop/products/admin/icons/quotes.svg"
        alt="Quotes"
        width={37}
        height={28}
        className="h-4 w-4 sm:h-5 sm:w-5 md:h-8 md:w-8 mb-2 text-[#1A2B4E] opacity-50"
      />
      <p className="mb-4 text-sm sm:text-lg md:text-xl leading-relaxed font-normal text-[#374151] italic">{quote}</p>
      <div className="flex flex-col">
        {name && <CardTitle className="font-semibold text-base sm:text-lg">{name}</CardTitle>}
        <CardDescription className="text-sm sm:text-base font-normal">{role}</CardDescription>
      </div>
    </div>
  </div>
);