import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/nextauth.config';
import logger from '@/lib/utils/logger';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions) as { user?: { id: string; email: string; role: string; provider: string }; customToken?: string } | null;
    
    if (!session || !session.user) {
      logger.error('OAuth callback: No session found');
      return NextResponse.redirect(new URL('/login?error=no-session', request.url));
    }

    const { user } = session;
    const userRole = user?.role;

    // Determine redirect URL based on user role
    let redirectUrl = '/dashboard';
    
    if (userRole === 'admin') {
      redirectUrl = '/dashboard/admin';
    } else if (userRole === 'whistleblower' || userRole === 'investigator') {
      redirectUrl = '/dashboard/whistleblower';
    }

    logger.info('OAuth callback successful', {
      userId: user.id,
      email: user.email,
      role: userRole,
      provider: user.provider,
      redirectUrl
    });

    // Store the custom token in a cookie for API authentication
    const response = NextResponse.redirect(new URL(redirectUrl, request.url));
    
    if (session.customToken) {
      response.cookies.set('auth_token', session.customToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 30 * 24 * 60 * 60 // 30 days
      });
    }

    return response;

  } catch (error) {
    logger.error('OAuth callback error:', error);
    return NextResponse.redirect(new URL('/login?error=callback-error', request.url));
  }
}
