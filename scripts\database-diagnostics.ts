import { config } from 'dotenv';
import mongoose from 'mongoose';

// Load environment variables
config({ path: '.env.local' });

// Import database connection
import connectDB from '../src/lib/db/mongodb';

class DatabaseDiagnostics {
  
  async runFullDiagnostics(): Promise<void> {
    console.log('🔍 Running Full Database Diagnostics...');
    console.log('='.repeat(50));
    
    try {
      await connectDB();
      console.log('✅ Database connection successful');
      
      // 1. Check database info
      await this.checkDatabaseInfo();
      
      // 2. List all collections
      await this.listAllCollections();
      
      // 3. Check collection sizes
      await this.checkCollectionSizes();
      
      // 4. Check for indexes
      await this.checkIndexes();
      
      // 5. Check for schema validation errors
      await this.checkSchemaValidation();
      
    } catch (error) {
      console.error('❌ Database diagnostics failed:', error);
    }
  }
  
  async checkDatabaseInfo(): Promise<void> {
    console.log('\n📊 Database Information:');
    console.log('-'.repeat(30));

    const db = mongoose.connection.db;
    if (!db) {
      console.error('❌ Database connection not available');
      return;
    }

    try {
      const dbStats = await db.stats();
      console.log(`Database Name: ${mongoose.connection.name}`);
      console.log(`Host: ${mongoose.connection.host}`);
      console.log(`Collections: ${dbStats.collections}`);
      console.log(`Data Size: ${(dbStats.dataSize / 1024 / 1024).toFixed(2)} MB`);
      console.log(`Storage Size: ${(dbStats.storageSize / 1024 / 1024).toFixed(2)} MB`);
      console.log(`Indexes: ${dbStats.indexes}`);
    } catch (error: any) {
      console.error('❌ Could not get database stats:', error.message);
    }
  }
  
  async listAllCollections(): Promise<void> {
    console.log('\n📁 All Collections:');
    console.log('-'.repeat(30));

    const db = mongoose.connection.db;
    if (!db) {
      console.error('❌ Database connection not available');
      return;
    }

    try {
      const collections = await db.listCollections().toArray();

      if (collections.length === 0) {
        console.log('No collections found');
        return;
      }

      collections.forEach((collection, index) => {
        console.log(`${index + 1}. ${collection.name}`);
      });
    } catch (error: any) {
      console.error('❌ Could not list collections:', error.message);
    }
  }
  
  async checkCollectionSizes(): Promise<void> {
    console.log('\n📈 Collection Document Counts:');
    console.log('-'.repeat(30));

    const db = mongoose.connection.db;
    if (!db) {
      console.error('❌ Database connection not available');
      return;
    }

    const expectedCollections = [
      'companies', 'users', 'reports', 'conversations',
      'messages', 'notifications', 'blogs', 'pricingplans',
      'escalations', 'auditlogs', 'systemsettings', 'systemstatuses'
    ];

    for (const collectionName of expectedCollections) {
      try {
        const count = await db.collection(collectionName).countDocuments();
        console.log(`${collectionName}: ${count} documents`);
      } catch (error) {
        console.log(`${collectionName}: Collection not found or error`);
      }
    }

    // Check for unexpected collections
    try {
      const allCollections = await db.listCollections().toArray();
      const unexpectedCollections = allCollections
        .map(c => c.name)
        .filter(name => !expectedCollections.includes(name) && !name.startsWith('system.'));

      if (unexpectedCollections.length > 0) {
        console.log('\n⚠️ Unexpected collections found:');
        unexpectedCollections.forEach(name => console.log(`- ${name}`));
      }
    } catch (error) {
      console.error('❌ Could not check for unexpected collections');
    }
  }
  
  async checkIndexes(): Promise<void> {
    console.log('\n🔍 Index Information:');
    console.log('-'.repeat(30));

    const db = mongoose.connection.db;
    if (!db) {
      console.error('❌ Database connection not available');
      return;
    }

    const collections = ['companies', 'users', 'reports'];

    for (const collectionName of collections) {
      try {
        const indexes = await db.collection(collectionName).indexes();
        console.log(`\n${collectionName} indexes:`);
        indexes.forEach(index => {
          console.log(`  - ${index.name}: ${JSON.stringify(index.key)}`);
        });
      } catch (error) {
        console.log(`${collectionName}: Could not get indexes`);
      }
    }
  }
  
  async checkSchemaValidation(): Promise<void> {
    console.log('\n✅ Schema Validation Check:');
    console.log('-'.repeat(30));

    const db = mongoose.connection.db;
    if (!db) {
      console.error('❌ Database connection not available');
      return;
    }

    // Try to create a test document in each collection to check for validation errors
    const testCollections = [
      { name: 'companies', testDoc: { name: 'Test Company', isActive: true } },
      { name: 'users', testDoc: { email: '<EMAIL>', firstName: 'Test', lastName: 'User', role: 'admin' } }
    ];

    for (const { name, testDoc } of testCollections) {
      try {
        // Try to validate without actually inserting
        const collection = db.collection(name);
        await collection.insertOne({ ...testDoc, _testDocument: true });
        await collection.deleteOne({ _testDocument: true });
        console.log(`✅ ${name}: Schema validation passed`);
      } catch (error: any) {
        console.log(`❌ ${name}: Schema validation failed - ${error.message}`);
      }
    }
  }
  
  async forceCleanDatabase(): Promise<void> {
    console.log('\n🧹 Force Cleaning Database...');
    console.log('-'.repeat(30));

    try {
      await connectDB();

      const db = mongoose.connection.db;
      if (!db) {
        console.error('❌ Database connection not available');
        return;
      }

      // Get all collections
      const collections = await db.listCollections().toArray();

      console.log(`Found ${collections.length} collections to clean`);

      // Drop all collections (more thorough than deleteMany)
      for (const collection of collections) {
        if (!collection.name.startsWith('system.')) {
          try {
            await db.collection(collection.name).drop();
            console.log(`✅ Dropped collection: ${collection.name}`);
          } catch (error: any) {
            console.log(`⚠️ Could not drop ${collection.name}: ${error.message}`);
          }
        }
      }

      console.log('🎉 Force clean completed!');

    } catch (error) {
      console.error('❌ Force clean failed:', error);
    }
  }
}

// Main execution
async function main() {
  const command = process.argv[2];
  const diagnostics = new DatabaseDiagnostics();
  
  try {
    switch (command) {
      case 'diagnose':
        await diagnostics.runFullDiagnostics();
        break;
      case 'force-clean':
        await diagnostics.forceCleanDatabase();
        break;
      default:
        console.log('Available commands:');
        console.log('  pnpm db:diagnose     - Run full database diagnostics');
        console.log('  pnpm db:force-clean  - Force clean all collections');
    }
  } catch (error) {
    console.error('❌ Operation failed:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    process.exit(0);
  }
}

main();
