import { NextResponse } from 'next/server';
import { DataService } from '@/lib/db/dataService';
import { withCompanyIsolation, AuthenticatedRequest } from '@/lib/middleware/auth';
import connectDB from '@/lib/db/mongodb';

export const runtime = 'nodejs';

export const GET = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    if (!request.user) {
      return NextResponse.json(
        { success: false, error: 'User not authenticated' },
        { status: 401 }
      );
    }

    // For admin users, get company-wide stats; for whistleblowers, get user-specific stats
    const userId = request.user.role === 'whistleblower' ? request.user.id : undefined;
    const companyId = request.user.companyId;
    
    const stats = await DataService.getDashboardStats(userId, companyId);

    return NextResponse.json({
      success: true,
      data: stats
    });
  } catch {
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});