import { NextRequest, NextResponse } from 'next/server';
import { MFAService } from '@/lib/auth/mfa';

export async function POST(request: NextRequest) {
  try {
    const { userId, secret, token, backupCodes } = await request.json();
    
    if (!userId || !secret || !token || !backupCodes) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    const isValid = MFAService.verifyTOTP(secret, token);
    if (!isValid) {
      return NextResponse.json({ error: 'Invalid verification code' }, { status: 400 });
    }

    const success = await MFAService.enableMFA(userId, secret, backupCodes);
    
    return NextResponse.json({
      success,
      message: success ? 'MFA enabled successfully' : 'Failed to enable MFA'
    });
  } catch (error) {
    console.error('MFA enable error:', error);
    return NextResponse.json({ error: 'MFA enable failed' }, { status: 500 });
  }
}