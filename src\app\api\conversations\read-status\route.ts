import { NextResponse } from 'next/server';
import { withAuth } from '@/lib/middleware/auth';
import { AuthenticatedRequest } from '@/lib/middleware/auth';
import connectDB from '@/lib/db/mongodb';
import { Conversation } from '@/lib/db/models';
import mongoose from 'mongoose';

// POST endpoint to mark conversation as read
export const POST = withAuth(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    const { conversationId, lastMessageId } = await request.json();
    const userId = request.user.id;
    
    if (!conversationId) {
      return NextResponse.json(
        { success: false, error: 'Conversation ID is required' },
        { status: 400 }
      );
    }

    if (!mongoose.Types.ObjectId.isValid(conversationId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid conversation ID' },
        { status: 400 }
      );
    }

    // Verify user has access to this conversation
    const conversation = await Conversation.findOne({
      _id: conversationId,
      participants: userId
    });

    if (!conversation) {
      return NextResponse.json(
        { success: false, error: 'Conversation not found or access denied' },
        { status: 404 }
      );
    }

    // Update or create read status
    const readStatus = {
      userId: new mongoose.Types.ObjectId(userId),
      readAt: new Date(),
      lastMessageId: lastMessageId ? new mongoose.Types.ObjectId(lastMessageId) : undefined
    };

    // Update conversation with read status
    await Conversation.updateOne(
      { _id: conversationId },
      {
        $pull: { readBy: { userId: new mongoose.Types.ObjectId(userId) } },
      }
    );

    await Conversation.updateOne(
      { _id: conversationId },
      {
        $push: { readBy: readStatus }
      }
    );

    return NextResponse.json({
      success: true,
      message: 'Conversation marked as read'
    });

  } catch {
    return NextResponse.json(
      { success: false, error: 'Failed to mark conversation as read' },
      { status: 500 }
    );
  }
});

// GET endpoint to retrieve read statuses for a user
export const GET = withAuth(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    const userId = request.user.id;
    
    // Get all conversations for the user with read status
    const conversations = await Conversation.find({
      participants: userId
    }).select('_id readBy lastMessageAt');

    const readStatuses = conversations.map(conv => {
      const userReadStatus = conv.readBy?.find(
        (read: { userId: mongoose.Types.ObjectId; readAt: Date; lastMessageId?: mongoose.Types.ObjectId }) => read.userId.toString() === userId
      );

      return {
        conversationId: conv._id.toString(),
        userId,
        lastReadAt: userReadStatus?.readAt || null,
        lastMessageId: userReadStatus?.lastMessageId?.toString() || null,
        isUnread: userReadStatus ? 
          (conv.lastMessageAt && conv.lastMessageAt > userReadStatus.readAt) : 
          true
      };
    });

    return NextResponse.json({
      success: true,
      readStatuses
    });

  } catch {
    return NextResponse.json(
      { success: false, error: 'Failed to retrieve read statuses' },
      { status: 500 }
    );
  }
});
