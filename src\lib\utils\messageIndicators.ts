// Utility functions for message indicators across the application
// Removed mock data import - using database APIs

// Event system for real-time updates
type MessageUpdateListener = () => void;
const messageUpdateListeners: MessageUpdateListener[] = [];

/**
 * Subscribe to message count updates
 */
export const subscribeToMessageCountUpdates = (listener: MessageUpdateListener): (() => void) => {
    messageUpdateListeners.push(listener);

    // Return unsubscribe function
    return () => {
        const index = messageUpdateListeners.indexOf(listener);
        if (index > -1) {
            messageUpdateListeners.splice(index, 1);
        }
    };
};

/**
 * Notify all listeners of message count updates
 */
export const notifyMessageCountUpdate = (): void => {
    messageUpdateListeners.forEach(listener => listener());
};

/**
 * Get the unread message count for display in navigation
 * Note: This should be replaced with real API calls in components
 */
export const getUnreadMessageCount = (): number => {
    // Return 0 as default - components should use real API data
    return 0;
};

/**
 * Check if there are any unread messages
 * Note: This should be replaced with real API calls in components
 */
export const hasUnreadMessages = (): boolean => {
    // Return false as default - components should use real API data
    return false;
};

/**
 * Format unread count for display (e.g., "3" or "9+" for counts over 9)
 */
export const formatUnreadCount = (count: number): string => {
    if (count === 0) return "";
    if (count > 9) return "9+";
    return count.toString();
};

/**
 * Get message indicator props for navigation items
 * Note: This should be replaced with real API calls in components
 */
export const getMessageIndicatorProps = () => {
    const count = 0; // Default to 0 - components should use real API data
    return {
        hasUnread: count > 0,
        count,
        formattedCount: formatUnreadCount(count)
    };
};

/**
 * Message indicator component props interface
 */
export interface MessageIndicatorProps {
    count?: number;
    className?: string;
    size?: "sm" | "md" | "lg";
}

/**
 * Get CSS classes for message indicator based on size
 */
export const getMessageIndicatorClasses = (size: "sm" | "md" | "lg" = "md"): string => {
    const baseClasses = "bg-[#EF4444] text-white rounded-full flex items-center justify-center font-medium";
    
    switch (size) {
        case "sm":
            return `${baseClasses} w-4 h-4 text-xs`;
        case "md":
            return `${baseClasses} w-5 h-5 text-xs`;
        case "lg":
            return `${baseClasses} w-6 h-6 text-sm`;
        default:
            return `${baseClasses} w-5 h-5 text-xs`;
    }
};

/**
 * Message notification data for dashboard
 */
export interface MessageNotification {
    id: string;
    conversationId: string;
    senderName: string;
    preview: string;
    timestamp: string;
    isUnread: boolean;
    caseId: string;
}

/**
 * Get recent message notifications for dashboard
 */
export const getRecentMessageNotifications = (limit: number = 5): MessageNotification[] => {
    // This would typically fetch from an API or database
    // For now, we'll return mock data based on conversations
    const notifications = [
        {
            id: "msg_notif_001",
            conversationId: "conv_001",
            senderName: "Compliance Team",
            preview: "Thank you for your detailed report. We need additional information...",
            timestamp: "10:24 AM",
            isUnread: true,
            caseId: "Case #WB-2025-0428"
        },
        {
            id: "msg_notif_002",
            conversationId: "conv_003",
            senderName: "Sarah Martinez",
            preview: "I've reviewed your documentation and have some follow-up questions...",
            timestamp: "Yesterday",
            isUnread: false,
            caseId: "Case #WB-2025-0402"
        },
        {
            id: "msg_notif_003",
            conversationId: "conv_005",
            senderName: "System Administrator",
            preview: "Security update for the messaging system has been completed...",
            timestamp: "Apr 5",
            isUnread: false,
            caseId: "System Notice"
        }
    ];

    return notifications.slice(0, limit);
};

/**
 * Mark message as read (would typically update database)
 */
export const markMessageAsRead = async (messageId: string): Promise<boolean> => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // In a real implementation, this would update the database
    console.log(`Marking message ${messageId} as read`);
    
    // Notify listeners of the update
    notifyMessageCountUpdate();
    
    return true;
};

/**
 * Mark conversation as read (would typically update database)
 */
export const markConversationAsRead = async (conversationId: string): Promise<boolean> => {
    try {
        // This should use the real API call
        // For now, just simulate success and notify listeners
        console.log(`Conversation ${conversationId} marked as read`);

        // Notify listeners of the update
        notifyMessageCountUpdate();
        return true;
    } catch (error) {
        console.error('Error marking conversation as read:', error);
        return false;
    }
};

/**
 * Get message status for a specific conversation
 */
export const getConversationStatus = (conversationId: string) => {
    // This would typically check the database for the conversation status
    // TODO: Implement actual database lookup using conversationId
    console.log('Getting status for conversation:', conversationId); // Temporary usage to avoid unused variable warning
    return {
        hasUnreadMessages: true,
        lastMessageTime: "10:24 AM",
        isOnline: true,
        isTyping: false
    };
};

/**
 * Subscribe to message updates (would typically use WebSocket or SSE)
 */
export const subscribeToMessageUpdates = (callback: (update: { type: string; conversationId: string; timestamp: string }) => void) => {
    // In a real implementation, this would establish a WebSocket connection
    // For now, we'll simulate with a timer
    const interval = setInterval(() => {
        // Simulate random message updates
        if (Math.random() > 0.8) {
            callback({
                type: 'new_message',
                conversationId: 'conv_001',
                timestamp: new Date().toISOString()
            });
        }
    }, 30000); // Check every 30 seconds

    // Return cleanup function
    return () => clearInterval(interval);
};

/**
 * Unsubscribe from message updates
 */
export const unsubscribeFromMessageUpdates = (subscription: () => void) => {
    subscription();
};
