"use client";

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { ArrowLeft } from 'lucide-react';
import AnonymousReportTest from '@/components/anonymous-report/AnonymousReportTest';

export default function AnonymousTestPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="flex items-center mr-4">
                <Image
                  src="/logo.svg"
                  alt="7IRIS Logo"
                  width={83}
                  height={37}
                  className="h-8 w-auto hover:scale-105 transition-transform duration-300"
                  priority
                />
              </Link>
              <div className="flex items-center">
                <div>
                  <h1 className="text-lg font-semibold text-gray-900">Anonymous Reporting Test</h1>
                  <p className="text-xs text-gray-600">Test System Functionality</p>
                </div>
              </div>
            </div>
            <Link 
              href="/dashboard/anonymous" 
              className="text-sm text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Link>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8 text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Test Anonymous Reporting System
          </h2>
          <p className="text-gray-600">
            Use this form to test the anonymous reporting functionality. This will create a real report in the system.
          </p>
        </div>

        <AnonymousReportTest />
      </div>
    </div>
  );
}
