import Header from "@/components/home-components/shared/Header";
import Footer from "@/components/home-components/shared/Footer";
import { But<PERSON> } from "@/components/ui/button";
import Image from "next/image";

export default function AboutPage() {
  return (
    <div className="flex flex-col min-h-screen bg-[#F6FAF5]">
      <Header />
      <main id="main-content" className="flex-1 pt-20">
        {/* Hero Section */}
        <section className="relative bg-gradient-to-br from-[#1E4841] to-[#1E4841]/90 min-h-[420px] flex flex-col justify-center items-center px-4 py-20 text-white overflow-hidden">
          <div className="absolute inset-0 z-0">
            <Image src="/desktop/about/hero-bg.png" alt="Background" fill className="object-cover opacity-20" priority sizes="100vw" />
          </div>
          <div className="relative z-10 flex flex-col md:flex-row items-center gap-8 w-full max-w-6xl">
            <div className="flex-1 text-center md:text-left">
              <h1 className="text-4xl md:text-5xl font-bold mb-4">Integrity Through Insight</h1>
              <p className="text-lg md:text-xl mb-6 max-w-xl mx-auto md:mx-0">Empowering organizations to uncover the truth with clarity, confidentiality, and accountability.</p>
              <Button className="bg-[#BBF49C] text-[#1E4841] hover:bg-lime-200 font-semibold px-8 py-4 text-base">Learn More</Button>
            </div>
            <div className="flex-1 flex justify-center md:justify-end">
              <div className="bg-white/90 rounded-2xl shadow-lg p-8 max-w-sm w-full text-[#1E4841]">
                <div className="flex items-center mb-3">
                  <div className="bg-[#E6F9D7] rounded-full p-2 mr-3">
                    <Image src="/desktop/about/secure-icon.svg" alt="Secure" width={24} height={24} />
                  </div>
                  <span className="font-bold text-lg">Secure Whistleblowing</span>
                </div>
                <p className="text-sm">Our platform ensures complete anonymity and protection for whistleblowers, while providing organizations with the tools they need to address issues effectively.</p>
              </div>
            </div>
          </div>
          {/* Dots navigation (visual only) */}
          <div className="absolute right-8 top-1/2 flex flex-col gap-2 z-10">
            <span className="w-2 h-2 rounded-full bg-[#BBF49C] block" />
            <span className="w-2 h-2 rounded-full bg-white/40 block" />
            <span className="w-2 h-2 rounded-full bg-white/40 block" />
          </div>
        </section>

        {/* Timeline Section */}
        <section className="bg-white py-20 px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-2xl md:text-3xl font-bold text-center text-[#1E4841] mb-16">Our Founding Story</h2>
            <div className="relative border-l-2 border-[#BBF49C] ml-6">
              {/* Timeline Item 1 */}
              <div className="mb-16 flex flex-col md:flex-row items-center md:items-start">
                <div className="w-6 h-6 bg-[#F6FAF5] border-2 border-[#BBF49C] rounded-full flex items-center justify-center absolute -left-3 top-0">
                  <Image src="/desktop/about/inception.svg" alt="Inception" width={20} height={20} />
                </div>
                <div className="ml-10 bg-white rounded-xl shadow-md p-6 w-full md:w-1/2">
                  <h3 className="font-semibold text-lg mb-2">The Inception</h3>
                  <p className="text-sm text-[#374151]">7IRIS was founded in response to the rising global demand for transparent and secure internal reporting systems. What began as a conversation between industry experts quickly evolved into a mission to transform corporate accountability.</p>
                </div>
              </div>
              {/* Timeline Item 2 */}
              <div className="mb-16 flex flex-col md:flex-row-reverse items-center md:items-start">
                <div className="w-6 h-6 bg-[#F6FAF5] border-2 border-[#BBF49C] rounded-full flex items-center justify-center absolute -left-3 top-0 md:left-auto md:-right-3">
                  <Image src="/desktop/about/collaboration.svg" alt="Collaboration" width={20} height={20} />
                </div>
                <div className="ml-10 md:ml-0 md:mr-10 bg-white rounded-xl shadow-md p-6 w-full md:w-1/2">
                  <h3 className="font-semibold text-lg mb-2">The Collaboration</h3>
                  <p className="text-sm text-[#374151]">Our founding team brought together legal experts, compliance officers, and ethical AI designers. This unique collaboration emerged after a major whistleblowing incident exposed systemic flaws in existing reporting structures.</p>
                </div>
              </div>
              {/* Timeline Item 3 */}
              <div className="flex flex-col md:flex-row items-center md:items-start">
                <div className="w-6 h-6 bg-[#F6FAF5] border-2 border-[#BBF49C] rounded-full flex items-center justify-center absolute -left-3 top-0">
                  <Image src="/desktop/about/launch.svg" alt="Launch" width={20} height={20} />
                </div>
                <div className="ml-10 bg-white rounded-xl shadow-md p-6 w-full md:w-1/2">
                  <h3 className="font-semibold text-lg mb-2">The Launch</h3>
                  <p className="text-sm text-[#374151]">In 2025, we launched the first version of 7IRIS with a commitment to balancing technological innovation with human ethics. Today, we serve organizations worldwide, helping them build cultures of integrity and accountability.</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Mission & Vision Section */}
        <section className="relative bg-[#1E4841] py-20 px-4 flex flex-col md:flex-row gap-8 justify-center items-center">
          <div className="max-w-md w-full bg-[#153D32] rounded-2xl shadow-lg p-8 text-white flex flex-col gap-4">
            <h3 className="text-xl font-bold mb-2">Our Mission</h3>
            <p className="text-base mb-4">To protect the voice of every whistleblower and streamline internal case handling through trust-driven digital investigation.</p>
            <div className="flex gap-3 mt-2">
              <Image src="/desktop/about/mission-icon1.svg" alt="Mission Icon 1" width={28} height={28} />
              <Image src="/desktop/about/mission-icon2.svg" alt="Mission Icon 2" width={28} height={28} />
              <Image src="/desktop/about/mission-icon3.svg" alt="Mission Icon 3" width={28} height={28} />
            </div>
          </div>
          <div className="max-w-md w-full bg-white rounded-2xl shadow-lg p-8 text-[#1E4841] flex flex-col gap-4">
            <h3 className="text-xl font-bold mb-2">Our Vision</h3>
            <p className="text-base mb-4">A world where corporate integrity is non-negotiable, and every voice matters.</p>
            <Button className="bg-[#1E4841] text-white hover:bg-[#153D32] font-semibold px-6 py-3 w-fit self-start">Join Our Mission</Button>
          </div>
        </section>

        {/* Core Values Section */}
        <section className="bg-[#F6FAF5] py-20 px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-2xl md:text-3xl font-bold text-center text-[#1E4841] mb-2">Our Core Values</h2>
            <p className="text-center text-[#4B5563] mb-12">These principles guide every decision we make and every feature we build.</p>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
              {/* Value 1 */}
              <div className="bg-white rounded-2xl shadow p-6 flex flex-col gap-2">
                <div className="mb-2"><Image src="/desktop/about/values-transparency.svg" alt="Transparency" width={32} height={32} /></div>
                <h3 className="font-semibold text-lg mb-1">Transparency</h3>
                <p className="text-sm text-[#374151]">We believe in clear, open communication throughout the investigation process, while maintaining appropriate confidentiality where needed.</p>
              </div>
              {/* Value 2 */}
              <div className="bg-white rounded-2xl shadow p-6 flex flex-col gap-2">
                <div className="mb-2"><Image src="/desktop/about/values-confidentiality.svg" alt="Confidentiality" width={32} height={32} /></div>
                <h3 className="font-semibold text-lg mb-1">Confidentiality</h3>
                <p className="text-sm text-[#374151]">We protect whistleblower identities with military-grade security and ensure sensitive information remains secure throughout the investigation.</p>
              </div>
              {/* Value 3 */}
              <div className="bg-white rounded-2xl shadow p-6 flex flex-col gap-2">
                <div className="mb-2"><Image src="/desktop/about/values-accountability.svg" alt="Accountability" width={32} height={32} /></div>
                <h3 className="font-semibold text-lg mb-1">Accountability</h3>
                <p className="text-sm text-[#374151]">We help organizations take responsibility for addressing issues and implementing meaningful changes based on investigation findings.</p>
              </div>
              {/* Value 4 */}
              <div className="bg-white rounded-2xl shadow p-6 flex flex-col gap-2">
                <div className="mb-2"><Image src="/desktop/about/values-fair.svg" alt="Fair Resolution" width={32} height={32} /></div>
                <h3 className="font-semibold text-lg mb-1">Fair Resolution</h3>
                <p className="text-sm text-[#374151]">We design our systems to ensure impartial, evidence-based investigations that lead to just and equitable outcomes for all parties involved.</p>
              </div>
              {/* Value 5 */}
              <div className="bg-white rounded-2xl shadow p-6 flex flex-col gap-2">
                <div className="mb-2"><Image src="/desktop/about/values-human.svg" alt="Human-Centered Technology" width={32} height={32} /></div>
                <h3 className="font-semibold text-lg mb-1">Human-Centered Technology</h3>
                <p className="text-sm text-[#374151]">We create tools that enhance human judgment rather than replace it, keeping empathy and ethical considerations at the core of our design.</p>
              </div>
              {/* Value 6 (CTA) */}
              <div className="bg-white rounded-2xl shadow p-6 flex flex-col gap-2 items-start justify-between">
                <p className="text-sm text-[#374151] mb-4">Our values aren&apos;t just words on a page—they&apos;re built into every aspect of our platform.</p>
                <Button className="bg-[#BBF49C] text-[#1E4841] hover:bg-lime-200 font-semibold px-6 py-3 text-base">See How We Work</Button>
              </div>
            </div>
          </div>
        </section>

        {/* Our Approach Section */}
        <section className="bg-[#1E4841] py-20 px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-2xl md:text-3xl font-bold text-center text-[#1E4841] mb-2">Our Approach</h2>
            <p className="text-center text-[#BBF49C] mb-12">We balance AI, legal frameworks, and human judgment in resolving cases.</p>
            {/* Approach Cards */}
            <div className="flex flex-col gap-8">
              {/* Card 1 */}
              <div className="bg-white rounded-2xl shadow-lg flex flex-col md:flex-row overflow-hidden">
                <div className="md:w-1/2 flex items-center justify-center bg-[#F6FAF5] p-8">
                  <Image src="/desktop/about/approach-ai.png" alt="AI Analysis" width={220} height={140} className="rounded-xl" />
                </div>
                <div className="md:w-1/2 p-8 flex flex-col justify-center">
                  <h3 className="font-semibold text-xl mb-2 text-[#1E4841]">Technology Enhances, Never Replaces</h3>
                  <p className="text-[#374151] mb-4">We believe that technology should enhance, not replace, human ethics. 7IRIS offers structured workflows, secure communications, and audit-ready case trials—without compromising empathy.</p>
                  <div className="flex flex-wrap gap-2">
                    <span className="bg-[#E6F9D7] text-[#1E4841] rounded-full px-3 py-1 text-xs font-medium">AI-Assisted Analysis</span>
                    <span className="bg-[#E6F9D7] text-[#1E4841] rounded-full px-3 py-1 text-xs font-medium">Human Oversight</span>
                    <span className="bg-[#E6F9D7] text-[#1E4841] rounded-full px-3 py-1 text-xs font-medium">Ethical Frameworks</span>
                  </div>
                </div>
              </div>
              {/* Card 2 */}
              <div className="bg-white rounded-2xl shadow-lg flex flex-col md:flex-row-reverse overflow-hidden">
                <div className="md:w-1/2 flex items-center justify-center bg-[#F6FAF5] p-8">
                  <Image src="/desktop/about/approach-security.png" alt="Security" width={220} height={140} className="rounded-xl" />
                </div>
                <div className="md:w-1/2 p-8 flex flex-col justify-center">
                  <h3 className="font-semibold text-xl mb-2 text-[#1E4841]">Security Without Compromise</h3>
                  <p className="text-[#374151] mb-4">Our platform employs end-to-end encryption, anonymous reporting channels, and secure data storage to ensure that sensitive information remains protected throughout the investigation process.</p>
                  <div className="flex flex-wrap gap-2">
                    <span className="bg-[#E6F9D7] text-[#1E4841] rounded-full px-3 py-1 text-xs font-medium">End-to-End Encryption</span>
                    <span className="bg-[#E6F9D7] text-[#1E4841] rounded-full px-3 py-1 text-xs font-medium">Anonymous Reporting</span>
                    <span className="bg-[#E6F9D7] text-[#1E4841] rounded-full px-3 py-1 text-xs font-medium">Secure Storage</span>
                  </div>
                </div>
              </div>
              {/* Card 3 */}
              <div className="bg-white rounded-2xl shadow-lg flex flex-col md:flex-row overflow-hidden">
                <div className="md:w-1/2 flex items-center justify-center bg-[#F6FAF5] p-8">
                  <Image src="/desktop/about/approach-case.png" alt="Case Management" width={220} height={140} className="rounded-xl" />
                </div>
                <div className="md:w-1/2 p-8 flex flex-col justify-center">
                  <h3 className="font-semibold text-xl mb-2 text-[#1E4841]">Structured Yet Flexible</h3>
                  <p className="text-[#374151] mb-4">Our platform provides clear, structured workflows that guide investigations while remaining adaptable to the unique needs of each case and organization. This balance ensures thorough yet efficient resolution.</p>
                  <div className="flex flex-wrap gap-2">
                    <span className="bg-[#E6F9D7] text-[#1E4841] rounded-full px-3 py-1 text-xs font-medium">Customizable Workflows</span>
                    <span className="bg-[#E6F9D7] text-[#1E4841] rounded-full px-3 py-1 text-xs font-medium">Case Management</span>
                    <span className="bg-[#E6F9D7] text-[#1E4841] rounded-full px-3 py-1 text-xs font-medium">Audit Trials</span>
                  </div>
                </div>
              </div>
            </div>
            {/* Stats Row */}
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-6 mt-16">
              <div className="flex flex-col items-center">
                <span className="text-2xl md:text-3xl font-bold text-white">500+</span>
                <span className="text-[#BBF49C] text-sm mt-2 text-center">Organizations Served</span>
              </div>
              <div className="flex flex-col items-center">
                <span className="text-2xl md:text-3xl font-bold text-white">30+</span>
                <span className="text-[#BBF49C] text-sm mt-2 text-center">Countries Worldwide</span>
              </div>
              <div className="flex flex-col items-center">
                <span className="text-2xl md:text-3xl font-bold text-white">98%</span>
                <span className="text-[#BBF49C] text-sm mt-2 text-center">Client Retention Rate</span>
              </div>
              <div className="flex flex-col items-center">
                <span className="text-2xl md:text-3xl font-bold text-white">10,000+</span>
                <span className="text-[#BBF49C] text-sm mt-2 text-center">Cases Successfully Managed</span>
              </div>
            </div>
          </div>
        </section>

        {/* Leadership Team Section */}
        <section className="bg-[#F6FAF5] py-20 px-4">
          <div className="max-w-7xl mx-auto">
            <h2 className="text-2xl md:text-3xl font-bold text-center text-[#1E4841] mb-2">Meet Our Leadership Team</h2>
            <p className="text-center text-[#4B5563] mb-12">Our diverse team brings together expertise in compliance, technology, security, and customer success.</p>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
              {/* Member 1 */}
              <div className="bg-white rounded-2xl shadow flex flex-col overflow-hidden">
                <Image src="/desktop/about/team-michael.jpg" alt="Michael Thornton" width={400} height={260} className="w-full h-64 object-cover" />
                <div className="p-6 flex-1 flex flex-col justify-between">
                  <div>
                    <h3 className="font-semibold text-lg mb-1">Michael Thornton</h3>
                    <p className="text-sm text-[#374151] mb-1">CEO & Co-Founder</p>
                    <p className="text-xs text-[#6B7280] mb-4">Former Chief Compliance Officer with 15+ years of experience in financial services. Michael leads our strategic vision and growth initiatives.</p>
                  </div>
                  <a href="#" className="text-[#1E4841] text-xs font-semibold flex items-center gap-1 mt-auto">in <span className="underline">LinkedIn Profile</span></a>
                </div>
              </div>
              {/* Member 2 */}
              <div className="bg-white rounded-2xl shadow flex flex-col overflow-hidden">
                <Image src="/desktop/about/team-sophia.jpg" alt="Sophia Chen" width={400} height={260} className="w-full h-64 object-cover" />
                <div className="p-6 flex-1 flex flex-col justify-between">
                  <div>
                    <h3 className="font-semibold text-lg mb-1">Sophia Chen</h3>
                    <p className="text-sm text-[#374151] mb-1">CTO & Co-Founder</p>
                    <p className="text-xs text-[#6B7280] mb-4">Former security engineer at leading tech companies. Sophia oversees our technology development and security infrastructure.</p>
                  </div>
                  <a href="#" className="text-[#1E4841] text-xs font-semibold flex items-center gap-1 mt-auto">in <span className="underline">LinkedIn Profile</span></a>
                </div>
              </div>
              {/* Member 3 */}
              <div className="bg-white rounded-2xl shadow flex flex-col overflow-hidden">
                <Image src="/desktop/about/team-david.jpg" alt="David Okonkwo" width={400} height={260} className="w-full h-64 object-cover" />
                <div className="p-6 flex-1 flex flex-col justify-between">
                  <div>
                    <h3 className="font-semibold text-lg mb-1">David Okonkwo</h3>
                    <p className="text-sm text-[#374151] mb-1">Chief Operating Officer</p>
                    <p className="text-xs text-[#6B7280] mb-4">With 20+ years in operations and compliance, David ensures our platform meets the highest standards of reliability and compliance.</p>
                  </div>
                  <a href="#" className="text-[#1E4841] text-xs font-semibold flex items-center gap-1 mt-auto">in <span className="underline">LinkedIn Profile</span></a>
                </div>
              </div>
              {/* Member 4 */}
              <div className="bg-white rounded-2xl shadow flex flex-col overflow-hidden">
                <Image src="/desktop/about/team-alexandra.jpg" alt="Alexandra Reyes" width={400} height={260} className="w-full h-64 object-cover" />
                <div className="p-6 flex-1 flex flex-col justify-between">
                  <div>
                    <h3 className="font-semibold text-lg mb-1">Alexandra Reyes</h3>
                    <p className="text-sm text-[#374151] mb-1">Chief Security Officer</p>
                    <p className="text-xs text-[#6B7280] mb-4">Former cybersecurity consultant with expertise in data protection. Alexandra leads our security and compliance initiatives.</p>
                  </div>
                  <a href="#" className="text-[#1E4841] text-xs font-semibold flex items-center gap-1 mt-auto">in <span className="underline">LinkedIn Profile</span></a>
                </div>
              </div>
              {/* Member 5 */}
              <div className="bg-white rounded-2xl shadow flex flex-col overflow-hidden">
                <Image src="/desktop/about/team-jonathan.jpg" alt="Jonathan Park" width={400} height={260} className="w-full h-64 object-cover" />
                <div className="p-6 flex-1 flex flex-col justify-between">
                  <div>
                    <h3 className="font-semibold text-lg mb-1">Jonathan Park</h3>
                    <p className="text-sm text-[#374151] mb-1">Chief Compliance Officer</p>
                    <p className="text-xs text-[#6B7280] mb-4">With extensive experience in regulatory compliance, Jonathan ensures our platform meets global compliance standards.</p>
                  </div>
                  <a href="#" className="text-[#1E4841] text-xs font-semibold flex items-center gap-1 mt-auto">in <span className="underline">LinkedIn Profile</span></a>
                </div>
              </div>
              {/* Member 6 */}
              <div className="bg-white rounded-2xl shadow flex flex-col overflow-hidden">
                <Image src="/desktop/about/team-emma.jpg" alt="Emma Rodriguez" width={400} height={260} className="w-full h-64 object-cover" />
                <div className="p-6 flex-1 flex flex-col justify-between">
                  <div>
                    <h3 className="font-semibold text-lg mb-1">Emma Rodriguez</h3>
                    <p className="text-sm text-[#374151] mb-1">Customer Success Director</p>
                    <p className="text-xs text-[#6B7280] mb-4">Emma leads our customer success team, ensuring clients receive exceptional support and maximize platform value.</p>
                  </div>
                  <a href="#" className="text-[#1E4841] text-xs font-semibold flex items-center gap-1 mt-auto">in <span className="underline">LinkedIn Profile</span></a>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Client Testimonials Section */}
        <section className="bg-white py-20 px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-2xl md:text-3xl font-bold text-center text-[#1E4841] mb-2">What Our Clients Say</h2>
            <p className="text-center text-[#4B5563] mb-12">Hear from organizations that have transformed their whistleblowing programs with our platform.</p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* Testimonial 1 */}
              <div className="bg-[#F6FAF5] rounded-2xl shadow p-8 flex flex-col h-full">
                <p className="text-sm text-[#374151] mb-6">&ldquo;Implementing this whistleblowing platform has been transformative for our compliance program. The ease of use for reporters combined with powerful management tools has significantly improved our case resolution times.&rdquo;</p>
                <div className="flex items-center gap-3 mt-auto">
                  <Image src="/desktop/about/client-sarah.jpg" alt="Sarah Johnson" width={40} height={40} className="rounded-full" />
                  <div>
                    <span className="block font-semibold text-xs text-[#1E4841]">Sarah Johnson</span>
                    <span className="block text-xs text-[#6B7280]">Chief Ethics Officer, Global Corp</span>
                  </div>
                </div>
              </div>
              {/* Testimonial 2 */}
              <div className="bg-[#F6FAF5] rounded-2xl shadow p-8 flex flex-col h-full">
                <p className="text-sm text-[#374151] mb-6">&ldquo;The analytics and reporting capabilities have given us unprecedented visibility into our ethics program. We can now identify trends and address systemic issues before they become major problems.&rdquo;</p>
                <div className="flex items-center gap-3 mt-auto">
                  <Image src="/desktop/about/client-robert.jpg" alt="Robert Tanaka" width={40} height={40} className="rounded-full" />
                  <div>
                    <span className="block font-semibold text-xs text-[#1E4841]">Robert Tanaka</span>
                    <span className="block text-xs text-[#6B7280]">Compliance Director, Healthcare</span>
                  </div>
                </div>
              </div>
              {/* Testimonial 3 */}
              <div className="bg-[#F6FAF5] rounded-2xl shadow p-8 flex flex-col h-full">
                <p className="text-sm text-[#374151] mb-6">&ldquo;The security features give our whistleblowers confidence that their identities are protected. This has led to a significant increase in valuable reports that have helped us address issues early.&rdquo;</p>
                <div className="flex items-center gap-3 mt-auto">
                  <Image src="/desktop/about/client-maria.jpg" alt="Maria Gonzalez" width={40} height={40} className="rounded-full" />
                  <div>
                    <span className="block font-semibold text-xs text-[#1E4841]">Maria Gonzalez</span>
                    <span className="block text-xs text-[#6B7280]">Ethics Manager, European Group</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Founder Message Section */}
        <section className="bg-[#1E4841] py-20 px-4">
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-2xl shadow-lg flex flex-col md:flex-row overflow-hidden">
              <div className="md:w-1/3 flex flex-col items-center justify-center bg-[#153D32] p-8">
                <Image src="/desktop/about/founder.jpg" alt="Dr. Eleanor Richards" width={90} height={90} className="rounded-full mb-4" />
                <span className="text-white font-semibold text-lg mb-1">Dr. Eleanor Richards</span>
                <span className="text-[#BBF49C] text-xs mb-4">Founder & CEO</span>
                <div className="flex gap-3 mt-2">
                  <a href="#" aria-label="LinkedIn" className="bg-[#BBF49C] rounded-full p-2"><Image src="/desktop/about/icon-linkedin.svg" alt="LinkedIn" width={24} height={24} /></a>
                  <a href="#" aria-label="Twitter" className="bg-[#BBF49C] rounded-full p-2"><Image src="/desktop/about/icon-twitter.svg" alt="Twitter" width={24} height={24} /></a>
                </div>
              </div>
              <div className="md:w-2/3 p-8 flex flex-col justify-center">
                <span className="text-[#BBF49C] text-3xl mb-4">&ldquo;</span>
                <h3 className="font-semibold text-xl mb-4 text-[#1E4841]">A Message From Our Founder</h3>
                <p className="text-[#374151] mb-4 text-sm">When we started 7IRIS, we were driven by a simple yet powerful belief: that organizations thrive when they foster cultures of integrity and accountability. Having witnessed firsthand how inadequate reporting systems can fail both whistleblowers and organizations, we set out to create something different.</p>
                <p className="text-[#374151] mb-4 text-sm">Our platform isn&apos;t just about compliance—it&apos;s about creating environments where ethical concerns can be raised safely, investigated thoroughly, and resolved fairly. Where technology serves humanity, not the other way around.</p>
                <p className="text-[#374151] mb-4 text-sm">As we continue to grow and evolve, this mission remains at our core. We&apos;re honored to partner with organizations that share our commitment to integrity, and we look forward to building a more accountable corporate world together.</p>
                <span className="text-xs text-[#6B7280] mt-2">June 2025</span>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
}