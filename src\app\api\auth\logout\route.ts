import { NextRequest, NextResponse } from 'next/server';
import { validateSessionToken, revokeSessionToken } from '@/lib/auth/session-manager';
import { AuthAuditLogger } from '@/lib/security/audit-logger';

export async function POST(request: NextRequest) {
  try {
    // Get token from Authorization header or cookies
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.startsWith('Bearer ') 
      ? authHeader.substring(7)
      : request.cookies.get('auth_token')?.value;

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'No token provided' },
        { status: 400 }
      );
    }

    // Validate the session token to get user info and token ID
    const sessionValidation = await validateSessionToken(token);
    
    if (sessionValidation.valid && sessionValidation.decoded) {
      const decoded = sessionValidation.decoded as { tokenId: string; userId: string; email: string };
      const { tokenId, userId, email } = decoded;
      
      // Revoke the session token
      const revoked = await revokeSessionToken(tokenId, 'user_logout');
      
      if (revoked) {
        // Log successful logout
        await AuthAuditLogger.logLogout(request, userId, email);
        
        // Create response with cleared cookies
        const response = NextResponse.json({
          success: true,
          message: 'Logged out successfully'
        });
        
        // Clear authentication cookies
        response.cookies.set('auth_token', '', {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'strict',
          maxAge: 0,
          path: '/'
        });
        
        response.cookies.set('user_session', '', {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'strict',
          maxAge: 0,
          path: '/'
        });
        
        return response;
      } else {
        return NextResponse.json(
          { success: false, error: 'Failed to revoke session' },
          { status: 500 }
        );
      }
    } else {
      // Token is invalid or expired, but still clear cookies
      const response = NextResponse.json({
        success: true,
        message: 'Logged out (session was already invalid)'
      });
      
      // Clear cookies anyway
      response.cookies.set('auth_token', '', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 0,
        path: '/'
      });
      
      response.cookies.set('user_session', '', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 0,
        path: '/'
      });
      
      return response;
    }
  } catch {
    return NextResponse.json(
      { success: false, error: 'Logout failed' },
      { status: 500 }
    );
  }
}

// Also support GET for logout links
export async function GET(request: NextRequest) {
  return POST(request);
}
