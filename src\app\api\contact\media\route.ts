import { NextRequest, NextResponse } from 'next/server';
import { connectDB } from '@/lib/db/connection';
import MediaInquiry from '@/lib/db/models/MediaInquiry';
import { emailService } from '@/lib/email/emailService';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { z } from 'zod';

export const runtime = 'nodejs';

const mediaInquirySchema = z.object({
  mediaOrganization: z.string().min(2).max(200),
  journalistName: z.string().min(2).max(100),
  email: z.email(),
  phoneNumber: z.string().min(10).max(20),
  typeOfInquiry: z.enum(['interview_request', 'press_release', 'company_information', 'product_demo', 'expert_commentary', 'case_study', 'other']),
  deadline: z.string().transform((str) => new Date(str)),
  detailedMessage: z.string().min(20).max(3000),
});

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const formData = await request.formData();
    
    // Extract form fields
    const data = {
      mediaOrganization: formData.get('mediaOrganization') as string,
      journalistName: formData.get('journalistName') as string,
      email: formData.get('email') as string,
      phoneNumber: formData.get('phoneNumber') as string,
      typeOfInquiry: formData.get('typeOfInquiry') as string,
      deadline: formData.get('deadline') as string,
      detailedMessage: formData.get('detailedMessage') as string,
    };

    // Validate the form data
    const validationResult = mediaInquirySchema.safeParse(data);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid form data',
          details: validationResult.error.issues
        },
        { status: 400 }
      );
    }

    const validatedData = validationResult.data;

    // Validate deadline is in the future
    if (validatedData.deadline <= new Date()) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Deadline must be in the future'
        },
        { status: 400 }
      );
    }

    // Handle file uploads (press credentials)
    const pressCredentials: any[] = [];
    const files = formData.getAll('pressCredentials') as File[];
    
    if (files && files.length > 0) {
      // Create uploads directory if it doesn't exist
      const uploadsDir = join(process.cwd(), 'uploads', 'media');
      await mkdir(uploadsDir, { recursive: true });

      for (const file of files) {
        if (file.size > 0) {
          const bytes = await file.arrayBuffer();
          const buffer = Buffer.from(bytes);
          
          // Generate unique filename
          const timestamp = Date.now();
          const filename = `${timestamp}-${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`;
          const filepath = join(uploadsDir, filename);
          
          await writeFile(filepath, buffer);
          
          pressCredentials.push({
            filename,
            originalName: file.name,
            mimeType: file.type,
            size: file.size,
            path: filepath
          });
        }
      }
    }

    // Get client metadata
    const userAgent = request.headers.get('user-agent') || undefined;
    const forwarded = request.headers.get('x-forwarded-for');
    const ipAddress = forwarded ? forwarded.split(',')[0] : request.headers.get('x-real-ip') || undefined;

    // Create media inquiry
    const mediaInquiry = new MediaInquiry({
      ...validatedData,
      pressCredentials: pressCredentials.length > 0 ? pressCredentials : undefined,
      metadata: {
        userAgent,
        ipAddress,
        source: 'media_inquiry_form',
        verified: false // Would be verified manually by media team
      }
    });

    await mediaInquiry.save();

    // Send notification email to media relations team
    try {
      const mediaEmail = process.env.MEDIA_EMAIL || process.env.CONTACT_FORM_RECIPIENT || process.env.EMAIL_SERVER_USER;
      
      if (mediaEmail) {
        const emailSent = await emailService.sendMediaInquiryNotification({
          inquiryNumber: mediaInquiry.inquiryNumber,
          mediaOrganization: mediaInquiry.mediaOrganization,
          journalistName: mediaInquiry.journalistName,
          email: mediaInquiry.email,
          phoneNumber: mediaInquiry.phoneNumber,
          typeOfInquiry: mediaInquiry.typeOfInquiry,
          deadline: mediaInquiry.deadline,
          detailedMessage: mediaInquiry.detailedMessage,
          priority: mediaInquiry.priority,
          credentialsCount: pressCredentials.length,
          mediaEmail
        });

        if (emailSent) {
          // Send auto-reply to journalist
          await emailService.sendMediaInquiryAutoReply({
            inquiryNumber: mediaInquiry.inquiryNumber,
            journalistName: mediaInquiry.journalistName,
            email: mediaInquiry.email,
            mediaOrganization: mediaInquiry.mediaOrganization,
            typeOfInquiry: mediaInquiry.typeOfInquiry,
            deadline: mediaInquiry.deadline
          });
        }
      }
    } catch (emailError) {
      console.error('Failed to send media inquiry emails:', emailError);
      // Don't fail the request if email fails
    }

    return NextResponse.json({
      success: true,
      data: {
        inquiryNumber: mediaInquiry.inquiryNumber,
        message: 'Your media inquiry has been submitted successfully. Our media relations team will respond within 1-2 business days.',
        estimatedResponseTime: getMediaResponseTime(mediaInquiry.priority),
        priority: mediaInquiry.priority
      }
    });

  } catch (error) {
    console.error('Media inquiry submission error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'An unexpected error occurred. Please try again later.' 
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const inquiryNumber = searchParams.get('inquiry');
    const email = searchParams.get('email');

    if (inquiryNumber && email) {
      // Get specific media inquiry
      const mediaInquiry = await (MediaInquiry as any).findOne({
        inquiryNumber,
        email: email.toLowerCase()
      }).select('-metadata -pressCredentials.path').populate('assignedTo', 'firstName lastName email');

      if (!mediaInquiry) {
        return NextResponse.json(
          { success: false, error: 'Media inquiry not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        data: {
          ...mediaInquiry.toObject(),
          daysUntilDeadline: mediaInquiry.daysUntilDeadline,
          isOverdue: mediaInquiry.isOverdue(),
          isDeadlineApproaching: mediaInquiry.isDeadlineApproaching()
        }
      });
    }

    // Get statistics and urgent inquiries (admin only - would need auth check in real app)
    const stats = await (MediaInquiry as any).getStats();
    const urgentInquiries = await (MediaInquiry as any).findUrgent();
    
    return NextResponse.json({
      success: true,
      data: {
        ...stats,
        urgentCount: urgentInquiries.length,
        urgentInquiries: urgentInquiries.slice(0, 10) // Limit to 10 for performance
      }
    });

  } catch (error) {
    console.error('Media inquiry retrieval error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const inquiryNumber = searchParams.get('inquiry');
    
    if (!inquiryNumber) {
      return NextResponse.json(
        { success: false, error: 'Inquiry number is required' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { status, note, assignedTo, response } = body;

    const mediaInquiry = await (MediaInquiry as any).findOne({ inquiryNumber });
    
    if (!mediaInquiry) {
      return NextResponse.json(
        { success: false, error: 'Media inquiry not found' },
        { status: 404 }
      );
    }

    // Update status if provided
    if (status) {
      mediaInquiry.status = status;
      if (status === 'responded') {
        mediaInquiry.respondedAt = new Date();
      }
    }

    // Update assigned user if provided
    if (assignedTo) {
      mediaInquiry.assignedTo = assignedTo;
    }

    // Update response if provided
    if (response) {
      mediaInquiry.response = response;
    }

    // Add note if provided
    if (note && note.content && note.addedBy) {
      await mediaInquiry.addNote(note.content, note.addedBy);
    }

    await mediaInquiry.save();

    return NextResponse.json({
      success: true,
      data: mediaInquiry
    });

  } catch (error) {
    console.error('Media inquiry update error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

function getMediaResponseTime(priority: string): string {
  const responseMap = {
    urgent: '4-8 hours',
    high: '24 hours',
    medium: '1-2 business days',
    low: '2-3 business days'
  };
  return responseMap[priority as keyof typeof responseMap] || '1-2 business days';
}

// Handle preflight requests for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PATCH, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
