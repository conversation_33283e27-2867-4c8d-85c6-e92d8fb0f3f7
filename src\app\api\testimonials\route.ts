import { NextRequest, NextResponse } from 'next/server';
import { DataService } from '@/lib/db/dataService';
import connectDB from '@/lib/db/mongodb';
import { testimonials } from '@/lib/staticContent';

export const runtime = 'nodejs';

// Use static content testimonials as fallback when database is empty

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');

    // Try to get testimonials from database
    let dbTestimonials = await DataService.getTestimonials(category || undefined);

    // If database is empty, use static content testimonials as fallback
    if (!dbTestimonials || dbTestimonials.length === 0) {
      let filteredTestimonials = testimonials.filter(_testimonial => true); // All static testimonials are active

      if (category) {
        filteredTestimonials = filteredTestimonials.filter(
          testimonial => testimonial.category === category
        );
      }

      // Transform static testimonial format to API format
      dbTestimonials = filteredTestimonials.map((testimonial) => ({
        id: `static-testimonial-${testimonial.id}`,
        name: testimonial.name,
        role: testimonial.role,
        company: testimonial.company,
        content: testimonial.content,
        rating: testimonial.rating,
        category: testimonial.category || 'general',
        isActive: true,
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
        featured: false,
        avatar: testimonial.image
      }));
    }

    return NextResponse.json({
      success: true,
      data: dbTestimonials
    });
  } catch (error) {
    console.error('Testimonials API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}