import { Schema } from 'mongoose';
import { createModel } from '../utils';
import { IMessage } from './interfaces';

const MessageSchema = new Schema({
  conversationId: { type: Schema.Types.ObjectId, ref: 'Conversation' },
  reportId: { type: Schema.Types.ObjectId, ref: 'Report' }, // For direct report messaging
  senderId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  content: { type: String, required: true },
  htmlContent: { type: String }, // For rich text formatting
  messageType: {
    type: String,
    enum: ['text', 'file', 'system', 'user_message', 'investigator_message'],
    default: 'text'
  },
  isAnonymous: { type: <PERSON>olean, default: false },
  isEncrypted: { type: Boolean, default: true },
  encryptionData: {
    contentIv: { type: String }, // Initialization vector for content encryption
    contentTag: { type: String }, // Authentication tag for content
    htmlContentIv: { type: String }, // IV for HTML content
    htmlContentTag: { type: String }, // Tag for HTML content
    // Multi-key encryption metadata
    keyVersion: { type: String }, // Which key version was used
    algorithm: { type: String }, // Encryption algorithm used
    encryptedAt: { type: Date }, // When the message was encrypted
    migratedFrom: { type: String } // Previous key version if migrated
  },
  readBy: [{
    userId: { type: Schema.Types.ObjectId, ref: 'User' },
    readAt: { type: Date, default: Date.now }
  }],
  attachments: [{
    fileName: { type: String },
    fileUrl: { type: String },
    fileSize: { type: Number },
    mimeType: { type: String }
  }],
  editedAt: { type: Date },
  isDeleted: { type: Boolean, default: false },
  deletedAt: { type: Date },
  replyTo: { type: Schema.Types.ObjectId, ref: 'Message' }, // For message replies
  reactions: [{
    userId: { type: Schema.Types.ObjectId, ref: 'User' },
    emoji: { type: String },
    createdAt: { type: Date, default: Date.now }
  }],
  timestamp: { type: Date, default: Date.now }, // For compatibility with existing code

  // Anonymous messaging metadata
  metadata: {
    sentViaToken: { type: Boolean, default: false },
    reportToken: { type: String },
    anonymousSessionId: { type: String }
  }
}, {
  timestamps: true
});

// Use the utility function to create the model safely
const Message = createModel<IMessage>('Message', MessageSchema);

export default Message;