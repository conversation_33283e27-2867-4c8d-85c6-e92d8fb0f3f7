import { Schema } from 'mongoose';
import { createModel } from '../utils';

const AuditLogSchema = new Schema({
  companyId: { type: Schema.Types.ObjectId, ref: 'Company', required: true },
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  userName: { type: String, required: true }, // Denormalized for performance
  userRole: { type: String, required: true },
  
  // Action details
  action: { type: String, required: true }, // e.g., "Case Assignment", "Case Update"
  actionType: { 
    type: String, 
    enum: [
      'login', 'logout', 'create', 'update', 'delete', 'view', 
      'assignment', 'status_change', 'escalation', 'closure', 
      'message_sent', 'file_upload', 'export', 'system_alert',
      'user_management', 'settings_change', 'security_event'
    ],
    required: true 
  },
  
  // Target resource
  resourceType: { 
    type: String, 
    enum: ['report', 'user', 'conversation', 'message', 'notification', 'system', 'escalation'],
    required: true 
  },
  resourceId: { type: String }, // ID of the affected resource
  
  // Details
  description: { type: String, required: true },
  details: { type: Schema.Types.Mixed }, // Additional structured data
  
  // Context
  ipAddress: { type: String, required: true },
  userAgent: { type: String },
  sessionId: { type: String },
  
  // Security and compliance
  severity: {
    type: String,
    enum: ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'],
    default: 'LOW'
  },
  category: { 
    type: String, 
    enum: ['security', 'compliance', 'operational', 'administrative'],
    default: 'operational'
  },
  
  // Status and flags
  isSystemGenerated: { type: Boolean, default: false },
  isSuspicious: { type: Boolean, default: false },
  isExported: { type: Boolean, default: false },
  
  // Metadata
  metadata: {
    beforeState: { type: Schema.Types.Mixed }, // State before the action
    afterState: { type: Schema.Types.Mixed },  // State after the action
    source: { type: String, default: 'web' }, // web, api, mobile, system
    correlationId: { type: String }, // For tracking related actions
    tags: [{ type: String }]
  }
}, {
  timestamps: true
});

// Indexes for better query performance
AuditLogSchema.index({ companyId: 1, createdAt: -1 });
AuditLogSchema.index({ userId: 1, createdAt: -1 });
AuditLogSchema.index({ actionType: 1, createdAt: -1 });
AuditLogSchema.index({ resourceType: 1, resourceId: 1 });
AuditLogSchema.index({ severity: 1, createdAt: -1 });
AuditLogSchema.index({ category: 1, createdAt: -1 });
AuditLogSchema.index({ isSuspicious: 1 });

// Virtual for formatted timestamp
AuditLogSchema.virtual('formattedTimestamp').get(function() {
  return this.createdAt.toISOString().replace('T', ' ').substring(0, 19);
});

// Static method to create audit log entry
AuditLogSchema.statics.createEntry = function(data: {
  companyId: string;
  userId: string;
  userName: string;
  userRole: string;
  action: string;
  actionType: string;
  resourceType: string;
  resourceId?: string;
  description: string;
  details?: any;
  ipAddress: string;
  userAgent?: string;
  sessionId?: string;
  severity?: string;
  category?: string;
  isSystemGenerated?: boolean;
  metadata?: any;
}) {
  return this.create({
    companyId: data.companyId,
    userId: data.userId,
    userName: data.userName,
    userRole: data.userRole,
    action: data.action,
    actionType: data.actionType,
    resourceType: data.resourceType,
    resourceId: data.resourceId,
    description: data.description,
    details: data.details,
    ipAddress: data.ipAddress,
    userAgent: data.userAgent,
    sessionId: data.sessionId,
    severity: data.severity || 'low',
    category: data.category || 'operational',
    isSystemGenerated: data.isSystemGenerated || false,
    metadata: data.metadata || {}
  });
};

export default createModel('AuditLog', AuditLogSchema);
