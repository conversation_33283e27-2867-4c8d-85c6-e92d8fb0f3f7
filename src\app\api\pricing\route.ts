import { NextResponse } from 'next/server';
import { PRICING_PLANS } from '@/lib/staticContent';

export const runtime = 'nodejs';

export async function GET() {
  try {
    // Transform static pricing plans to API format
    const apiPlans = PRICING_PLANS.map((plan, index) => ({
      id: `plan-${plan.name.toLowerCase()}`,
      name: plan.name,
      price: parseInt(plan.monthlyPrice.replace(/[^0-9]/g, '')) || 0,
      features: plan.features,
      order: index + 1,
      isActive: true,
      description: plan.description,
      monthlyPrice: plan.monthlyPrice,
      yearlyPrice: plan.yearlyPrice,
      access: plan.access
    }));

    return NextResponse.json({
      success: true,
      data: apiPlans
    });
  } catch (error) {
    console.error('Pricing API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}