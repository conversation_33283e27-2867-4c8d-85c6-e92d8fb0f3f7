import Link from 'next/link';
import Image from 'next/image';
import { Metada<PERSON> } from 'next';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Header from '@/components/home-components/shared/Header';
import Footer from '@/components/home-components/shared/Footer';

export const metadata: Metadata = {
  title: "Sign Up - 7IRIS Whistleblowing Platform",
  description: "Create a new account on the 7IRIS Whistleblowing Platform as a whistleblower or administrator.",
};

export default function SignupPage() {
  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main id="main-content" aria-label="Signup selection" className="flex-1 pt-16 sm:pt-20 md:pt-24">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12 md:py-16 lg:py-20">
          <div className="max-w-sm sm:max-w-md lg:max-w-lg xl:max-w-xl mx-auto">
            <Card className="bg-[#F7FFF2] shadow-lg border-0 w-full">
              <CardHeader className="px-4 sm:px-6 pt-6 pb-4">
                <div className="flex justify-center mb-4 sm:mb-6">
                  <Image
                    src="/desktop/main/header/logo.svg"
                    alt="7IRIS Logo"
                    width={80}
                    height={36}
                    className="w-20 h-auto sm:w-24 md:w-28 lg:w-32 hover:scale-105 transition-transform duration-300"
                    priority
                  />
                </div>
                <CardTitle className="text-center text-xl sm:text-2xl lg:text-3xl font-bold text-[#1E4841]">
                  Choose Signup Type
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 sm:space-y-6 p-4 sm:p-6 lg:p-8">
                <Button 
                  asChild 
                  className="w-full bg-[#1E4841] hover:bg-[#2A5D54] text-white font-semibold py-3 sm:py-4 lg:py-5 h-auto text-sm sm:text-base lg:text-lg transition-colors duration-300"
                >
                  <Link href="/signup/whistleblower">
                    Whistleblower Signup
                  </Link>
                </Button>
                <Button 
                  asChild 
                  className="w-full bg-[#D6FFBD] text-[#1E4841] hover:bg-[#C5EEA9] border-0 font-semibold py-3 sm:py-4 lg:py-5 h-auto text-sm sm:text-base lg:text-lg transition-colors duration-300"
                  variant="secondary"
                >
                  <Link href="/signup/admin">
                    Admin Signup
                  </Link>
                </Button>
                <div className="text-center mt-4 sm:mt-6 text-[#1E4841]">
                  <p className="text-sm sm:text-base">
                    Already have an account? 
                    <Link href="/login" className="font-semibold hover:underline ml-1">
                      Log in
                    </Link>
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}