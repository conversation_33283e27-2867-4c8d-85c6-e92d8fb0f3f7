import { NextResponse } from 'next/server';
import { DataService } from '@/lib/db/dataService';
import { withCompanyIsolation, AuthenticatedRequest } from '@/lib/middleware/auth';
import { NotificationService } from '@/lib/services/notificationService';
import { emailService } from '@/lib/email/emailService';
import connectDB from '@/lib/db/mongodb';
import logger from '@/lib/utils/logger';
import mongoose from 'mongoose';

export const runtime = 'nodejs';

export const POST = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();

    const reportData = await request.json();

    // Validate required fields from multi-step form
    if (!reportData.title || !reportData.description || !reportData.category) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: title, description, and category' },
        { status: 400 }
      );
    }

    // Validate step 2 required fields
    if (!reportData.incidentDate || !reportData.specificLocation) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: incident date and specific location' },
        { status: 400 }
      );
    }

    // Process and structure the multi-step report data
    const processedReportData = {
      // Step 1 data
      title: reportData.title,
      description: reportData.description,
      category: reportData.category,
      dateOfOccurrence: reportData.dateOfOccurrence,
      location: reportData.location,
      isAnonymous: reportData.isAnonymous || false,

      // Step 2 data
      incidentDate: reportData.incidentDate,
      incidentTime: reportData.incidentTime,
      specificLocation: reportData.specificLocation,
      departmentInvolved: reportData.departmentInvolved,
      peopleInvolved: reportData.peopleInvolved,

      // Witness information
      hasWitnesses: reportData.witnessInfo?.hasWitnesses || false,
      witnessDetails: reportData.witnessInfo?.witnessDetails || '',

      // Evidence information
      hasEvidence: reportData.evidenceInfo?.hasEvidence || false,
      evidenceDescription: reportData.evidenceInfo?.evidenceDescription || '',
      evidenceFiles: reportData.evidenceInfo?.evidenceFiles || [],

      // Impact assessment
      urgencyLevel: reportData.urgencyLevel || 'Medium',
      financialImpact: reportData.impactAssessment?.financialImpact || '',
      operationalImpact: reportData.impactAssessment?.operationalImpact || '',
      reputationalImpact: reportData.impactAssessment?.reputationalImpact || '',

      // Previous reports
      hasPreviousReports: reportData.previousReports?.hasPreviousReports || false,
      previousReportDetails: reportData.previousReports?.previousReportDetails || '',

      // Additional information
      additionalComments: reportData.additionalComments || '',

      // Communication preferences
      emailUpdates: reportData.reportingPreferences?.emailUpdates ?? true,
      smsUpdates: reportData.reportingPreferences?.smsUpdates ?? false,

      // System fields
      userId: new mongoose.Types.ObjectId(request.user!.id),
      status: 'New',
      priority: reportData.urgencyLevel || 'Medium',
      isDraft: false,
      dateSubmitted: new Date(),
      lastUpdated: new Date(),
      submittedAt: reportData.submittedAt || new Date().toISOString()
    };

    // Create submitted report
    const submittedReport = await DataService.createReport(processedReportData);

    // Create notification for report submission
    try {
      await NotificationService.createReportSubmittedNotification(
        request.user!.id,
        submittedReport._id.toString(),
        reportData.title
      );
    } catch (notificationError) {
      logger.error('Failed to create report submission notification:', notificationError);
      // Don't fail the report creation if notification fails
    }

    // Send email notification to submitter
    try {
      const userEmail = request.user!.email;
      if (userEmail && processedReportData.emailUpdates) {
        await emailService.sendReportSubmissionNotification({
          id: submittedReport._id.toString(),
          title: reportData.title,
          category: reportData.category,
          priority: reportData.urgencyLevel || 'Medium',
          status: 'New',
          submittedAt: new Date(processedReportData.submittedAt)
        }, userEmail);
        logger.info('Report submission email sent', {
          reportId: submittedReport._id.toString(),
          userEmail
        });
      }
    } catch (emailError) {
      logger.error('Failed to send report submission email:', emailError);
      // Don't fail the report creation if email fails
    }

    return NextResponse.json({
      success: true,
      data: submittedReport,
      message: 'Report submitted successfully! You will receive email updates on its progress.'
    }, { status: 201 });
  } catch (error) {
    logger.error('Submit report API error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to submit report' },
      { status: 500 }
    );
  }
});