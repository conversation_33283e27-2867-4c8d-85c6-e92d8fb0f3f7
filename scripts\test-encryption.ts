#!/usr/bin/env ts-node

/**
 * Test Encryption/Decryption Process
 * 
 * This script tests the encryption and decryption process to debug
 * the "bad decrypt" error we're seeing.
 */

import { config } from 'dotenv';
import crypto from 'crypto';
import fs from 'fs/promises';
import path from 'path';

// Load environment variables
config({ path: '.env.local' });

class EncryptionTester {
  private readonly credentialsFile: string;

  constructor() {
    this.credentialsFile = path.join(process.cwd(), 'secure', 'db-credentials.enc');
  }

  /**
   * Derive encryption key (same as SecureDatabaseManager)
   */
  private deriveEncryptionKey(): Buffer {
    const keyMaterial = process.env.DB_ENCRYPTION_KEY || process.env.JWT_SECRET || 'default-key-change-in-production';
    console.log('🔑 Key derivation:');
    console.log(`   DB_ENCRYPTION_KEY exists: ${!!process.env.DB_ENCRYPTION_KEY}`);
    console.log(`   JWT_SECRET exists: ${!!process.env.JWT_SECRET}`);
    console.log(`   Using key material: ${keyMaterial ? keyMaterial.substring(0, 10) + '...' : 'none'}`);
    
    const key = crypto.scryptSync(keyMaterial, 'db-credentials-salt', 32);
    console.log(`   Derived key (hex): ${key.toString('hex').substring(0, 20)}...`);
    return key;
  }

  /**
   * Test the current encrypted file
   */
  async testCurrentFile(): Promise<void> {
    try {
      console.log('🧪 Testing current encrypted file...');
      console.log(`📁 File: ${this.credentialsFile}`);
      
      // Check if file exists
      const fileExists = await fs.access(this.credentialsFile).then(() => true).catch(() => false);
      if (!fileExists) {
        console.log('❌ File does not exist');
        return;
      }

      // Read and parse the encrypted data
      const encryptedData = JSON.parse(await fs.readFile(this.credentialsFile, 'utf8'));
      console.log('✅ File read successfully');
      console.log(`   IV: ${encryptedData.iv}`);
      console.log(`   Data length: ${encryptedData.data.length} characters`);
      console.log(`   Timestamp: ${encryptedData.timestamp}`);

      // Derive encryption key
      const encryptionKey = this.deriveEncryptionKey();

      // Attempt decryption
      const iv = Buffer.from(encryptedData.iv, 'hex');
      const decipher = crypto.createDecipheriv('aes-256-cbc', encryptionKey, iv);
      
      let decrypted = decipher.update(encryptedData.data, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      const credentialsData = JSON.parse(decrypted);
      
      console.log('✅ Decryption successful!');
      console.log(`📊 Found ${credentialsData.length} credential entries:`);
      credentialsData.forEach((cred: any, index: number) => {
        console.log(`   ${index + 1}. ${cred.version}: ${cred.description} (Active: ${cred.isActive})`);
      });
      
    } catch (error) {
      console.error('❌ Decryption failed:', error.message);
      console.error('   This suggests the encryption key used to create the file is different from the current key');
    }
  }

  /**
   * Create a new test file with current environment
   */
  async createTestFile(): Promise<void> {
    try {
      console.log('\n🔧 Creating new test file with current environment...');
      
      const encryptionKey = this.deriveEncryptionKey();
      
      // Test data
      const testData = [
        {
          version: 'v1',
          uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/test',
          encryptedAt: new Date(),
          isActive: true,
          isRetired: false,
          description: 'Test credentials created with current environment'
        }
      ];

      // Encrypt
      const plaintext = JSON.stringify(testData, null, 2);
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipheriv('aes-256-cbc', encryptionKey, iv);
      
      let encrypted = cipher.update(plaintext, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      const encryptedData = {
        iv: iv.toString('hex'),
        data: encrypted,
        timestamp: new Date().toISOString(),
        description: 'Test encrypted credentials'
      };

      // Save to test file
      const testFile = this.credentialsFile.replace('.enc', '-test.enc');
      await fs.writeFile(testFile, JSON.stringify(encryptedData, null, 2));
      
      console.log(`✅ Test file created: ${testFile}`);
      
      // Immediately test decryption
      console.log('🧪 Testing immediate decryption...');
      const iv2 = Buffer.from(encryptedData.iv, 'hex');
      const decipher = crypto.createDecipheriv('aes-256-cbc', encryptionKey, iv2);
      
      let decrypted = decipher.update(encryptedData.data, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      const result = JSON.parse(decrypted);
      console.log('✅ Immediate decryption successful!');
      console.log(`📊 Test data: ${result[0].description}`);
      
    } catch (error) {
      console.error('❌ Test file creation failed:', error);
    }
  }

  /**
   * Show environment details
   */
  showEnvironment(): void {
    console.log('\n🌍 Environment Details:');
    console.log(`   NODE_ENV: ${process.env.NODE_ENV}`);
    console.log(`   DB_ENCRYPTION_KEY: ${process.env.DB_ENCRYPTION_KEY ? 'Set' : 'Not set'}`);
    console.log(`   JWT_SECRET: ${process.env.JWT_SECRET ? 'Set (' + process.env.JWT_SECRET.substring(0, 10) + '...)' : 'Not set'}`);
    console.log(`   MONGODB_URI: ${process.env.MONGODB_URI ? 'Set' : 'Not set'}`);
  }
}

// Main execution
async function main() {
  console.log('🔐 Encryption/Decryption Test');
  console.log('='.repeat(40));
  
  const tester = new EncryptionTester();
  
  tester.showEnvironment();
  await tester.testCurrentFile();
  await tester.createTestFile();
  
  console.log('\n🎉 Encryption test completed!');
}

// Execute main function
main().catch(error => {
  console.error('Script execution failed:', error);
  process.exit(1);
});
