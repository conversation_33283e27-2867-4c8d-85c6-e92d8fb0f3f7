import React from 'react';
import Link from 'next/link';
import { Bookmark } from 'lucide-react';
import { Button } from '@/components/ui/button';
import RelatedPostCard from './RelatedPostCard';
import { BlogCard } from '@/lib/types';

interface BlogSidebarProps {
  category: string;
  relatedPosts: BlogCard[];
  tags?: string[];
}

/**
 * BlogSidebar component provides a standardized sidebar for blog posts
 */
const BlogSidebar: React.FC<BlogSidebarProps> = ({ 
  category, 
  relatedPosts,
  tags
}) => {
  return (
    <aside className="w-full lg:w-1/3 space-y-6 sm:space-y-8">
      {/* Category */}
      <div className="bg-[#ECF4E9] p-4 sm:p-6 rounded-lg">
        <h3 className="text-base sm:text-lg font-semibold text-[#1E4841] mb-3 sm:mb-4">Category</h3>
        <Link href={`/blog/category/${encodeURIComponent(category)}`}>
          <span className="inline-block px-4 py-2 bg-white text-[#1E4841] rounded-md hover:bg-[#1E4841] hover:text-white transition-colors">
            {category}
          </span>
        </Link>
      </div>

      {/* Tags if available */}
      {tags && tags.length > 0 && (
        <div className="border border-gray-200 p-4 sm:p-6 rounded-lg">
          <h3 className="text-base sm:text-lg font-semibold text-[#1E4841] mb-3 sm:mb-4">Related Topics</h3>
          <div className="flex flex-wrap gap-2 gap-y-3 sm:gap-y-4">
            {tags.map((tag) => (
              <Link href={`/blog/search?tag=${tag}`} key={tag}>
                <span className="px-3 py-1 sm:px-4 sm:py-2 bg-[#ECF4E9] text-[#1E4841] rounded-full text-xs sm:text-sm hover:bg-[#1E4841] hover:text-white transition-colors">
                  {tag}
                </span>
              </Link>
            ))}
          </div>
        </div>
      )}

      {/* Save for later */}
      <div className="border border-gray-200 p-4 sm:p-6 rounded-lg lg:block hidden">
        <h3 className="text-base sm:text-lg font-semibold text-[#1E4841] mb-3 sm:mb-4">Save for Later</h3>
        <Button variant="outline" className="w-full flex items-center gap-2 justify-center">
          <Bookmark className="h-5 w-5" />
          <span className="hidden lg:inline">Save Article</span>
        </Button>
      </div>

      {/* Related Posts */}
      {relatedPosts.length > 0 && (
        <div className="border border-gray-200 p-4 sm:p-6 rounded-lg">
          <h3 className="text-base sm:text-lg font-semibold text-[#1E4841] mb-3 sm:mb-4">Related Articles</h3>
          <div className="space-y-3 sm:space-y-4">
            {relatedPosts.map((post, index) => (
              <RelatedPostCard key={index} post={post} />
            ))}
          </div>
        </div>
      )}
    </aside>
  );
};

export default BlogSidebar;