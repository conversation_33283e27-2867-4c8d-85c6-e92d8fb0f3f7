/**
 * Secure File Upload System
 * 
 * This module provides production-safe file upload handling with:
 * 1. Path traversal prevention
 * 2. File type validation and sanitization
 * 3. Malware scanning and threat detection
 * 4. Secure file storage with sandboxing
 * 5. Comprehensive audit logging
 */

import { NextRequest } from 'next/server';
import { writeFile, mkdir, access } from 'fs/promises';
import { join, resolve, basename, extname, dirname } from 'path';
import crypto from 'crypto';
import { validateFile, scanFileForThreats } from './file-security';
import { DataAuditLogger, SecurityAuditLogger } from './audit-logger';

interface SecureUploadOptions {
  allowedTypes?: string[];
  maxFileSize?: number;
  maxFiles?: number;
  uploadPath?: string;
  requireAuth?: boolean;
  scanForThreats?: boolean;
  quarantineEnabled?: boolean;
}

interface SecureUploadResult {
  success: boolean;
  files?: SecureFileInfo[];
  error?: string;
  quarantinedFiles?: string[];
}

interface SecureFileInfo {
  id: string;
  originalName: string;
  sanitizedName: string;
  size: number;
  mimeType: string;
  extension: string;
  path: string;
  url: string;
  hash: string;
  uploadedAt: Date;
  isSecure: boolean;
  scanResults?: {
    safe: boolean;
    threats: string[];
  };
}

class SecureFileUploadManager {
  private readonly UPLOAD_BASE_PATH: string;
  private readonly QUARANTINE_PATH: string;
  private readonly MAX_FILENAME_LENGTH = 100;
  private readonly ALLOWED_EXTENSIONS = [
    '.jpg', '.jpeg', '.png', '.gif', '.webp', // Images
    '.pdf', '.doc', '.docx', '.txt', '.csv', // Documents
    '.xls', '.xlsx', '.ppt', '.pptx' // Office files
  ];

  constructor() {
    this.UPLOAD_BASE_PATH = join(process.cwd(), 'secure-uploads');
    this.QUARANTINE_PATH = join(process.cwd(), 'quarantine');
  }

  /**
   * Process secure file upload
   */
  async processUpload(
    request: NextRequest,
    options: SecureUploadOptions = {}
  ): Promise<SecureUploadResult> {
    try {
      // Set default options
      const config = {
        allowedTypes: options.allowedTypes || this.getDefaultAllowedTypes(),
        maxFileSize: options.maxFileSize || 50 * 1024 * 1024, // 50MB
        maxFiles: options.maxFiles || 10,
        uploadPath: options.uploadPath || 'general',
        requireAuth: options.requireAuth ?? true,
        scanForThreats: options.scanForThreats ?? true,
        quarantineEnabled: options.quarantineEnabled ?? true,
        ...options
      };

      // Validate request
      const validationResult = await this.validateUploadRequest(request, config);
      if (!validationResult.valid) {
        return { success: false, error: validationResult.error };
      }

      // Extract files from form data
      const formData = await request.formData();
      const files = this.extractFiles(formData, config.maxFiles);
      
      if (files.length === 0) {
        return { success: false, error: 'No files provided' };
      }

      // Process each file securely
      const processedFiles: SecureFileInfo[] = [];
      const quarantinedFiles: string[] = [];

      for (const file of files) {
        try {
          const result = await this.processSecureFile(file, config, request);
          
          if (result.isSecure) {
            processedFiles.push(result);
          } else {
            quarantinedFiles.push(result.originalName);
            
            if (config.quarantineEnabled) {
              await this.quarantineFile(file, result, request);
            }
          }
        } catch (error) {
          console.error(`Error processing file ${file.name}:`, error);
          quarantinedFiles.push(file.name);
        }
      }

      // Log upload activity
      await DataAuditLogger.logDataAccess(request, 'system', 'file-upload', `File upload: ${processedFiles.length} files processed`);

      return {
        success: true,
        files: processedFiles,
        quarantinedFiles: quarantinedFiles.length > 0 ? quarantinedFiles : undefined
      };

    } catch (error) {
      console.error('Secure upload error:', error);
      
      await SecurityAuditLogger.logSuspiciousActivity(
        request,
        'File upload system error',
        { error: error.message }
      );

      return {
        success: false,
        error: 'Upload processing failed'
      };
    }
  }

  /**
   * Process individual file securely
   */
  private async processSecureFile(
    file: File,
    config: SecureUploadOptions,
    request: NextRequest
  ): Promise<SecureFileInfo> {
    // Generate secure file ID
    const fileId = this.generateSecureFileId();
    
    // Sanitize filename to prevent path traversal
    const sanitizedName = this.sanitizeFilename(file.name);
    const extension = this.getSecureExtension(file.name);
    
    // Validate file
    const validation = await validateFile(file, {
      allowedTypes: config.allowedTypes,
      maxSize: config.maxFileSize
    });

    if (!validation.valid) {
      throw new Error(`File validation failed: ${validation.errors.join(', ')}`);
    }

    // Scan for threats if enabled
    let scanResults = { safe: true, threats: [] as string[] };
    if (config.scanForThreats) {
      scanResults = await scanFileForThreats(file, request);
    }

    // Calculate file hash for integrity
    const buffer = await file.arrayBuffer();
    const hash = crypto.createHash('sha256').update(Buffer.from(buffer)).digest('hex');

    // Create secure storage path
    const secureStoragePath = this.createSecureStoragePath(config.uploadPath!, fileId, extension);
    
    // Ensure directory exists
    await this.ensureSecureDirectory(dirname(secureStoragePath));

    // Write file securely
    await writeFile(secureStoragePath, Buffer.from(buffer), { mode: 0o644 });

    // Generate secure URL
    const secureUrl = this.generateSecureUrl(config.uploadPath!, fileId, extension);

    const fileInfo: SecureFileInfo = {
      id: fileId,
      originalName: file.name,
      sanitizedName,
      size: file.size,
      mimeType: file.type,
      extension,
      path: secureStoragePath,
      url: secureUrl,
      hash,
      uploadedAt: new Date(),
      isSecure: scanResults.safe,
      scanResults
    };

    return fileInfo;
  }

  /**
   * Sanitize filename to prevent path traversal attacks
   */
  private sanitizeFilename(filename: string): string {
    if (!filename) {
      return 'unnamed_file';
    }

    // Remove path separators and dangerous characters
    let sanitized = basename(filename)
      .replace(/[<>:"/\\|?*\x00-\x1f]/g, '_') // Remove dangerous characters
      .replace(/^\.+/, '_') // Remove leading dots
      .replace(/\.+$/, '_') // Remove trailing dots
      .replace(/\s+/g, '_') // Replace spaces with underscores
      .toLowerCase();

    // Limit length
    if (sanitized.length > this.MAX_FILENAME_LENGTH) {
      const ext = extname(sanitized);
      const nameWithoutExt = sanitized.slice(0, this.MAX_FILENAME_LENGTH - ext.length);
      sanitized = nameWithoutExt + ext;
    }

    // Ensure we have a valid filename
    if (!sanitized || sanitized === '_') {
      sanitized = 'unnamed_file';
    }

    return sanitized;
  }

  /**
   * Get secure file extension
   */
  private getSecureExtension(filename: string): string {
    const ext = extname(filename).toLowerCase();
    
    if (!this.ALLOWED_EXTENSIONS.includes(ext)) {
      throw new Error(`File extension ${ext} is not allowed`);
    }
    
    return ext;
  }

  /**
   * Create secure storage path with sandboxing
   */
  private createSecureStoragePath(uploadPath: string, fileId: string, extension: string): string {
    // Sanitize upload path to prevent directory traversal
    const sanitizedPath = uploadPath.replace(/[^a-zA-Z0-9_-]/g, '_');
    
    // Create date-based subdirectory for organization
    const date = new Date();
    const dateDir = `${date.getFullYear()}/${String(date.getMonth() + 1).padStart(2, '0')}`;
    
    // Construct secure path
    const relativePath = join(sanitizedPath, dateDir, `${fileId}${extension}`);
    const fullPath = join(this.UPLOAD_BASE_PATH, relativePath);
    
    // Ensure path is within upload directory (prevent path traversal)
    const resolvedPath = resolve(fullPath);
    const resolvedBase = resolve(this.UPLOAD_BASE_PATH);
    
    if (!resolvedPath.startsWith(resolvedBase)) {
      throw new Error('Invalid upload path - potential path traversal detected');
    }
    
    return resolvedPath;
  }

  /**
   * Ensure secure directory exists
   */
  private async ensureSecureDirectory(dirPath: string): Promise<void> {
    try {
      await access(dirPath);
    } catch {
      await mkdir(dirPath, { recursive: true, mode: 0o755 });
    }
  }

  /**
   * Generate secure file ID
   */
  private generateSecureFileId(): string {
    const timestamp = Date.now().toString(36);
    const random = crypto.randomBytes(16).toString('hex');
    return `${timestamp}_${random}`;
  }

  /**
   * Generate secure URL for file access
   */
  private generateSecureUrl(uploadPath: string, fileId: string, extension: string): string {
    // Create secure URL that doesn't expose file system structure
    return `/api/files/secure/${uploadPath}/${fileId}${extension}`;
  }

  /**
   * Quarantine suspicious file
   */
  private async quarantineFile(
    file: File,
    fileInfo: SecureFileInfo,
    request: NextRequest
  ): Promise<void> {
    try {
      const quarantinePath = join(
        this.QUARANTINE_PATH,
        new Date().toISOString().split('T')[0],
        `${fileInfo.id}_${fileInfo.sanitizedName}`
      );

      await this.ensureSecureDirectory(dirname(quarantinePath));
      
      const buffer = await file.arrayBuffer();
      await writeFile(quarantinePath, Buffer.from(buffer), { mode: 0o600 });

      await SecurityAuditLogger.logSuspiciousActivity(
        request,
        'File quarantined due to security threats',
        {
          fileName: file.name,
          threats: fileInfo.scanResults?.threats,
          quarantinePath
        }
      );

      console.log(`🚨 File quarantined: ${file.name} -> ${quarantinePath}`);
    } catch (error) {
      console.error('Failed to quarantine file:', error);
    }
  }

  /**
   * Validate upload request
   */
  private async validateUploadRequest(
    request: NextRequest,
    config: SecureUploadOptions
  ): Promise<{ valid: boolean; error?: string }> {
    // Check content type
    const contentType = request.headers.get('content-type');
    if (!contentType?.includes('multipart/form-data')) {
      return { valid: false, error: 'Invalid content type for file upload' };
    }

    // Check request size (basic check)
    const contentLength = request.headers.get('content-length');
    if (contentLength && parseInt(contentLength) > (config.maxFileSize! * config.maxFiles!)) {
      return { valid: false, error: 'Request size exceeds maximum allowed' };
    }

    return { valid: true };
  }

  /**
   * Extract files from form data
   */
  private extractFiles(formData: FormData, maxFiles: number): File[] {
    const files: File[] = [];
    
    for (const [, value] of formData.entries()) {
      if (value instanceof File && value.size > 0) {
        files.push(value);
        
        if (files.length >= maxFiles) {
          break;
        }
      }
    }
    
    return files;
  }

  /**
   * Get default allowed MIME types
   */
  private getDefaultAllowedTypes(): string[] {
    return [
      'image/jpeg', 'image/png', 'image/gif', 'image/webp',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain', 'text/csv'
    ];
  }
}

// Singleton instance
export const secureFileUploadManager = new SecureFileUploadManager();

export { SecureFileUploadManager };
export type { SecureUploadOptions, SecureUploadResult, SecureFileInfo };
