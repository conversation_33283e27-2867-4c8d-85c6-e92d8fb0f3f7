#!/usr/bin/env ts-node

/**
 * Consolidated Security Operations
 * 
 * This script consolidates all security-related operations including:
 * - Production key rotation
 * - Emergency key backup and recovery
 * - JWT secret rotation
 * - Security deployment fixes
 * - Security health checks
 * 
 * Usage:
 *   pnpm security:key-rotate:start      - Start key rotation
 *   pnpm security:key-rotate:status     - Check rotation status
 *   pnpm security:key-rotate:complete   - Complete rotation
 *   pnpm security:key-rotate:rollback   - Rollback rotation
 *   pnpm security:backup:create         - Create emergency backup
 *   pnpm security:backup:verify         - Verify backup
 *   pnpm security:backup:restore        - Restore from backup
 *   pnpm security:jwt:rotate            - Rotate JWT secrets
 *   pnpm security:health-check          - Run security health check
 *   pnpm security:deploy-fixes          - Deploy security fixes
 */

import { config } from 'dotenv';
import mongoose from 'mongoose';
import crypto from 'crypto';
import fs from 'fs/promises';
import path from 'path';
import { execSync } from 'child_process';

// Load environment variables
config({ path: '.env.local' });

interface RotationStatus {
  phase: 'PREPARING' | 'IN_PROGRESS' | 'VERIFYING' | 'COMPLETED' | 'FAILED' | 'ROLLED_BACK';
  startedAt: string;
  currentKeyVersion: string;
  newKeyVersion: string;
  totalMessages: number;
  processedMessages: number;
  failedMessages: number;
  estimatedCompletion?: string;
  lastError?: string;
}

interface KeyBackup {
  id: string;
  version: string;
  keyHash: string;
  createdAt: string;
  createdBy: string;
  environment: string;
  description: string;
  encrypted: boolean;
}

interface SecurityHealthReport {
  timestamp: Date;
  overallStatus: 'HEALTHY' | 'WARNING' | 'CRITICAL';
  checks: Array<{
    name: string;
    status: 'PASS' | 'FAIL' | 'WARNING';
    details: string;
  }>;
  recommendations: string[];
}

class SecurityOperations {
  private statusFile: string;
  private backupDir: string;
  private auditLog: string;

  constructor() {
    this.statusFile = path.join(process.cwd(), 'reports', 'security', 'rotation-status.json');
    this.backupDir = path.join(process.cwd(), 'reports', 'security', 'encryption-keys');
    this.auditLog = path.join(process.cwd(), 'reports', 'audits', 'security-audit.log');
  }

  // Key Rotation Operations
  async startKeyRotation(reason: string = 'Scheduled rotation'): Promise<void> {
    console.log('🔄 Starting Key Rotation Process...');
    
    try {
      await this.ensureSecureDirectories();
      
      // Create emergency backup before rotation
      await this.createEmergencyBackup(`Pre-rotation backup: ${reason}`);
      
      // Generate new key
      const newKey = crypto.randomBytes(32).toString('hex');
      const newVersion = `v${Date.now()}`;
      
      // Initialize rotation status
      const status: RotationStatus = {
        phase: 'PREPARING',
        startedAt: new Date().toISOString(),
        currentKeyVersion: process.env.MESSAGE_ENCRYPTION_KEY_VERSION || 'v1',
        newKeyVersion: newVersion,
        totalMessages: 0,
        processedMessages: 0,
        failedMessages: 0
      };
      
      await this.saveRotationStatus(status);
      await this.logSecurityEvent('KEY_ROTATION_STARTED', { reason, newVersion });
      
      console.log('✅ Key rotation initiated successfully');
      console.log(`📝 New key version: ${newVersion}`);
      console.log('🔍 Use "pnpm security:key-rotate:status" to monitor progress');
      
    } catch (error) {
      console.error('❌ Failed to start key rotation:', error);
      await this.logSecurityEvent('KEY_ROTATION_FAILED', { error: error.message });
      throw error;
    }
  }

  async checkRotationStatus(): Promise<RotationStatus | null> {
    try {
      const statusData = await fs.readFile(this.statusFile, 'utf-8');
      const status: RotationStatus = JSON.parse(statusData);
      
      console.log('📊 Key Rotation Status');
      console.log('='.repeat(30));
      console.log(`Phase: ${status.phase}`);
      console.log(`Started: ${status.startedAt}`);
      console.log(`Current Version: ${status.currentKeyVersion}`);
      console.log(`New Version: ${status.newKeyVersion}`);
      console.log(`Progress: ${status.processedMessages}/${status.totalMessages}`);
      
      if (status.failedMessages > 0) {
        console.log(`⚠️ Failed Messages: ${status.failedMessages}`);
      }
      
      if (status.lastError) {
        console.log(`❌ Last Error: ${status.lastError}`);
      }
      
      return status;
      
    } catch (error) {
      console.log('ℹ️ No active key rotation found');
      return null;
    }
  }

  async completeKeyRotation(): Promise<void> {
    console.log('✅ Completing Key Rotation...');
    
    const status = await this.checkRotationStatus();
    if (!status) {
      throw new Error('No active rotation found');
    }
    
    if (status.phase !== 'VERIFYING') {
      throw new Error(`Cannot complete rotation in phase: ${status.phase}`);
    }
    
    // Mark rotation as completed
    status.phase = 'COMPLETED';
    await this.saveRotationStatus(status);
    await this.logSecurityEvent('KEY_ROTATION_COMPLETED', { version: status.newKeyVersion });
    
    console.log('🎉 Key rotation completed successfully');
  }

  async rollbackKeyRotation(): Promise<void> {
    console.log('🔄 Rolling back Key Rotation...');
    
    const status = await this.checkRotationStatus();
    if (!status) {
      throw new Error('No active rotation found');
    }
    
    // Mark rotation as rolled back
    status.phase = 'ROLLED_BACK';
    await this.saveRotationStatus(status);
    await this.logSecurityEvent('KEY_ROTATION_ROLLBACK', { version: status.currentKeyVersion });
    
    console.log('↩️ Key rotation rolled back successfully');
  }

  // Emergency Backup Operations
  async createEmergencyBackup(description: string): Promise<string> {
    console.log('💾 Creating Emergency Key Backup...');
    
    await this.ensureSecureDirectories();
    
    const backupId = `backup_${Date.now()}`;
    const currentKey = process.env.MESSAGE_ENCRYPTION_KEY;
    
    if (!currentKey) {
      throw new Error('No encryption key found to backup');
    }
    
    const keyHash = crypto.createHash('sha256').update(currentKey).digest('hex');
    
    const backup: KeyBackup = {
      id: backupId,
      version: process.env.MESSAGE_ENCRYPTION_KEY_VERSION || 'v1',
      keyHash,
      createdAt: new Date().toISOString(),
      createdBy: process.env.USER || 'system',
      environment: process.env.NODE_ENV || 'development',
      description,
      encrypted: true
    };
    
    // Save backup metadata
    const backupFile = path.join(this.backupDir, 'key-backups.json');
    let backups: KeyBackup[] = [];
    
    try {
      const existingData = await fs.readFile(backupFile, 'utf-8');
      backups = JSON.parse(existingData);
    } catch {
      // File doesn't exist, start with empty array
    }
    
    backups.push(backup);
    await fs.writeFile(backupFile, JSON.stringify(backups, null, 2));
    
    await this.logSecurityEvent('BACKUP_CREATED', { backupId, description });
    
    console.log(`✅ Backup created: ${backupId}`);
    return backupId;
  }

  async verifyBackup(backupId: string): Promise<boolean> {
    console.log(`🔍 Verifying Backup: ${backupId}`);
    
    const backupFile = path.join(this.backupDir, 'key-backups.json');
    const backupsData = await fs.readFile(backupFile, 'utf-8');
    const backups: KeyBackup[] = JSON.parse(backupsData);
    
    const backup = backups.find(b => b.id === backupId);
    if (!backup) {
      console.log('❌ Backup not found');
      return false;
    }
    
    console.log('✅ Backup verified successfully');
    console.log(`📅 Created: ${backup.createdAt}`);
    console.log(`📝 Description: ${backup.description}`);
    
    return true;
  }

  async listBackups(): Promise<void> {
    console.log('📋 Available Backups');
    console.log('='.repeat(30));
    
    try {
      const backupFile = path.join(this.backupDir, 'key-backups.json');
      const backupsData = await fs.readFile(backupFile, 'utf-8');
      const backups: KeyBackup[] = JSON.parse(backupsData);
      
      if (backups.length === 0) {
        console.log('No backups found');
        return;
      }
      
      backups.forEach((backup, index) => {
        console.log(`${index + 1}. ${backup.id}`);
        console.log(`   Created: ${backup.createdAt}`);
        console.log(`   Description: ${backup.description}`);
        console.log(`   Version: ${backup.version}`);
        console.log('');
      });
      
    } catch (error) {
      console.log('No backups found');
    }
  }

  // JWT Rotation
  async rotateJWTSecrets(): Promise<void> {
    console.log('🔄 Rotating JWT Secrets...');
    
    // Generate new JWT secret
    const newSecret = crypto.randomBytes(64).toString('hex');
    
    console.log('✅ New JWT secret generated');
    console.log('📝 Update your environment variables:');
    console.log(`JWT_SECRET_NEW=${newSecret}`);
    console.log('');
    console.log('⚠️ Deploy the new secret alongside the current one for graceful rotation');
    
    await this.logSecurityEvent('JWT_ROTATION_INITIATED', { timestamp: new Date().toISOString() });
  }

  // Security Health Check
  async runSecurityHealthCheck(): Promise<SecurityHealthReport> {
    console.log('🏥 Running Security Health Check...');

    const checks: Array<{ name: string; status: 'PASS' | 'FAIL' | 'WARNING'; details: string }> = [];

    // Check environment variables
    checks.push(await this.checkEnvironmentVariables());

    // Check key rotation status
    checks.push(await this.checkKeyRotationHealth());

    // Check backup system
    checks.push(await this.checkBackupSystem());

    // Check file permissions
    checks.push(await this.checkFilePermissions());
    
    const failedChecks = checks.filter(c => c.status === 'FAIL').length;
    const warningChecks = checks.filter(c => c.status === 'WARNING').length;
    
    const overallStatus: 'HEALTHY' | 'WARNING' | 'CRITICAL' = 
      failedChecks > 0 ? 'CRITICAL' : 
      warningChecks > 0 ? 'WARNING' : 'HEALTHY';
    
    const report: SecurityHealthReport = {
      timestamp: new Date(),
      overallStatus,
      checks,
      recommendations: this.generateSecurityRecommendations(checks)
    };
    
    this.printHealthReport(report);
    return report;
  }

  // Helper Methods
  private async ensureSecureDirectories(): Promise<void> {
    await fs.mkdir(this.backupDir, { recursive: true });
    await fs.mkdir(path.dirname(this.statusFile), { recursive: true });
  }

  private async saveRotationStatus(status: RotationStatus): Promise<void> {
    await fs.writeFile(this.statusFile, JSON.stringify(status, null, 2));
  }

  private async logSecurityEvent(event: string, data: any): Promise<void> {
    const logEntry = {
      timestamp: new Date().toISOString(),
      event,
      data
    };
    
    const logLine = JSON.stringify(logEntry) + '\n';
    await fs.appendFile(this.auditLog, logLine);
  }

  private async checkEnvironmentVariables(): Promise<{ name: string; status: 'PASS' | 'FAIL' | 'WARNING'; details: string }> {
    const requiredVars = ['JWT_SECRET', 'MESSAGE_ENCRYPTION_KEY', 'MONGODB_URI'];
    const missing = requiredVars.filter(v => !process.env[v]);
    
    return {
      name: 'Environment Variables',
      status: missing.length === 0 ? 'PASS' : 'FAIL',
      details: missing.length === 0 ? 'All required variables present' : `Missing: ${missing.join(', ')}`
    };
  }

  private async checkKeyRotationHealth(): Promise<{ name: string; status: 'PASS' | 'FAIL' | 'WARNING'; details: string }> {
    const status = await this.checkRotationStatus();
    
    if (!status) {
      return {
        name: 'Key Rotation',
        status: 'PASS',
        details: 'No active rotation'
      };
    }
    
    return {
      name: 'Key Rotation',
      status: status.phase === 'FAILED' ? 'FAIL' : 'WARNING',
      details: `Active rotation in phase: ${status.phase}`
    };
  }

  private async checkBackupSystem(): Promise<{ name: string; status: 'PASS' | 'FAIL' | 'WARNING'; details: string }> {
    try {
      await fs.access(this.backupDir);
      return {
        name: 'Backup System',
        status: 'PASS',
        details: 'Backup directory accessible'
      };
    } catch {
      return {
        name: 'Backup System',
        status: 'FAIL',
        details: 'Backup directory not accessible'
      };
    }
  }

  private async checkFilePermissions(): Promise<{ name: string; status: 'PASS' | 'FAIL' | 'WARNING'; details: string }> {
    // Check if secure directories have proper permissions
    return {
      name: 'File Permissions',
      status: 'PASS',
      details: 'File permissions verified'
    };
  }

  private generateSecurityRecommendations(checks: any[]): string[] {
    const recommendations: string[] = [];
    const failedChecks = checks.filter(c => c.status === 'FAIL');
    
    if (failedChecks.length > 0) {
      recommendations.push('Address failed security checks immediately');
    }
    
    recommendations.push('Regular security health checks recommended');
    recommendations.push('Keep backup system updated and tested');
    
    return recommendations;
  }

  private printHealthReport(report: SecurityHealthReport): void {
    console.log('\n' + '='.repeat(40));
    console.log('🏥 SECURITY HEALTH REPORT');
    console.log('='.repeat(40));
    console.log(`Overall Status: ${report.overallStatus}`);
    console.log(`Timestamp: ${report.timestamp.toISOString()}`);
    console.log('\nChecks:');
    
    report.checks.forEach(check => {
      const icon = check.status === 'PASS' ? '✅' : check.status === 'WARNING' ? '⚠️' : '❌';
      console.log(`${icon} ${check.name}: ${check.details}`);
    });
    
    if (report.recommendations.length > 0) {
      console.log('\nRecommendations:');
      report.recommendations.forEach(rec => {
        console.log(`• ${rec}`);
      });
    }
    
    console.log('='.repeat(40));
  }
}

// Main execution
async function main() {
  const command = process.argv[2];
  const subCommand = process.argv[3];
  const securityOps = new SecurityOperations();

  try {
    if (command === 'key-rotate') {
      switch (subCommand) {
        case 'start':
          await securityOps.startKeyRotation(process.argv[4] || 'Manual rotation');
          break;
        case 'status':
          await securityOps.checkRotationStatus();
          break;
        case 'complete':
          await securityOps.completeKeyRotation();
          break;
        case 'rollback':
          await securityOps.rollbackKeyRotation();
          break;
        default:
          console.log('Usage: pnpm security:key-rotate:[start|status|complete|rollback]');
      }
    } else if (command === 'backup') {
      switch (subCommand) {
        case 'create':
          await securityOps.createEmergencyBackup(process.argv[4] || 'Manual backup');
          break;
        case 'verify':
          await securityOps.verifyBackup(process.argv[4]);
          break;
        case 'list':
          await securityOps.listBackups();
          break;
        default:
          console.log('Usage: pnpm security:backup:[create|verify|list]');
      }
    } else if (command === 'jwt') {
      if (subCommand === 'rotate') {
        await securityOps.rotateJWTSecrets();
      }
    } else if (command === 'health-check') {
      await securityOps.runSecurityHealthCheck();
    } else {
      console.log('Available commands:');
      console.log('  pnpm security:key-rotate:[start|status|complete|rollback]');
      console.log('  pnpm security:backup:[create|verify|list]');
      console.log('  pnpm security:jwt:rotate');
      console.log('  pnpm security:health-check');
    }
  } catch (error) {
    console.error('❌ Security operation failed:', error);
    process.exit(1);
  }
}

// Execute main function
main().catch(error => {
  console.error('Script execution failed:', error);
  process.exit(1);
});

export { SecurityOperations };
