// Centralized validation schemas using Zod
import { z } from 'zod';
import { VALIDATION_RULES } from './constants';

// Base schemas
export const emailSchema = z
  .email(VALIDATION_RULES.email.message)
  .min(1, "Email is required");

export const passwordSchema = z
  .string()
  .min(VALIDATION_RULES.password.minLength, VALIDATION_RULES.password.message)
  .regex(VALIDATION_RULES.password.pattern, VALIDATION_RULES.password.message);

// Auth schemas
export const loginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, "Password is required"),
  remember: z.boolean().optional().default(false)
});

export const signUpSchema = z.object({
  fullName: z.string().min(2, "Full name must be at least 2 characters"),
  email: emailSchema,
  phoneNumber: z.string().optional(),
  password: passwordSchema,
  confirmPassword: z.string().optional(),
  role: z.enum(['admin', 'investigator', 'whistleblower']).optional().default('whistleblower'),
  companyId: z.string().optional(), // For associating user with existing company
  companyName: z.string().optional(), // For creating new company
  agreeToTerms: z.boolean().refine(val => val === true, {
    message: "You must agree to the terms and conditions"
  }).optional(),
  recaptchaToken: z.string().optional() // reCAPTCHA token for whistleblower signups
}).refine(data => !data.confirmPassword || data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
});

// Report schemas
export const reportSchema = z.object({
  title: z
    .string()
    .min(VALIDATION_RULES.reportTitle.minLength, VALIDATION_RULES.reportTitle.message)
    .max(VALIDATION_RULES.reportTitle.maxLength, VALIDATION_RULES.reportTitle.message),
  description: z
    .string()
    .min(VALIDATION_RULES.reportDescription.minLength, VALIDATION_RULES.reportDescription.message)
    .max(VALIDATION_RULES.reportDescription.maxLength, VALIDATION_RULES.reportDescription.message),
  category: z.enum(["Financial", "Workplace Safety", "Ethics Violation", "Data Privacy", "Harassment", "Discrimination", "Other"]),
  priority: z.enum(["Low", "Medium", "High", "Critical"]),
  isAnonymous: z.boolean().default(false),
  incidentDate: z.date().optional(),
  location: z.string().optional(),
  evidence: z.array(z.string().regex(/\.pdf|\.docx|\.jpg|\.png/)).optional()
});

// User profile schema
export const userProfileSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  email: emailSchema,
  preferences: z.object({
    language: z.string().default("en"),
    notifications: z.object({
      email: z.boolean().default(true),
      push: z.boolean().default(true),
      sms: z.boolean().default(false)
    }),
    theme: z.enum(["light", "dark", "system"]).default("light")
  }).optional()
});

// Message schema
export const messageSchema = z.object({
  content: z.string().min(1, "Message content is required").max(5000, "Message too long"),
  conversationId: z.string().min(1, "Conversation ID is required"),
  attachments: z.array(z.object({
    fileName: z.string(),
    fileSize: z.number(),
    mimeType: z.string()
  })).optional()
});

// Contact form schema
export const contactFormSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: emailSchema,
  phone: z.string().optional().refine((phone) => {
    if (!phone) return true; // Optional field
    // Basic phone validation - allows various formats
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
  }, "Please enter a valid phone number"),
  company: z.string().optional(),
  subject: z.string().min(5, "Subject must be at least 5 characters"),
  message: z.string().min(20, "Message must be at least 20 characters"),
  department: z.enum(["sales", "technical", "customer", "media"]).optional()
});

// Newsletter subscription schema
export const newsletterSchema = z.object({
  email: emailSchema,
  preferences: z.object({
    productUpdates: z.boolean().default(true),
    securityAlerts: z.boolean().default(true),
    complianceNews: z.boolean().default(false)
  }).optional()
});

// Search and filter schemas
export const reportFiltersSchema = z.object({
  status: z.array(z.enum(["New", "Under Review", "Awaiting Response", "Resolved", "Closed"])).optional(),
  priority: z.array(z.enum(["Low", "Medium", "High", "Critical"])).optional(),
  category: z.array(z.enum(["Financial", "Workplace Safety", "Ethics Violation", "Data Privacy", "Harassment", "Discrimination", "Other"])).optional(),
  dateFrom: z.date().optional(),
  dateTo: z.date().optional(),
  searchTerm: z.string().optional(),
  sortBy: z.enum(["createdAt", "updatedAt", "priority", "status"]).default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
  limit: z.number().min(1).max(100).default(10),
  offset: z.number().min(0).default(0)
});

export const notificationFiltersSchema = z.object({
  status: z.array(z.enum(["read", "unread"])).optional(),
  type: z.array(z.enum(["system", "report_update", "message", "alert"])).optional(),
  priority: z.array(z.enum(["low", "medium", "high", "urgent"])).optional(),
  dateFrom: z.date().optional(),
  dateTo: z.date().optional(),
  limit: z.number().min(1).max(100).default(10),
  offset: z.number().min(0).default(0)
});

// File upload schema
export const fileUploadSchema = z.object({
  file: z
    .any()
    .refine(
      (file) => file.size <= 50 * 1024 * 1024, // 50MB
      "File size must be less than 50MB"
    )
    .refine(
      (file) => [
        "image/jpeg",
        "image/png", 
        "image/gif",
        "application/pdf",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/vnd.ms-excel",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      ].includes(file.type),
      "Invalid file type"
    )
});

// Export type inference helpers
export type LoginFormData = z.infer<typeof loginSchema>;
export type SignUpFormData = z.infer<typeof signUpSchema>;
export type ReportFormData = z.infer<typeof reportSchema>;
export type UserProfileData = z.infer<typeof userProfileSchema>;
export type MessageFormData = z.infer<typeof messageSchema>;
export type ContactFormData = z.infer<typeof contactFormSchema>;
export type NewsletterFormData = z.infer<typeof newsletterSchema>;
export type ReportFiltersData = z.infer<typeof reportFiltersSchema>;
export type NotificationFiltersData = z.infer<typeof notificationFiltersSchema>;