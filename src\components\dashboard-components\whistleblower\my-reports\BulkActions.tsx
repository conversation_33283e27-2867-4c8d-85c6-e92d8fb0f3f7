"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { LoadingSpinner } from "@/components/ui/skeleton-components";
import {
  ArrowDownToLine,
  Copy,
  Archive,
  Share2,
  Printer,
  Trash2} from "lucide-react";

interface BulkActionsProps {
  selectedReports: string[];
  bulkActionInProgress: boolean;
  onBulkExport: (format: 'csv' | 'pdf') => void;
  onBulkArchive: () => void;
  onBulkDelete: () => void;
  onDuplicate: () => void;
  onShare: () => void;
  onPrint: () => void;
}

export default function BulkActions({
  selectedReports,
  bulkActionInProgress,
  onBulkExport,
  onBulkArchive,
  onBulkDelete,
  onDuplicate,
  onShare,
  onPrint
}: BulkActionsProps) {
  return (
    <Card className="h-full w-full xl:w-2/3">
      <CardHeader className="py-0 flex items-center justify-between gap-2 sm:gap-0 px-4">
        <CardTitle className="p-0 text-sm sm:text-base text-[#242E2C]">Bulk Actions</CardTitle>
      </CardHeader>
      <Separator className="bg-[#F3F4F6]" />
      <CardContent className="flex flex-col gap-4 w-full p-4 sm:p-6">
        <div className="grid grid-cols-2 sm:grid-cols-3 gap-3 sm:gap-4 w-full">
          <Button
            variant="outline"
            className="flex-1 h-10 sm:h-12 text-center justify-center text-xs sm:text-sm"
            onClick={() => onBulkExport('csv')}
            disabled={selectedReports.length === 0 || bulkActionInProgress}
            title={selectedReports.length === 0 ? "Select reports to export" : "Export selected reports as CSV"}
          >
            <ArrowDownToLine className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
            <span className="hidden sm:inline">Export Selected</span>
            <span className="sm:hidden">Export</span>
          </Button>
          <Button
            variant="outline"
            className="flex-1 h-10 sm:h-12 text-center justify-center text-xs sm:text-sm"
            disabled={selectedReports.length !== 1 || bulkActionInProgress}
            title={selectedReports.length !== 1 ? "Select exactly one report to duplicate" : "Create a copy of the selected report"}
            onClick={onDuplicate}
          >
            <Copy className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
            <span className="hidden sm:inline">Duplicate Report</span>
            <span className="sm:hidden">Duplicate</span>
          </Button>
          <Button
            variant="outline"
            className="flex-1 h-10 sm:h-12 text-center justify-center text-xs sm:text-sm col-span-2 sm:col-span-1"
            onClick={onBulkArchive}
            disabled={selectedReports.length === 0 || bulkActionInProgress}
            title={selectedReports.length === 0 ? "Select reports to archive" : "Archive selected reports"}
          >
            <Archive className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
            <span className="hidden sm:inline">Archive Reports</span>
            <span className="sm:hidden">Archive</span>
          </Button>
        </div>
        <div className="grid grid-cols-2 sm:grid-cols-3 gap-3 sm:gap-4 w-full">
          <Button
            variant="outline"
            className="flex-1 h-10 sm:h-12 text-center justify-center text-xs sm:text-sm"
            disabled={selectedReports.length === 0 || bulkActionInProgress}
            title={selectedReports.length === 0 ? "Select reports to share" : "Share selected reports"}
            onClick={onShare}
          >
            <Share2 className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
            <span className="hidden sm:inline">Share Reports</span>
            <span className="sm:hidden">Share</span>
          </Button>
          <Button
            variant="outline"
            className="flex-1 h-10 sm:h-12 text-center justify-center text-xs sm:text-sm"
            onClick={onPrint}
            disabled={selectedReports.length === 0 || bulkActionInProgress}
            title={selectedReports.length === 0 ? "Select reports to print" : "Print selected reports"}
          >
            <Printer className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
            <span className="hidden sm:inline">Print Reports</span>
            <span className="sm:hidden">Print</span>
          </Button>
          <Button
            variant="outline"
            className="flex-1 h-10 sm:h-12 text-center justify-center text-xs sm:text-sm border-[#FCA5A5] text-[#B91C1C] hover:bg-red-50 col-span-2 sm:col-span-1"
            onClick={onBulkDelete}
            disabled={selectedReports.length === 0 || bulkActionInProgress}
            title={selectedReports.length === 0 ? "Select reports to delete" : "Delete selected reports permanently"}
          >
            <Trash2 className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
            <span className="hidden sm:inline">Delete Report</span>
            <span className="sm:hidden">Delete</span>
          </Button>
        </div>
        {selectedReports.length > 0 && (
          <div className="text-sm text-gray-600 text-center">
            {selectedReports.length} report(s) selected
          </div>
        )}
        {bulkActionInProgress && (
          <div className="text-sm text-blue-600 text-center flex items-center justify-center gap-2">
            <LoadingSpinner size="sm" color="primary" text="Processing action..." />
          </div>
        )}
      </CardContent>
    </Card>
  );
}