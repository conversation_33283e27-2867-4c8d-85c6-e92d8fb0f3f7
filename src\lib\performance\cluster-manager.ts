/**
 * Node.js Cluster Manager for Production Performance
 * 
 * This module enables clustering to utilize all CPU cores for better performance
 * under high load conditions.
 */

import cluster from 'cluster';
import os from 'os';

interface ClusterConfig {
  enabled: boolean;
  workers?: number;
  respawnDelay?: number;
  maxRestarts?: number;
}

class ClusterManager {
  private config: ClusterConfig;
  private workerRestarts: Map<number, number> = new Map();

  constructor(config?: Partial<ClusterConfig>) {
    this.config = {
      enabled: process.env.CLUSTER_MODE === 'true',
      workers: parseInt(process.env.CLUSTER_WORKERS || '0') || os.cpus().length,
      respawnDelay: parseInt(process.env.CLUSTER_RESPAWN_DELAY || '1000'),
      maxRestarts: parseInt(process.env.CLUSTER_MAX_RESTARTS || '5'),
      ...config
    };
  }

  /**
   * Initialize clustering if enabled
   */
  initialize(): boolean {
    if (!this.config.enabled) {
      console.log('🔧 Clustering disabled - running in single process mode');
      return false;
    }

    if (cluster.isPrimary) {
      this.setupPrimary();
      return true;
    } else {
      this.setupWorker();
      return false;
    }
  }

  /**
   * Setup primary process (cluster manager)
   */
  private setupPrimary(): void {
    const numWorkers = this.config.workers!;
    
    console.log(`🚀 Starting cluster with ${numWorkers} workers (CPU cores: ${os.cpus().length})`);
    console.log(`📊 Memory: ${Math.round(os.totalmem() / 1024 / 1024 / 1024)}GB total, ${Math.round(os.freemem() / 1024 / 1024 / 1024)}GB free`);

    // Fork workers
    for (let i = 0; i < numWorkers; i++) {
      this.forkWorker();
    }

    // Handle worker events
    cluster.on('exit', (worker, code, signal) => {
      console.log(`⚠️ Worker ${worker.process.pid} died (${signal || code})`);
      this.handleWorkerExit(worker);
    });

    cluster.on('online', (worker) => {
      console.log(`✅ Worker ${worker.process.pid} is online`);
    });

    cluster.on('disconnect', (worker) => {
      console.log(`🔌 Worker ${worker.process.pid} disconnected`);
    });

    // Graceful shutdown handling
    process.on('SIGTERM', () => this.gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => this.gracefulShutdown('SIGINT'));

    // Log cluster status
    this.logClusterStatus();
  }

  /**
   * Setup worker process
   */
  private setupWorker(): void {
    console.log(`👷 Worker ${process.pid} started`);

    // Handle graceful shutdown
    process.on('SIGTERM', () => {
      console.log(`👷 Worker ${process.pid} received SIGTERM, shutting down gracefully`);
      process.exit(0);
    });

    process.on('SIGINT', () => {
      console.log(`👷 Worker ${process.pid} received SIGINT, shutting down gracefully`);
      process.exit(0);
    });
  }

  /**
   * Fork a new worker
   */
  private forkWorker(): void {
    const worker = cluster.fork();
    this.workerRestarts.set(worker.id, 0);
  }

  /**
   * Handle worker exit and respawn if needed
   */
  private handleWorkerExit(worker: any): void {
    const restarts = this.workerRestarts.get(worker.id) || 0;
    
    if (restarts < this.config.maxRestarts!) {
      console.log(`🔄 Respawning worker ${worker.id} (restart ${restarts + 1}/${this.config.maxRestarts})`);
      
      setTimeout(() => {
        const newWorker = cluster.fork();
        this.workerRestarts.set(newWorker.id, restarts + 1);
      }, this.config.respawnDelay);
    } else {
      console.log(`❌ Worker ${worker.id} exceeded max restarts (${this.config.maxRestarts}), not respawning`);
      this.workerRestarts.delete(worker.id);
    }
  }

  /**
   * Graceful shutdown of all workers
   */
  private gracefulShutdown(signal: string): void {
    console.log(`🛑 Primary received ${signal}, shutting down cluster gracefully`);

    // Disconnect all workers
    for (const id in cluster.workers) {
      const worker = cluster.workers[id];
      if (worker) {
        worker.disconnect();
      }
    }

    // Force exit after timeout
    setTimeout(() => {
      console.log('⏰ Force exiting cluster after timeout');
      process.exit(0);
    }, 10000);

    // Exit when all workers are disconnected
    cluster.on('disconnect', () => {
      if (Object.keys(cluster.workers || {}).length === 0) {
        console.log('✅ All workers disconnected, exiting primary');
        process.exit(0);
      }
    });
  }

  /**
   * Log cluster status information
   */
  private logClusterStatus(): void {
    setInterval(() => {
      const workers = Object.keys(cluster.workers || {}).length;
      const memory = process.memoryUsage();
      const memoryMB = Math.round(memory.heapUsed / 1024 / 1024);
      
      console.log(`📊 Cluster Status: ${workers} workers active, Primary memory: ${memoryMB}MB`);
    }, 60000); // Log every minute
  }

  /**
   * Get cluster information
   */
  getClusterInfo(): {
    enabled: boolean;
    isPrimary: boolean;
    workerId?: number;
    totalWorkers: number;
    cpuCores: number;
  } {
    return {
      enabled: this.config.enabled,
      isPrimary: cluster.isPrimary,
      workerId: cluster.worker?.id,
      totalWorkers: Object.keys(cluster.workers || {}).length,
      cpuCores: os.cpus().length
    };
  }

  /**
   * Health check for cluster
   */
  healthCheck(): {
    healthy: boolean;
    workers: number;
    expectedWorkers: number;
    issues: string[];
  } {
    const issues: string[] = [];
    const workers = Object.keys(cluster.workers || {}).length;
    const expectedWorkers = this.config.enabled ? this.config.workers! : 1;

    if (this.config.enabled && cluster.isPrimary) {
      if (workers < expectedWorkers * 0.5) {
        issues.push(`Low worker count: ${workers}/${expectedWorkers}`);
      }

      // Check for dead workers
      for (const id in cluster.workers) {
        const worker = cluster.workers[id];
        if (worker && worker.isDead()) {
          issues.push(`Dead worker detected: ${id}`);
        }
      }
    }

    return {
      healthy: issues.length === 0,
      workers,
      expectedWorkers,
      issues
    };
  }
}

// Export singleton instance
export const clusterManager = new ClusterManager();

// Helper function to initialize clustering
export function initializeClustering(): boolean {
  return clusterManager.initialize();
}

// Helper function to check if this is a worker process
export function isWorkerProcess(): boolean {
  return cluster.isWorker;
}

// Helper function to check if this is the primary process
export function isPrimaryProcess(): boolean {
  return cluster.isPrimary;
}

export default clusterManager;
