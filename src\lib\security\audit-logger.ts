/**
 * Security audit logging system
 */

import { NextRequest } from 'next/server';
import connectDB from '@/lib/db/mongodb';
import mongoose from 'mongoose';

// Audit event types
export enum AuditEventType {
  LOGIN_SUCCESS = 'LOGIN_SUCCESS',
  LOGIN_FAILED = 'LOGIN_FAILED',
  LOGIN_BLOCKED = 'LOGIN_BLOCKED',
  LOGOUT = 'LOGOUT',
  PASSWORD_CHANGE = 'PASSWORD_CHANGE',
  ACCOUNT_LOCKED = 'ACCOUNT_LOCKED',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  DATA_ACCESS = 'DATA_ACCESS',
  DATA_EXPORT = 'DATA_EXPORT',
  DATA_DELETE = 'DATA_DELETE',
  SUSPICIOUS_ACTIVITY = 'SUSPICIOUS_ACTIVITY',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  CSRF_ATTEMPT = 'CSRF_ATTEMPT',
  XSS_ATTEMPT = 'XSS_ATTEMPT',
  SQL_INJECTION_ATTEMPT = 'SQL_INJECTION_ATTEMPT'
}

export enum AuditSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

export interface AuditEvent {
  eventType: AuditEventType;
  severity: AuditSeverity;
  userId?: string;
  userEmail?: string;
  userRole?: string;
  ipAddress?: string;
  userAgent?: string;
  resource?: string;
  details?: Record<string, unknown>;
  timestamp: Date;
  sessionId?: string;
}

interface AuditLogDocument extends AuditEvent {
  _id: string;
  createdAt: Date;
  updatedAt: Date;
}

// Audit log schema
const auditLogSchema = new mongoose.Schema({
  eventType: { type: String, required: true, enum: Object.values(AuditEventType) },
  severity: { type: String, required: true, enum: Object.values(AuditSeverity) },
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  userEmail: String,
  userRole: String,
  ipAddress: String,
  userAgent: String,
  resource: String,
  details: mongoose.Schema.Types.Mixed,
  timestamp: { type: Date, default: Date.now },
  sessionId: String
}, {
  collection: 'audit_logs',
  timestamps: true
});

// Create indexes for performance
auditLogSchema.index({ eventType: 1, timestamp: -1 });
auditLogSchema.index({ userId: 1, timestamp: -1 });
auditLogSchema.index({ ipAddress: 1, timestamp: -1 });
auditLogSchema.index({ severity: 1, timestamp: -1 });

const AuditLog = mongoose.models.AuditLog || mongoose.model<AuditLogDocument>('AuditLog', auditLogSchema);

/**
 * Extract client information from request
 */
function extractClientInfo(request: NextRequest): { ipAddress: string; userAgent: string } {
  const forwardedFor = request.headers.get('x-forwarded-for');
  const realIp = request.headers.get('x-real-ip');
  const ipAddress = forwardedFor?.split(',')[0]?.trim() || realIp || 'unknown';
  const userAgent = request.headers.get('user-agent') || 'unknown';

  return { ipAddress, userAgent };
}

/**
 * Log security audit event
 */
export async function logAuditEvent(
  eventType: AuditEventType,
  severity: AuditSeverity,
  options: {
    request?: NextRequest;
    userId?: string;
    userEmail?: string;
    userRole?: string;
    resource?: string;
    details?: Record<string, unknown>;
    sessionId?: string;
  } = {}
): Promise<void> {
  try {
    await connectDB();

    let ipAddress = 'unknown';
    let userAgent = 'unknown';

    if (options.request) {
      const clientInfo = extractClientInfo(options.request);
      ipAddress = clientInfo.ipAddress;
      userAgent = clientInfo.userAgent;
    }

    const auditEvent: AuditEvent = {
      eventType,
      severity,
      userId: options.userId,
      userEmail: options.userEmail,
      userRole: options.userRole,
      ipAddress,
      userAgent,
      resource: options.resource,
      details: options.details,
      timestamp: new Date(),
      sessionId: options.sessionId
    };

    // Note: This audit system is separate from the main AuditLog model
    // For now, just log to console to avoid validation errors
    console.log('AUDIT EVENT:', {
      eventType,
      severity,
      userId: options.userId,
      userEmail: options.userEmail,
      ipAddress,
      userAgent,
      resource: options.resource,
      details: options.details,
      timestamp: new Date()
    });

    // Log critical events to console for immediate attention
    if (severity === AuditSeverity.CRITICAL) {
      console.error('CRITICAL SECURITY EVENT:', {
        eventType,
        userId: options.userId,
        userEmail: options.userEmail,
        ipAddress,
        timestamp: auditEvent.timestamp
      });
    }
  } catch (error) {
    // Don't let audit logging failures break the application
    console.error('Failed to log audit event:', error);
  }
}

/**
 * Log authentication events
 */
export class AuthAuditLogger {
  static async logLoginSuccess(request: NextRequest, userId: string, userEmail: string, userRole: string) {
    await logAuditEvent(AuditEventType.LOGIN_SUCCESS, AuditSeverity.LOW, {
      request,
      userId,
      userEmail,
      userRole,
      details: { loginMethod: 'password' }
    });
  }

  static async logLoginFailed(request: NextRequest, email: string, reason: string) {
    await logAuditEvent(AuditEventType.LOGIN_FAILED, AuditSeverity.MEDIUM, {
      request,
      userEmail: email,
      details: { reason, attemptedEmail: email }
    });
  }

  static async logLoginBlocked(request: NextRequest, email: string, reason: string) {
    await logAuditEvent(AuditEventType.LOGIN_BLOCKED, AuditSeverity.HIGH, {
      request,
      userEmail: email,
      details: { reason, blockedEmail: email }
    });
  }

  static async logLogout(request: NextRequest, userId: string, userEmail: string) {
    await logAuditEvent(AuditEventType.LOGOUT, AuditSeverity.LOW, {
      request,
      userId,
      userEmail
    });
  }
}

/**
 * Log security violations
 */
export class SecurityAuditLogger {
  static async logRateLimitExceeded(request: NextRequest, limit: number, window: number) {
    await logAuditEvent(AuditEventType.RATE_LIMIT_EXCEEDED, AuditSeverity.MEDIUM, {
      request,
      details: { limit, windowMs: window }
    });
  }

  static async logCSRFAttempt(request: NextRequest) {
    await logAuditEvent(AuditEventType.CSRF_ATTEMPT, AuditSeverity.HIGH, {
      request,
      details: {
        origin: request.headers.get('origin'),
        referer: request.headers.get('referer'),
        host: request.headers.get('host')
      }
    });
  }

  static async logSuspiciousActivity(request: NextRequest, activity: string, details?: Record<string, unknown>) {
    await logAuditEvent(AuditEventType.SUSPICIOUS_ACTIVITY, AuditSeverity.HIGH, {
      request,
      details: { activity, ...details }
    });
  }

  static async logPermissionDenied(request: NextRequest, userId: string, resource: string, requiredRole: string) {
    await logAuditEvent(AuditEventType.PERMISSION_DENIED, AuditSeverity.MEDIUM, {
      request,
      userId,
      resource,
      details: { requiredRole, attemptedResource: resource }
    });
  }
}

/**
 * Log data access events
 */
export class DataAuditLogger {
  static async logDataAccess(request: NextRequest, userId: string, resource: string, action: string) {
    await logAuditEvent(AuditEventType.DATA_ACCESS, AuditSeverity.LOW, {
      request,
      userId,
      resource,
      details: { action }
    });
  }

  static async logDataExport(request: NextRequest, userId: string, dataType: string, recordCount?: number) {
    await logAuditEvent(AuditEventType.DATA_EXPORT, AuditSeverity.MEDIUM, {
      request,
      userId,
      details: { dataType, recordCount }
    });
  }

  static async logDataDelete(request: NextRequest, userId: string, resource: string, recordId: string) {
    await logAuditEvent(AuditEventType.DATA_DELETE, AuditSeverity.HIGH, {
      request,
      userId,
      resource,
      details: { deletedRecordId: recordId }
    });
  }
}

/**
 * Get audit logs with filtering
 */
export async function getAuditLogs(filters: {
  eventType?: AuditEventType;
  severity?: AuditSeverity;
  userId?: string;
  ipAddress?: string;
  startDate?: Date;
  endDate?: Date;
  limit?: number;
  offset?: number;
}) {
  await connectDB();

  const query: Record<string, unknown> = {};

  if (filters.eventType) query.eventType = filters.eventType;
  if (filters.severity) query.severity = filters.severity;
  if (filters.userId) query.userId = filters.userId;
  if (filters.ipAddress) query.ipAddress = filters.ipAddress;

  if (filters.startDate || filters.endDate) {
    const timestampQuery: Record<string, Date> = {};
    if (filters.startDate) timestampQuery.$gte = filters.startDate;
    if (filters.endDate) timestampQuery.$lte = filters.endDate;
    query.timestamp = timestampQuery;
  }

  return await (AuditLog as mongoose.Model<AuditLogDocument>).find(query)
    .sort({ timestamp: -1 })
    .limit(filters.limit || 100)
    .skip(filters.offset || 0)
    .lean();
}
