'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { AlertTriangle, Shield, Users, Activity, LogOut, Trash2 } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';

interface Session {
  tokenId: string;
  userAgent: string;
  ipAddress: string;
  createdAt: string;
  lastActivity: string;
  isCurrent: boolean;
}

interface SecurityEvent {
  _id: string;
  eventType: string;
  severity: string;
  timestamp: string;
  ipAddress?: string;
  userAgent?: string;
  details?: Record<string, unknown>;
}

export function SecurityDashboard() {
  const [sessions, setSessions] = useState<Session[]>([]);
  const [securityEvents, setSecurityEvents] = useState<SecurityEvent[]>([]);
  const [loading, setLoading] = useState(true);
  // Using toast function directly from import

  useEffect(() => {
    loadSecurityData();
  }, []);

  const loadSecurityData = async () => {
    try {
      setLoading(true);
      
      // Load active sessions
      const sessionsResponse = await fetch('/api/auth/sessions', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      });
      
      if (sessionsResponse.ok) {
        const sessionsData = await sessionsResponse.json();
        setSessions(sessionsData.sessions || []);
      }

      // Load recent security events (if user has admin access)
      const eventsResponse = await fetch('/api/security/events', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      });
      
      if (eventsResponse.ok) {
        const eventsData = await eventsResponse.json();
        setSecurityEvents(eventsData.events || []);
      }
      
    } catch (error) {
      console.error('Failed to load security data:', error);
    } finally {
      setLoading(false);
    }
  };

  const revokeSession = async (tokenId: string) => {
    try {
      const response = await fetch('/api/auth/sessions', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify({
          action: 'revoke_session',
          tokenId
        })
      });

      if (response.ok) {
        toast({
          title: 'Session Revoked',
          description: 'The session has been successfully revoked.',
        });
        loadSecurityData(); // Reload data
      } else {
        throw new Error('Failed to revoke session');
      }
    } catch (error) {
      console.error('Error revoking session:', error);
      toast({
        title: 'Error',
        description: 'Failed to revoke session. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const revokeAllSessions = async () => {
    try {
      const response = await fetch('/api/auth/sessions', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify({
          action: 'revoke_all'
        })
      });

      if (response.ok) {
        toast({
          title: 'All Sessions Revoked',
          description: 'All sessions have been revoked. You will be logged out.',
        });
        
        // Redirect to login after a short delay
        setTimeout(() => {
          localStorage.removeItem('auth_token');
          window.location.href = '/login';
        }, 2000);
      } else {
        throw new Error('Failed to revoke all sessions');
      }
    } catch (error) {
      console.error('Error revoking all sessions:', error);
      toast({
        title: 'Error',
        description: 'Failed to revoke all sessions. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical': return 'destructive';
      case 'high': return 'destructive';
      case 'medium': return 'secondary';
      case 'low': return 'outline';
      default: return 'outline';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Security Dashboard</h2>
          <p className="text-muted-foreground">
            Manage your account security and monitor activity
          </p>
        </div>
        <Button 
          variant="destructive" 
          onClick={revokeAllSessions}
          className="flex items-center gap-2"
        >
          <LogOut className="h-4 w-4" />
          Revoke All Sessions
        </Button>
      </div>

      <Tabs defaultValue="sessions" className="space-y-4">
        <TabsList>
          <TabsTrigger value="sessions" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Active Sessions
          </TabsTrigger>
          <TabsTrigger value="events" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Security Events
          </TabsTrigger>
        </TabsList>

        <TabsContent value="sessions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Active Sessions ({sessions.length})
              </CardTitle>
              <CardDescription>
                Manage your active login sessions across different devices and browsers
              </CardDescription>
            </CardHeader>
            <CardContent>
              {sessions.length === 0 ? (
                <p className="text-muted-foreground text-center py-4">
                  No active sessions found
                </p>
              ) : (
                <div className="space-y-4">
                  {sessions.map((session) => (
                    <div
                      key={session.tokenId}
                      className="flex items-center justify-between p-4 border rounded-lg"
                    >
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <p className="font-medium">
                            {session.userAgent.includes('Chrome') ? 'Chrome' :
                             session.userAgent.includes('Firefox') ? 'Firefox' :
                             session.userAgent.includes('Safari') ? 'Safari' : 'Unknown Browser'}
                          </p>
                          {session.isCurrent && (
                            <Badge variant="secondary">Current Session</Badge>
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground">
                          IP: {session.ipAddress}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          Created: {formatDate(session.createdAt)}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          Last Activity: {formatDate(session.lastActivity)}
                        </p>
                      </div>
                      {!session.isCurrent && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => revokeSession(session.tokenId)}
                          className="flex items-center gap-2"
                        >
                          <Trash2 className="h-4 w-4" />
                          Revoke
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="events" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Recent Security Events
              </CardTitle>
              <CardDescription>
                Monitor security-related activities on your account
              </CardDescription>
            </CardHeader>
            <CardContent>
              {securityEvents.length === 0 ? (
                <p className="text-muted-foreground text-center py-4">
                  No security events found
                </p>
              ) : (
                <div className="space-y-4">
                  {securityEvents.map((event) => (
                    <div
                      key={event._id}
                      className="flex items-start justify-between p-4 border rounded-lg"
                    >
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <p className="font-medium">{event.eventType.replace(/_/g, ' ')}</p>
                          <Badge variant={getSeverityColor(event.severity)}>
                            {event.severity}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {formatDate(event.timestamp)}
                        </p>
                        {event.ipAddress && (
                          <p className="text-sm text-muted-foreground">
                            IP: {event.ipAddress}
                          </p>
                        )}
                        {event.details && (
                          <p className="text-sm text-muted-foreground">
                            {JSON.stringify(event.details, null, 2)}
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
