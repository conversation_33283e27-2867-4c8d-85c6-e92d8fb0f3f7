import { NextRequest, NextResponse } from 'next/server';
import { GDPRService } from '@/lib/compliance/gdpr';

export async function POST(request: NextRequest) {
  try {
    const { userId } = await request.json();
    
    if (!userId) {
      return NextResponse.json({ error: 'User ID required' }, { status: 400 });
    }

    const exportData = await GDPRService.exportUserData(userId);
    
    return NextResponse.json({
      success: true,
      data: exportData
    });
  } catch {
    return NextResponse.json({ error: 'Export failed' }, { status: 500 });
  }
}