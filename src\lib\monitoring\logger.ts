/**
 * Production Logger Configuration
 * 
 * This module provides structured logging with appropriate levels
 * for development and production environments.
 */

type LogLevel = 'error' | 'warn' | 'info' | 'debug';

interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  context?: string;
  metadata?: any;
  pid?: number;
  workerId?: number;
}

class ProductionLogger {
  private logLevel: LogLevel;
  private isDevelopment: boolean;
  private workerId?: number;

  constructor() {
    this.logLevel = (process.env.LOG_LEVEL as LogLevel) || 'info';
    this.isDevelopment = process.env.NODE_ENV !== 'production';
    
    // Get worker ID if running in cluster
    if (process.env.CLUSTER_MODE === 'true' && (global as any).cluster?.worker) {
      this.workerId = (global as any).cluster.worker.id;
    }
  }

  /**
   * Check if a log level should be output
   */
  private shouldLog(level: LogLevel): boolean {
    const levels: Record<LogLevel, number> = {
      error: 0,
      warn: 1,
      info: 2,
      debug: 3
    };

    return levels[level] <= levels[this.logLevel];
  }

  /**
   * Format log entry
   */
  private formatLog(level: LogLevel, message: string, context?: string, metadata?: any): LogEntry {
    return {
      timestamp: new Date().toISOString(),
      level,
      message,
      context,
      metadata,
      pid: process.pid,
      workerId: this.workerId
    };
  }

  /**
   * Output log entry
   */
  private output(logEntry: LogEntry): void {
    if (this.isDevelopment) {
      // Development: Pretty formatted logs
      const timestamp = logEntry.timestamp.split('T')[1].split('.')[0];
      const levelIcon = {
        error: '❌',
        warn: '⚠️',
        info: 'ℹ️',
        debug: '🔍'
      }[logEntry.level];

      const workerInfo = logEntry.workerId ? ` [W${logEntry.workerId}]` : '';
      const contextInfo = logEntry.context ? ` [${logEntry.context}]` : '';
      
      console.log(`${levelIcon} ${timestamp}${workerInfo}${contextInfo} ${logEntry.message}`);
      
      if (logEntry.metadata) {
        console.log('   Metadata:', logEntry.metadata);
      }
    } else {
      // Production: JSON structured logs
      console.log(JSON.stringify(logEntry));
    }
  }

  /**
   * Log error message
   */
  error(message: string, context?: string, metadata?: any): void {
    if (this.shouldLog('error')) {
      this.output(this.formatLog('error', message, context, metadata));
    }
  }

  /**
   * Log warning message
   */
  warn(message: string, context?: string, metadata?: any): void {
    if (this.shouldLog('warn')) {
      this.output(this.formatLog('warn', message, context, metadata));
    }
  }

  /**
   * Log info message
   */
  info(message: string, context?: string, metadata?: any): void {
    if (this.shouldLog('info')) {
      this.output(this.formatLog('info', message, context, metadata));
    }
  }

  /**
   * Log debug message
   */
  debug(message: string, context?: string, metadata?: any): void {
    if (this.shouldLog('debug')) {
      this.output(this.formatLog('debug', message, context, metadata));
    }
  }

  /**
   * Log security event
   */
  security(message: string, metadata?: any): void {
    this.error(`SECURITY: ${message}`, 'SECURITY', metadata);
  }

  /**
   * Log performance metric
   */
  performance(message: string, duration: number, metadata?: any): void {
    this.info(`PERFORMANCE: ${message} (${duration}ms)`, 'PERFORMANCE', { duration, ...metadata });
  }

  /**
   * Log database operation
   */
  database(message: string, operation: string, metadata?: any): void {
    this.debug(`DATABASE: ${message}`, 'DATABASE', { operation, ...metadata });
  }

  /**
   * Log authentication event
   */
  auth(message: string, userId?: string, metadata?: any): void {
    this.info(`AUTH: ${message}`, 'AUTH', { userId, ...metadata });
  }

  /**
   * Log API request
   */
  api(method: string, path: string, statusCode: number, duration: number, userId?: string): void {
    const level = statusCode >= 500 ? 'error' : statusCode >= 400 ? 'warn' : 'info';
    this[level](`API: ${method} ${path} ${statusCode}`, 'API', {
      method,
      path,
      statusCode,
      duration,
      userId
    });
  }

  /**
   * Get current log level
   */
  getLogLevel(): LogLevel {
    return this.logLevel;
  }

  /**
   * Set log level
   */
  setLogLevel(level: LogLevel): void {
    this.logLevel = level;
    this.info(`Log level changed to: ${level}`, 'LOGGER');
  }

  /**
   * Health check for logger
   */
  healthCheck(): { healthy: boolean; logLevel: LogLevel; isDevelopment: boolean } {
    return {
      healthy: true,
      logLevel: this.logLevel,
      isDevelopment: this.isDevelopment
    };
  }
}

// Export singleton instance
export const logger = new ProductionLogger();

// Convenience exports
export const log = logger;
export default logger;

// Helper functions for common logging patterns
export function logError(error: Error, context?: string, metadata?: any): void {
  logger.error(error.message, context, {
    stack: error.stack,
    name: error.name,
    ...metadata
  });
}

export function logPerformance<T>(
  operation: string,
  fn: () => T | Promise<T>,
  context?: string
): T | Promise<T> {
  const start = Date.now();
  
  try {
    const result = fn();
    
    if (result instanceof Promise) {
      return result.then(
        (value) => {
          logger.performance(`${operation} completed`, Date.now() - start, { context });
          return value;
        },
        (error) => {
          logger.performance(`${operation} failed`, Date.now() - start, { context, error: error.message });
          throw error;
        }
      );
    } else {
      logger.performance(`${operation} completed`, Date.now() - start, { context });
      return result;
    }
  } catch (error) {
    logger.performance(`${operation} failed`, Date.now() - start, { context, error: error.message });
    throw error;
  }
}

// Initialize logger
logger.info('Production logger initialized', 'LOGGER', {
  logLevel: logger.getLogLevel(),
  isDevelopment: process.env.NODE_ENV !== 'production',
  pid: process.pid
});
