import { NextResponse, NextRequest } from 'next/server';
import { DataService } from '@/lib/db/dataService';
import connectDB from '@/lib/db/mongodb';

export const runtime = 'nodejs';

export async function PUT(request: NextRequest, { params }: { params: Promise<{ id: string; action: string }> }) {
  let action = 'unknown';
  try {
    await connectDB();
    
    const resolvedParams = await params;
    const { id, action: paramAction } = resolvedParams;
    action = paramAction;
    
    if (!['delete', 'archive'].includes(action)) {
      return NextResponse.json(
        { success: false, error: 'Invalid action' },
        { status: 400 }
      );
    }
    
    // For delete, we'll soft delete by updating status
    // For archive, we'll update status to archived
    const newStatus = action === 'delete' ? 'deleted' : 'archived';
    
    const updatedConversation = await DataService.updateConversationStatus(id, newStatus);
    
    if (!updatedConversation) {
      return NextResponse.json(
        { success: false, error: 'Conversation not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: updatedConversation
    });
  } catch (error) {
    console.error(`Conversation ${action} API error:`, error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}